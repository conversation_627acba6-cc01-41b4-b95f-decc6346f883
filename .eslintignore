# Dependencies
node_modules/

# Build outputs
dist/
unpackage/

# IDE files
.vscode/
.idea/

# Environment files
.env
.env.local
.env.*.local

# Log files
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# Temporary folders
tmp/
temp/

# Static assets that don't need linting
static/
public/

# Configuration files that are auto-generated
auto-import.d.ts
components.d.ts

# Package lock files
package-lock.json
yarn.lock
pnpm-lock.yaml