/**
 * MongoDB数据库配置文件
 * 用于存储连接信息和数据库设置
 */

module.exports = {
  // MongoDB连接URI
  uri: process.env.MONGODB_URI || 'mongodb+srv://qu18354531302:<EMAIL>/',
  
  // 数据库名称
  dbName: 'wardrobe',
  
  // 连接选项
  options: {
    useNewUrlParser: true,
    useUnifiedTopology: true,
    // 连接池大小
    maxPoolSize: 10,
    // 连接超时时间（毫秒）
    connectTimeoutMS: 10000,
    // 套接字超时时间（毫秒）
    socketTimeoutMS: 45000,
  },
  
  // 各集合名称
  collections: {
    clothing: 'clothing',
    outfits: 'outfits',
    users: 'users',
    collections: 'collections',
  }
}; 