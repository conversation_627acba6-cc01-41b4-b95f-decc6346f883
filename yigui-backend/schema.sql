-- <PERSON><PERSON><PERSON> 数据库表结构

-- 用户表
CREATE TABLE users (
    id TEXT PRIMARY KEY,
    username TEXT UNIQUE NOT NULL,
    email TEXT UNIQUE NOT NULL,
    password_hash TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_username ON users(username);

-- 衣物表
CREATE TABLE clothing (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    category TEXT NOT NULL,
    subcategory TEXT NOT NULL,
    type TEXT NOT NULL,
    brand TEXT,
    color TEXT NOT NULL,
    size TEXT NOT NULL,
    season TEXT NOT NULL,
    purchase_date DATE,
    price DECIMAL(10,2),
    description TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_clothing_user ON clothing(user_id);
CREATE INDEX idx_clothing_category ON clothing(category);
CREATE INDEX idx_clothing_color ON clothing(color);
CREATE INDEX idx_clothing_season ON clothing(season);

-- 衣物图片表
CREATE TABLE clothing_images (
    id TEXT PRIMARY KEY,
    clothing_id TEXT NOT NULL,
    image_url TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(clothing_id) REFERENCES clothing(id) ON DELETE CASCADE
);

CREATE INDEX idx_clothing_images_clothing ON clothing_images(clothing_id);

-- 穿搭表
CREATE TABLE outfits (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    occasion TEXT,
    notes TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_outfits_user ON outfits(user_id);
CREATE INDEX idx_outfits_occasion ON outfits(occasion);

-- 穿搭衣物关联表
CREATE TABLE outfit_items (
    outfit_id TEXT NOT NULL,
    clothing_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (outfit_id, clothing_id),
    FOREIGN KEY(outfit_id) REFERENCES outfits(id) ON DELETE CASCADE,
    FOREIGN KEY(clothing_id) REFERENCES clothing(id) ON DELETE CASCADE
);

-- 收藏表
CREATE TABLE favorites (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    item_type TEXT NOT NULL, -- 'clothing' 或 'outfit'
    item_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, item_type, item_id)
);

-- 优化收藏表索引
CREATE INDEX idx_favorites_user_type ON favorites(user_id, item_type);
CREATE INDEX idx_favorites_created_at ON favorites(created_at);
CREATE INDEX idx_favorites_item ON favorites(item_type, item_id);

-- 标签表（可选，用于更灵活的标签系统）
CREATE TABLE tags (
    id TEXT PRIMARY KEY,
    name TEXT UNIQUE NOT NULL,
    category TEXT NOT NULL, -- 'color', 'style', 'occasion', etc.
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- 衣物标签关联表
CREATE TABLE clothing_tags (
    clothing_id TEXT NOT NULL,
    tag_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (clothing_id, tag_id),
    FOREIGN KEY(clothing_id) REFERENCES clothing(id) ON DELETE CASCADE,
    FOREIGN KEY(tag_id) REFERENCES tags(id) ON DELETE CASCADE
);

-- 收藏集合表
CREATE TABLE collections (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    cover_image_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_collections_user ON collections(user_id);
CREATE INDEX idx_collections_public ON collections(is_public);

-- 收藏集合项目表
CREATE TABLE collection_items (
    collection_id TEXT NOT NULL,
    item_type TEXT NOT NULL, -- 'clothing' 或 'outfit'
    item_id TEXT NOT NULL,
    added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (collection_id, item_type, item_id),
    FOREIGN KEY(collection_id) REFERENCES collections(id) ON DELETE CASCADE
);

CREATE INDEX idx_collection_items_collection ON collection_items(collection_id);

-- 分享记录表
CREATE TABLE shares (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    share_type TEXT NOT NULL, -- 'collection', 'clothing', 'outfit'
    target_id TEXT NOT NULL,
    share_token TEXT UNIQUE NOT NULL,
    expires_at DATETIME,
    view_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_shares_token ON shares(share_token);
CREATE INDEX idx_shares_user ON shares(user_id);