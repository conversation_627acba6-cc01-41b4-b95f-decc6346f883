name = "yigui-backend"
main = "src/index.js"
compatibility_date = "2024-01-01"
compatibility_flags = ["nodejs_compat"]

[vars]
ENVIRONMENT = "production"
JWT_SECRET = "your-jwt-secret-here"
CORS_ORIGIN = "*"
CLOUDFLARE_ACCOUNT_ID = "7d6910dd16865035e5197d8b1d7ac237"

# 开发环境配置
[env.development]
name = "yigui-backend-dev"

[env.development.vars]
ENVIRONMENT = "development"
JWT_SECRET = "dev-jwt-secret"
CORS_ORIGIN = "*"

# KV 命名空间 (用于会话存储)
[[kv_namespaces]]
binding = "SESSIONS"
id = "47882f0da5f3408a9ddadb781f2e6bb4"

# D1 数据库配置
[[d1_databases]]
binding = "DB"
database_name = "yigui-db"
database_id = "eede9b4d-5edd-4c61-b16d-e8ba8c412d1b"

# R2 存储配置
[[r2_buckets]]
binding = "STORAGE"
bucket_name = "yigui-images"