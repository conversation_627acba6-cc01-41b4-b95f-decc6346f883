#!/usr/bin/env node

// Test R2 upload signature functionality
const API_BASE = 'http://localhost:8787/api';

// Test user data  
const testUser = {
  username: `testuser${Math.floor(Math.random() * 10000)}`,
  email: `test${Math.floor(Math.random() * 10000)}@example.com`,
  password: 'testpassword123'
};

async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json'
    }
  };

  try {
    const response = await fetch(url, { ...defaultOptions, ...options });
    
    let data;
    try {
      data = await response.json();
    } catch (e) {
      data = await response.text();
    }

    return {
      status: response.status,
      ok: response.ok,
      data
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      data: null,
      error: error.message
    };
  }
}

async function testR2Upload() {
  console.log('🧪 测试R2上传签名功能...\n');
  
  // 1. 注册用户
  console.log('1. 注册测试用户...');
  const registerResponse = await apiRequest('/auth/register', {
    method: 'POST',  
    body: JSON.stringify(testUser)
  });
  
  if (!registerResponse.ok) {
    console.error('❌ 注册失败:', registerResponse.data);
    return;
  }
  
  const token = registerResponse.data.data.token;
  const userId = registerResponse.data.data.user.id;
  console.log(`✅ 用户注册成功，ID: ${userId}`);
  
  // 2. 获取上传签名
  console.log('\n2. 获取上传签名...');
  const signatureResponse = await apiRequest('/upload/signature?fileType=image/jpeg&fileName=test.jpg', {
    headers: {
      'Authorization': `Bearer ${token}`,
      'Content-Type': 'application/json'
    }
  });
  
  console.log('响应状态:', signatureResponse.status);
  console.log('响应数据:', JSON.stringify(signatureResponse.data, null, 2));
  
  if (signatureResponse.ok) {
    console.log('✅ 上传签名获取成功!');
    console.log('上传URL:', signatureResponse.data.data.uploadUrl);
    console.log('文件键:', signatureResponse.data.data.key);
    console.log('文件URL:', signatureResponse.data.data.fileUrl);
  } else {
    console.log('❌ 上传签名获取失败');
  }
  
  // 3. 测试直接上传API
  console.log('\n3. 测试直接上传API...');
  
  // 创建一个简单的测试文件
  const testFileContent = 'fake image content for testing';
  const formData = new FormData();
  const blob = new Blob([testFileContent], { type: 'image/jpeg' });
  formData.append('file', blob, 'test.jpg');
  
  const uploadResponse = await apiRequest('/upload', {
    method: 'POST',
    headers: {
      'Authorization': `Bearer ${token}`
    },
    body: formData
  });
  
  console.log('直接上传响应状态:', uploadResponse.status);
  console.log('直接上传响应数据:', JSON.stringify(uploadResponse.data, null, 2));
  
  if (uploadResponse.ok) {
    console.log('✅ 直接上传成功!');
  } else {
    console.log('❌ 直接上传失败');
  }
}

testR2Upload().catch(console.error);