#!/usr/bin/env node

// 收藏功能验证脚本
// 验证所有收藏相关的组件是否正确配置

import { readFileSync } from 'fs';
import { join, dirname } from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

console.log('🔍 验证收藏功能基础设施...\n');

let errors = [];
let warnings = [];

// 1. 检查模型文件
try {
  const favoritesModelPath = join(__dirname, '../src/models/favorites.js');
  const modelContent = readFileSync(favoritesModelPath, 'utf8');
  
  // 检查必要的导出
  const requiredExports = [
    'FavoriteToggleSchema',
    'FavoriteQuerySchema', 
    'FavoriteBatchSchema',
    'FavoriteStatusSchema',
    'FavoritesModel'
  ];
  
  for (const exportName of requiredExports) {
    if (!modelContent.includes(`export const ${exportName}`) && !modelContent.includes(`export class ${exportName}`)) {
      errors.push(`❌ 模型文件缺少导出: ${exportName}`);
    }
  }
  
  console.log('✅ 收藏模型文件检查通过');
} catch (error) {
  errors.push(`❌ 无法读取收藏模型文件: ${error.message}`);
}

// 2. 检查处理器文件
try {
  const favoritesHandlerPath = join(__dirname, '../src/handlers/favorites.js');
  const handlerContent = readFileSync(favoritesHandlerPath, 'utf8');
  
  // 检查必要的函数
  const requiredFunctions = ['toggle', 'list', 'batch', 'status', 'stats'];
  
  for (const funcName of requiredFunctions) {
    if (!handlerContent.includes(`export async function ${funcName}`)) {
      errors.push(`❌ 处理器文件缺少函数: ${funcName}`);
    }
  }
  
  console.log('✅ 收藏处理器文件检查通过');
} catch (error) {
  errors.push(`❌ 无法读取收藏处理器文件: ${error.message}`);
}

// 3. 检查路由配置
try {
  const indexPath = join(__dirname, '../src/index.js');
  const indexContent = readFileSync(indexPath, 'utf8');
  
  // 检查收藏路由
  const requiredRoutes = [
    '/api/favorites/toggle',
    '/api/favorites',
    '/api/favorites/batch',
    '/api/favorites/status',
    '/api/favorites/stats'
  ];
  
  for (const route of requiredRoutes) {
    if (!indexContent.includes(`'${route}'`)) {
      errors.push(`❌ 主路由文件缺少路由: ${route}`);
    }
  }
  
  // 检查导入
  if (!indexContent.includes("import * as favoritesHandler from './handlers/favorites'")) {
    errors.push('❌ 主路由文件缺少收藏处理器导入');
  }
  
  console.log('✅ 路由配置检查通过');
} catch (error) {
  errors.push(`❌ 无法读取主路由文件: ${error.message}`);
}

// 4. 检查数据库模式
try {
  const schemaPath = join(__dirname, '../schema.sql');
  const schemaContent = readFileSync(schemaPath, 'utf8');
  
  // 检查收藏表
  if (!schemaContent.includes('CREATE TABLE favorites')) {
    errors.push('❌ 数据库模式缺少收藏表');
  }
  
  // 检查索引
  const requiredIndexes = [
    'idx_favorites_user_type',
    'idx_favorites_created_at',
    'idx_favorites_item'
  ];
  
  for (const index of requiredIndexes) {
    if (!schemaContent.includes(index)) {
      warnings.push(`⚠️  数据库模式缺少索引: ${index}`);
    }
  }
  
  // 检查新增的表
  if (!schemaContent.includes('CREATE TABLE collections')) {
    warnings.push('⚠️  数据库模式缺少收藏集合表（未来功能）');
  }
  
  if (!schemaContent.includes('CREATE TABLE shares')) {
    warnings.push('⚠️  数据库模式缺少分享表（未来功能）');
  }
  
  console.log('✅ 数据库模式检查通过');
} catch (error) {
  errors.push(`❌ 无法读取数据库模式文件: ${error.message}`);
}

// 5. 检查测试文件
try {
  const testPath = join(__dirname, '../tests/favorites.test.js');
  const testContent = readFileSync(testPath, 'utf8');
  
  if (!testContent.includes('describe(')) {
    errors.push('❌ 收藏测试文件格式不正确');
  }
  
  console.log('✅ 测试文件检查通过');
} catch (error) {
  warnings.push(`⚠️  收藏测试文件不存在或无法读取: ${error.message}`);
}

// 6. 检查API文档
try {
  const docsPath = join(__dirname, '../docs/api.md');
  const docsContent = readFileSync(docsPath, 'utf8');
  
  if (!docsContent.includes('## Favorites Management Endpoints')) {
    warnings.push('⚠️  API文档缺少收藏管理端点说明');
  }
  
  console.log('✅ API文档检查通过');
} catch (error) {
  warnings.push(`⚠️  API文档不存在或无法读取: ${error.message}`);
}

// 输出结果
console.log('\n📊 验证结果:');

if (errors.length === 0) {
  console.log('🎉 所有必要组件都已正确配置！');
} else {
  console.log(`❌ 发现 ${errors.length} 个错误:`);
  errors.forEach(error => console.log(`  ${error}`));
}

if (warnings.length > 0) {
  console.log(`\n⚠️  发现 ${warnings.length} 个警告:`);
  warnings.forEach(warning => console.log(`  ${warning}`));
}

console.log('\n✨ 收藏功能基础设施验证完成');

// 输出下一步建议
console.log('\n📋 下一步建议:');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 运行集成测试: node tests/integration.test.js');
console.log('3. 检查API端点是否正常响应');
console.log('4. 验证数据库连接和表结构');

process.exit(errors.length > 0 ? 1 : 0);