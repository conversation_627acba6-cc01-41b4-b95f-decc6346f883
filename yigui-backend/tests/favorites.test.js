// 收藏功能单元测试
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  FavoritesModel, 
  FavoriteToggleSchema, 
  FavoriteQuerySchema, 
  FavoriteBatchSchema,
  FavoriteStatusSchema 
} from '../src/models/favorites.js';

describe('Favorites Model Validation', () => {
  describe('FavoriteToggleSchema', () => {
    it('should validate correct toggle data', () => {
      const validData = {
        itemType: 'clothing',
        itemId: '123e4567-e89b-12d3-a456-************'
      };
      
      const result = FavoriteToggleSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject invalid item type', () => {
      const invalidData = {
        itemType: 'invalid',
        itemId: '123e4567-e89b-12d3-a456-************'
      };
      
      const result = FavoriteToggleSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid UUID', () => {
      const invalidData = {
        itemType: 'clothing',
        itemId: 'invalid-uuid'
      };
      
      const result = FavoriteToggleSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('FavoriteQuerySchema', () => {
    it('should validate correct query parameters', () => {
      const validData = {
        type: 'clothing',
        page: 1,
        pageSize: 20,
        category: 'tops',
        sortBy: 'created_at',
        order: 'desc'
      };
      
      const result = FavoriteQuerySchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should apply default values', () => {
      const minimalData = {};
      
      const result = FavoriteQuerySchema.safeParse(minimalData);
      expect(result.success).toBe(true);
      expect(result.data.page).toBe(1);
      expect(result.data.pageSize).toBe(20);
      expect(result.data.sortBy).toBe('created_at');
      expect(result.data.order).toBe('desc');
    });
  });

  describe('FavoriteBatchSchema', () => {
    it('should validate correct batch data', () => {
      const validData = {
        action: 'add',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
          { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-426614174001' }
        ]
      };
      
      const result = FavoriteBatchSchema.safeParse(validData);
      expect(result.success).toBe(true);
    });

    it('should reject empty items array', () => {
      const invalidData = {
        action: 'add',
        items: []
      };
      
      const result = FavoriteBatchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject too many items', () => {
      const items = Array.from({ length: 51 }, (_, i) => ({
        itemType: 'clothing',
        itemId: `123e4567-e89b-12d3-a456-42661417400${i}`
      }));
      
      const invalidData = {
        action: 'add',
        items
      };
      
      const result = FavoriteBatchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('FavoriteStatusSchema', () => {
    it('should validate and parse itemIds string', () => {
      const validData = {
        itemType: 'clothing',
        itemIds: '123e4567-e89b-12d3-a456-************,123e4567-e89b-12d3-a456-426614174001'
      };
      
      const result = FavoriteStatusSchema.safeParse(validData);
      expect(result.success).toBe(true);
      expect(result.data.itemIds).toHaveLength(2);
    });

    it('should reject too many itemIds', () => {
      const itemIds = Array.from({ length: 101 }, (_, i) => 
        `123e4567-e89b-12d3-a456-42661417400${i.toString().padStart(1, '0')}`
      ).join(',');
      
      const invalidData = {
        itemType: 'clothing',
        itemIds
      };
      
      const result = FavoriteStatusSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });
});

// Mock database for testing FavoritesModel
class MockDB {
  constructor() {
    this.data = new Map();
    this.queries = [];
  }

  prepare(query) {
    this.queries.push(query);
    return {
      bind: (...params) => ({
        first: () => this.mockFirst(query, params),
        all: () => this.mockAll(query, params),
        run: () => this.mockRun(query, params)
      })
    };
  }

  mockFirst(query, params) {
    // Mock implementation for testing
    if (query.includes('SELECT id FROM favorites')) {
      return null; // No existing favorite
    }
    if (query.includes('SELECT COUNT(*) as total')) {
      return { total: 0 };
    }
    return null;
  }

  mockAll(query, params) {
    return { results: [] };
  }

  mockRun(query, params) {
    return { success: true };
  }
}

describe('FavoritesModel', () => {
  let favoritesModel;
  let mockDB;

  beforeEach(() => {
    mockDB = new MockDB();
    favoritesModel = new FavoritesModel(mockDB);
  });

  describe('toggleFavorite', () => {
    it('should create FavoritesModel instance', () => {
      expect(favoritesModel).toBeInstanceOf(FavoritesModel);
    });

    it('should have toggleFavorite method', () => {
      expect(typeof favoritesModel.toggleFavorite).toBe('function');
    });
  });

  describe('getFavorites', () => {
    it('should have getFavorites method', () => {
      expect(typeof favoritesModel.getFavorites).toBe('function');
    });
  });

  describe('batchFavorites', () => {
    it('should have batchFavorites method', () => {
      expect(typeof favoritesModel.batchFavorites).toBe('function');
    });
  });

  describe('checkFavoriteStatus', () => {
    it('should have checkFavoriteStatus method', () => {
      expect(typeof favoritesModel.checkFavoriteStatus).toBe('function');
    });
  });

  describe('getFavoriteStats', () => {
    it('should have getFavoriteStats method', () => {
      expect(typeof favoritesModel.getFavoriteStats).toBe('function');
    });

    it('should return comprehensive stats structure', async () => {
      // Mock database responses
      const mockTotalStats = { total: 10, clothing_count: 6, outfit_count: 4 };
      const mockDailyStats = [
        { date: '2024-01-15', daily_count: 3 },
        { date: '2024-01-14', daily_count: 2 }
      ];
      const mockCategoryStats = [
        { category: 'tops', count: 3 },
        { category: 'bottoms', count: 2 }
      ];
      const mockOccasionStats = [
        { occasion: 'casual', count: 2 },
        { occasion: 'formal', count: 1 }
      ];
      const mockRecentOutfits = [
        { favorited_at: '2024-01-15T10:00:00Z', name: '休闲搭配', occasion: 'casual' }
      ];

      // Mock database prepare method
      let callCount = 0;
      mockDB.prepare = vi.fn().mockImplementation((query) => {
        callCount++;
        return {
          bind: vi.fn().mockReturnThis(),
          first: vi.fn().mockImplementation(() => {
            if (callCount === 2) return mockTotalStats; // Second call for total stats
            return null;
          }),
          all: vi.fn().mockImplementation(() => {
            if (callCount === 1) return { results: mockDailyStats }; // First call for daily stats
            if (callCount === 3) return { results: mockCategoryStats }; // Third call for category stats
            if (callCount === 4) return { results: mockOccasionStats }; // Fourth call for occasion stats
            if (callCount === 5) return { results: mockRecentOutfits }; // Fifth call for recent outfits
            return { results: [] };
          })
        };
      });

      const stats = await favoritesModel.getFavoriteStats('user123');

      expect(stats).toEqual({
        total: 10,
        clothingCount: 6,
        outfitCount: 4,
        dailyActivity: mockDailyStats,
        categoryDistribution: mockCategoryStats,
        occasionDistribution: mockOccasionStats,
        recentOutfits: mockRecentOutfits
      });

      // Verify all required queries were called
      expect(mockDB.prepare).toHaveBeenCalledTimes(5);
    });

    it('should handle empty stats gracefully', async () => {
      // Mock empty database responses
      const mockEmptyStats = { total: 0, clothing_count: 0, outfit_count: 0 };
      
      mockDB.prepare = vi.fn().mockImplementation(() => ({
        bind: vi.fn().mockReturnThis(),
        first: vi.fn().mockResolvedValue(mockEmptyStats),
        all: vi.fn().mockResolvedValue({ results: [] })
      }));

      const stats = await favoritesModel.getFavoriteStats('user123');

      expect(stats.total).toBe(0);
      expect(stats.clothingCount).toBe(0);
      expect(stats.outfitCount).toBe(0);
      expect(stats.dailyActivity).toEqual([]);
      expect(stats.categoryDistribution).toEqual([]);
      expect(stats.occasionDistribution).toEqual([]);
      expect(stats.recentOutfits).toEqual([]);
    });

    it('should handle database errors gracefully', async () => {
      mockDB.prepare = vi.fn().mockImplementation(() => ({
        bind: vi.fn().mockReturnThis(),
        first: vi.fn().mockRejectedValue(new Error('Database error')),
        all: vi.fn().mockRejectedValue(new Error('Database error'))
      }));

      await expect(favoritesModel.getFavoriteStats('user123')).rejects.toThrow('Database error');
    });
  });

  describe('getItemDetails', () => {
    it('should have getItemDetails method', () => {
      expect(typeof favoritesModel.getItemDetails).toBe('function');
    });
  });
});

// 搭配收藏专项测试
describe('Outfit Favorites Validation', () => {
  describe('FavoriteToggleSchema with outfit', () => {
    it('should validate outfit toggle data', () => {
      const validData = {
        itemType: 'outfit',
        itemId: '123e4567-e89b-12d3-a456-************'
      };
      
      const result = FavoriteToggleSchema.safeParse(validData);
      expect(result.success).toBe(true);
      expect(result.data.itemType).toBe('outfit');
    });
  });

  describe('FavoriteQuerySchema with outfit type', () => {
    it('should validate outfit query parameters', () => {
      const validData = {
        type: 'outfit',
        page: 1,
        pageSize: 20,
        category: 'casual',
        sortBy: 'created_at',
        order: 'desc'
      };
      
      const result = FavoriteQuerySchema.safeParse(validData);
      expect(result.success).toBe(true);
      expect(result.data.type).toBe('outfit');
    });
  });

  describe('FavoriteBatchSchema with mixed items', () => {
    it('should validate batch data with both clothing and outfit items', () => {
      const validData = {
        action: 'add',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
          { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-426614174001' },
          { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-426614174002' }
        ]
      };
      
      const result = FavoriteBatchSchema.safeParse(validData);
      expect(result.success).toBe(true);
      expect(result.data.items).toHaveLength(3);
      expect(result.data.items.filter(item => item.itemType === 'outfit')).toHaveLength(2);
    });

    it('should validate batch remove operation for outfits', () => {
      const validData = {
        action: 'remove',
        items: [
          { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' },
          { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-426614174001' }
        ]
      };
      
      const result = FavoriteBatchSchema.safeParse(validData);
      expect(result.success).toBe(true);
      expect(result.data.action).toBe('remove');
    });
  });

  describe('FavoriteStatusSchema with outfit items', () => {
    it('should validate outfit status check', () => {
      const validData = {
        itemType: 'outfit',
        itemIds: '123e4567-e89b-12d3-a456-************,123e4567-e89b-12d3-a456-426614174001'
      };
      
      const result = FavoriteStatusSchema.safeParse(validData);
      expect(result.success).toBe(true);
      expect(result.data.itemType).toBe('outfit');
      expect(result.data.itemIds).toHaveLength(2);
    });
  });
});

// 搭配收藏模型测试
describe('Outfit Favorites Model Operations', () => {
  let favoritesModel;
  let mockDB;

  beforeEach(() => {
    mockDB = new MockDB();
    favoritesModel = new FavoritesModel(mockDB);
  });

  describe('outfit-specific operations', () => {
    it('should handle outfit favorite toggle', async () => {
      const userId = 'user123';
      const itemType = 'outfit';
      const itemId = '123e4567-e89b-12d3-a456-************';

      // Mock no existing favorite
      mockDB.mockFirst = () => null;
      mockDB.mockRun = () => ({ success: true });

      const result = await favoritesModel.toggleFavorite(userId, itemType, itemId);
      
      expect(result.success).toBe(true);
      expect(result.isFavorited).toBe(true);
      expect(result.action).toBe('added');
    });

    it('should handle outfit favorites list query', async () => {
      const userId = 'user123';
      const queryParams = { type: 'outfit', page: 1, pageSize: 20 };

      mockDB.mockAll = () => ({ results: [] });
      mockDB.mockFirst = () => ({ total: 0 });

      const result = await favoritesModel.getFavorites(userId, queryParams);
      
      expect(result.items).toEqual([]);
      expect(result.total).toBe(0);
      expect(result.page).toBe(1);
    });

    it('should handle batch outfit favorites', async () => {
      const userId = 'user123';
      const action = 'add';
      const items = [
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-426614174001' }
      ];

      mockDB.mockFirst = () => null; // No existing favorites
      mockDB.mockRun = () => ({ success: true });

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(2);
      expect(result.totalCount).toBe(2);
      expect(result.results).toHaveLength(2);
    });

    it('should check outfit favorite status', async () => {
      const userId = 'user123';
      const itemType = 'outfit';
      const itemIds = ['123e4567-e89b-12d3-a456-************', '123e4567-e89b-12d3-a456-426614174001'];

      mockDB.mockAll = () => ({ 
        results: [{ item_id: '123e4567-e89b-12d3-a456-************', is_favorited: 1 }] 
      });

      const result = await favoritesModel.checkFavoriteStatus(userId, itemType, itemIds);
      
      expect(result['123e4567-e89b-12d3-a456-************']).toBe(true);
      expect(result['123e4567-e89b-12d3-a456-426614174001']).toBe(false);
    });
  });
});