// 收藏搜索功能测试
import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FavoritesModel } from '../src/models/favorites';

// 模拟数据库
class MockDB {
  constructor() {
    this.data = {
      favorites: [],
      clothing: [],
      outfits: []
    };
    this.queryResults = [];
    this.queries = [];
  }

  prepare(query) {
    this.lastQuery = query;
    this.queries.push(query);
    return {
      bind: (...params) => {
        this.lastParams = params;
        return {
          all: () => ({ results: this.queryResults }),
          first: () => {
            // Return count result for count queries
            if (query.includes('COUNT(*)')) {
              return { total: this.queryResults.length };
            }
            return this.queryResults[0] || null;
          },
          run: () => ({ success: true })
        };
      }
    };
  }

  setQueryResults(results) {
    this.queryResults = results;
  }

  getQueriesContaining(text) {
    return this.queries.filter(q => q.includes(text));
  }
}

describe('收藏搜索API测试', () => {
  let mockDB;
  let favoritesModel;
  const userId = 'test-user-id';

  beforeEach(() => {
    mockDB = new MockDB();
    favoritesModel = new FavoritesModel(mockDB);
  });

  afterEach(() => {
    mockDB = null;
    favoritesModel = null;
  });

  describe('搜索功能测试', () => {
    it('应该支持按名称搜索衣物收藏', async () => {
      // 模拟搜索结果
      mockDB.setQueryResults([
        {
          id: 'fav-1',
          item_type: 'clothing',
          item_id: 'clothing-1',
          favorited_at: '2024-01-01T00:00:00Z',
          clothing_name: '白色T恤',
          category: 'tops',
          color: 'white'
        }
      ]);

      const result = await favoritesModel.getFavorites(userId, {
        search: '白色',
        type: 'clothing'
      });

      const searchQueries = mockDB.getQueriesContaining('LOWER(c.name) LIKE LOWER(?)');
      expect(searchQueries.length).toBeGreaterThan(0);
      expect(result.searchQuery).toBe('白色');
    });

    it('应该支持按描述搜索', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getFavorites(userId, {
        search: '休闲',
        type: 'clothing'
      });

      const searchQueries = mockDB.getQueriesContaining('LOWER(c.description) LIKE LOWER(?)');
      expect(searchQueries.length).toBeGreaterThan(0);
    });

    it('应该支持按品牌搜索', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getFavorites(userId, {
        search: 'Nike',
        type: 'clothing'
      });

      const searchQueries = mockDB.getQueriesContaining('LOWER(c.brand) LIKE LOWER(?)');
      expect(searchQueries.length).toBeGreaterThan(0);
    });

    it('应该支持搭配名称和备注搜索', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getFavorites(userId, {
        search: '约会',
        type: 'outfit'
      });

      const nameQueries = mockDB.getQueriesContaining('LOWER(o.name) LIKE LOWER(?)');
      const notesQueries = mockDB.getQueriesContaining('LOWER(o.notes) LIKE LOWER(?)');
      expect(nameQueries.length).toBeGreaterThan(0);
      expect(notesQueries.length).toBeGreaterThan(0);
    });

    it('应该支持按相关性排序', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getFavorites(userId, {
        search: '白色',
        sortBy: 'relevance',
        type: 'clothing'
      });

      const relevanceQueries = mockDB.getQueriesContaining('ORDER BY');
      const caseQueries = mockDB.getQueriesContaining('CASE');
      expect(relevanceQueries.length).toBeGreaterThan(0);
      expect(caseQueries.length).toBeGreaterThan(0);
    });
  });

  describe('筛选功能测试', () => {
    it('应该支持按颜色筛选', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getFavorites(userId, {
        color: '红色',
        type: 'clothing'
      });

      const colorQueries = mockDB.getQueriesContaining('LOWER(c.color) LIKE LOWER(?)');
      expect(colorQueries.length).toBeGreaterThan(0);
    });

    it('应该支持按季节筛选', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getFavorites(userId, {
        season: 'summer',
        type: 'clothing'
      });

      const seasonQueries = mockDB.getQueriesContaining('c.season = ?');
      expect(seasonQueries.length).toBeGreaterThan(0);
    });

    it('应该支持按场合筛选搭配', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getFavorites(userId, {
        occasion: 'work',
        type: 'outfit'
      });

      const occasionQueries = mockDB.getQueriesContaining('o.occasion = ?');
      expect(occasionQueries.length).toBeGreaterThan(0);
    });

    it('应该支持组合筛选条件', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getFavorites(userId, {
        search: '白色',
        color: '白色',
        season: 'summer',
        category: 'tops',
        type: 'clothing'
      });

      const searchQueries = mockDB.getQueriesContaining('LOWER(c.name) LIKE LOWER(?)');
      const colorQueries = mockDB.getQueriesContaining('LOWER(c.color) LIKE LOWER(?)');
      const seasonQueries = mockDB.getQueriesContaining('c.season = ?');
      const categoryQueries = mockDB.getQueriesContaining('c.category = ?');
      
      expect(searchQueries.length).toBeGreaterThan(0);
      expect(colorQueries.length).toBeGreaterThan(0);
      expect(seasonQueries.length).toBeGreaterThan(0);
      expect(categoryQueries.length).toBeGreaterThan(0);
    });
  });

  describe('搜索建议功能测试', () => {
    it('应该返回衣物名称建议', async () => {
      mockDB.setQueryResults([
        { suggestion: '白色T恤', type: 'clothing', category: 'tops' },
        { suggestion: '白色衬衫', type: 'clothing', category: 'tops' }
      ]);

      const suggestions = await favoritesModel.getSearchSuggestions(userId, '白色', 10);

      const clothingQueries = mockDB.getQueriesContaining('DISTINCT c.name as suggestion');
      expect(clothingQueries.length).toBeGreaterThan(0);
      expect(suggestions.length).toBeGreaterThan(0);
      expect(suggestions[0]).toHaveProperty('suggestion');
      expect(suggestions[0]).toHaveProperty('type');
    });

    it('应该返回搭配名称建议', async () => {
      mockDB.setQueryResults([
        { suggestion: '约会装', type: 'outfit', occasion: 'date' }
      ]);

      await favoritesModel.getSearchSuggestions(userId, '约会', 10);

      const outfitQueries = mockDB.getQueriesContaining('DISTINCT o.name as suggestion');
      expect(outfitQueries.length).toBeGreaterThan(0);
    });

    it('应该限制建议数量', async () => {
      mockDB.setQueryResults([]);

      const suggestions = await favoritesModel.getSearchSuggestions(userId, '测试', 5);

      // 验证返回的建议数量不超过限制
      expect(suggestions.length).toBeLessThanOrEqual(5);
    });

    it('查询长度小于2时应该返回空数组', async () => {
      const suggestions = await favoritesModel.getSearchSuggestions(userId, 'a', 10);
      expect(suggestions).toEqual([]);
    });
  });

  describe('热门搜索词功能测试', () => {
    it('应该返回基于收藏频率的热门搜索词', async () => {
      mockDB.setQueryResults([
        { term: '白色T恤', frequency: 5 },
        { term: '牛仔裤', frequency: 3 },
        { term: '运动鞋', frequency: 2 }
      ]);

      const popularTerms = await favoritesModel.getPopularSearchTerms(userId, 5);

      expect(mockDB.lastQuery).toContain('COUNT(*) as frequency');
      expect(mockDB.lastQuery).toContain('ORDER BY frequency DESC');
      expect(mockDB.lastParams).toContain(5);
    });

    it('应该排除空的搜索词', async () => {
      mockDB.setQueryResults([]);

      await favoritesModel.getPopularSearchTerms(userId, 5);

      expect(mockDB.lastQuery).toContain('HAVING term IS NOT NULL');
    });
  });

  describe('查询构建测试', () => {
    it('应该正确构建衣物搜索查询', () => {
      const filters = {
        type: 'clothing',
        search: '白色',
        category: 'tops',
        color: '白色'
      };

      const { query, bindings } = favoritesModel._buildSearchQuery(userId, filters);

      expect(query).toContain('JOIN clothing c ON f.item_id = c.id');
      expect(query).toContain('f.user_id = ?');
      expect(query).toContain('f.item_type = ?');
      expect(query).toContain('c.category = ?');
      expect(query).toContain('LOWER(c.color) LIKE LOWER(?)');
      
      expect(bindings).toContain(userId);
      expect(bindings).toContain('clothing');
      expect(bindings).toContain('tops');
      expect(bindings).toContain('%白色%');
    });

    it('应该正确构建搭配搜索查询', () => {
      const filters = {
        type: 'outfit',
        search: '约会',
        occasion: 'date'
      };

      const { query, bindings } = favoritesModel._buildSearchQuery(userId, filters);

      expect(query).toContain('JOIN outfits o ON f.item_id = o.id');
      expect(query).toContain('o.occasion = ?');
      expect(bindings).toContain('outfit');
      expect(bindings).toContain('date');
    });

    it('应该正确构建混合类型查询', () => {
      const filters = {
        search: '白色'
      };

      const { query } = favoritesModel._buildSearchQuery(userId, filters);

      expect(query).toContain('LEFT JOIN clothing c');
      expect(query).toContain('LEFT JOIN outfits o');
    });
  });
});