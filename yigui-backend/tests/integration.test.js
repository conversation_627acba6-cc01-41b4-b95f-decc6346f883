#!/usr/bin/env node

// Yigui Backend API 集成测试套件
// 测试所有主要API端点的功能

const API_BASE = process.env.PRODUCTION_TEST ? 'https://yigui-backend.qu18354531302.workers.dev/api' : 'http://localhost:8788/api';
let authToken = null;
let testUserId = null;
let testClothingId = null;
let testOutfitId = null;

// 测试用户数据
const testUser = {
  username: `testuser${Math.floor(Math.random() * 10000)}`,
  email: `test${Math.floor(Math.random() * 10000)}@example.com`,
  password: 'testpassword123'
};

// 测试衣物数据
const testClothing = {
  name: '测试T恤',
  category: 'tops',
  subcategory: 'tshirts',
  type: 'casual',
  brand: '测试品牌',
  color: 'white',
  size: 'M',
  season: 'summer',
  description: '这是一件测试T恤',
  imageUrls: []
};

// 测试穿搭数据
const testOutfit = {
  name: '测试穿搭',
  occasion: 'casual',
  notes: '这是一个测试穿搭',
  clothingItems: []
};

// HTTP请求助手函数
async function apiRequest(endpoint, options = {}) {
  const url = `${API_BASE}${endpoint}`;
  const defaultOptions = {
    headers: {
      'Content-Type': 'application/json',
      ...(authToken && { 'Authorization': `Bearer ${authToken}` })
    }
  };

  try {
    const response = await fetch(url, { ...defaultOptions, ...options });
    
    let data;
    try {
      data = await response.json();
    } catch (e) {
      data = null;
    }

    return {
      status: response.status,
      ok: response.ok,
      data
    };
  } catch (error) {
    return {
      status: 0,
      ok: false,
      data: null,
      error: error.message
    };
  }
}

// 简单的测试运行器
async function runTests() {
  console.log('🧪 开始执行API集成测试...\n');
  
  let passedTests = 0;
  let failedTests = 0;
  
  const tests = [
    { 
      name: '健康检查', 
      fn: async () => {
        const response = await apiRequest('/health');
        if (response.status !== 200) throw new Error(`期望状态码200，实际${response.status}`);
        if (!response.data?.status) throw new Error('缺少status字段');
        console.log(`   响应: ${JSON.stringify(response.data)}`);
      }
    },
    
    { 
      name: '用户注册', 
      fn: async () => {
        const response = await apiRequest('/auth/register', {
          method: 'POST',
          body: JSON.stringify(testUser)
        });
        
        if (response.status !== 200) {
          throw new Error(`注册失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        if (!response.data?.success) {
          throw new Error(`注册失败：${response.data?.error || '未知错误'}`);
        }
        
        authToken = response.data.data.token;
        testUserId = response.data.data.user.id;
        console.log(`   用户ID: ${testUserId}`);
      }
    },
    
    { 
      name: '用户登录', 
      fn: async () => {
        const response = await apiRequest('/auth/login', {
          method: 'POST',
          body: JSON.stringify({
            email: testUser.email,
            password: testUser.password
          })
        });
        
        if (response.status !== 200) {
          throw new Error(`登录失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        authToken = response.data.data.token;
        console.log(`   获取到Token: ${authToken.substr(0, 20)}...`);
      }
    },
    
    { 
      name: '获取用户资料', 
      fn: async () => {
        const response = await apiRequest('/auth/profile');
        
        if (response.status !== 200) {
          throw new Error(`获取资料失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   用户名: ${response.data.data.username}`);
      }
    },
    
    { 
      name: '创建衣物', 
      fn: async () => {
        const response = await apiRequest('/clothing', {
          method: 'POST',
          body: JSON.stringify(testClothing)
        });
        
        if (response.status !== 200) {
          throw new Error(`创建衣物失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        testClothingId = response.data.data.id;
        console.log(`   衣物ID: ${testClothingId}`);
      }
    },
    
    { 
      name: '获取衣物列表', 
      fn: async () => {
        const response = await apiRequest('/clothing');
        
        if (response.status !== 200) {
          throw new Error(`获取衣物列表失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   衣物总数: ${response.data.data.total}`);
      }
    },
    
    { 
      name: '获取单个衣物详情', 
      fn: async () => {
        const response = await apiRequest(`/clothing/${testClothingId}`);
        
        if (response.status !== 200) {
          throw new Error(`获取衣物详情失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   衣物名称: ${response.data.data.name}`);
      }
    },
    
    { 
      name: '更新衣物信息', 
      fn: async () => {
        const updateData = { name: '更新的测试T恤', description: '更新的描述' };
        const response = await apiRequest(`/clothing/${testClothingId}`, {
          method: 'PUT',
          body: JSON.stringify(updateData)
        });
        
        if (response.status !== 200) {
          throw new Error(`更新衣物失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   更新后名称: ${response.data.data.name}`);
      }
    },
    
    { 
      name: '创建穿搭', 
      fn: async () => {
        const response = await apiRequest('/outfits', {
          method: 'POST',
          body: JSON.stringify({
            ...testOutfit,
            clothingItems: [testClothingId]
          })
        });
        
        if (response.status !== 200) {
          throw new Error(`创建穿搭失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        testOutfitId = response.data.data.id;
        console.log(`   穿搭ID: ${testOutfitId}`);
      }
    },
    
    { 
      name: '获取穿搭列表', 
      fn: async () => {
        const response = await apiRequest('/outfits');
        
        if (response.status !== 200) {
          throw new Error(`获取穿搭列表失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   穿搭总数: ${response.data.data.total}`);
      }
    },
    
    { 
      name: '获取单个穿搭详情', 
      fn: async () => {
        const response = await apiRequest(`/outfits/${testOutfitId}`);
        
        if (response.status !== 200) {
          throw new Error(`获取穿搭详情失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   穿搭名称: ${response.data.data.name}, 包含衣物: ${response.data.data.clothingItems.length}件`);
      }
    },
    
    { 
      name: '获取穿搭统计信息', 
      fn: async () => {
        const response = await apiRequest('/outfits/stats');
        
        if (response.status !== 200) {
          throw new Error(`获取统计信息失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   统计信息: 总计${response.data.data.total}个穿搭`);
      }
    },
    
    { 
      name: '获取穿搭推荐', 
      fn: async () => {
        const response = await apiRequest('/outfits/recommendations?limit=5');
        
        if (response.status !== 200) {
          throw new Error(`获取推荐失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   推荐穿搭: ${response.data.data.items.length}个`);
      }
    },
    
    { 
      name: '测试授权控制', 
      fn: async () => {
        const originalToken = authToken;
        authToken = null; // 清除token
        
        const response = await apiRequest('/clothing');
        if (response.status !== 401) {
          throw new Error(`期望401未授权状态码，实际${response.status}`);
        }
        
        authToken = originalToken; // 恢复token
        console.log(`   正确拒绝了未授权请求`);
      }
    },
    
    { 
      name: '获取文件上传签名', 
      fn: async () => {
        const response = await apiRequest('/upload/signature?fileType=image/jpeg&fileName=test.jpg');
        
        if (response.status !== 200) {
          throw new Error(`获取上传签名失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        console.log(`   上传签名获取成功`);
      }
    },
    
    { 
      name: '收藏衣物', 
      fn: async () => {
        const response = await apiRequest('/favorites/toggle', {
          method: 'POST',
          body: JSON.stringify({
            itemType: 'clothing',
            itemId: testClothingId
          })
        });
        
        if (response.status !== 200) {
          throw new Error(`收藏衣物失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        if (!response.data.data.isFavorited) {
          throw new Error('收藏状态应该为true');
        }
        
        console.log(`   衣物收藏成功，操作: ${response.data.data.action}`);
      }
    },
    
    { 
      name: '收藏穿搭', 
      fn: async () => {
        const response = await apiRequest('/favorites/toggle', {
          method: 'POST',
          body: JSON.stringify({
            itemType: 'outfit',
            itemId: testOutfitId
          })
        });
        
        if (response.status !== 200) {
          throw new Error(`收藏穿搭失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        if (!response.data.data.isFavorited) {
          throw new Error('收藏状态应该为true');
        }
        
        console.log(`   穿搭收藏成功，操作: ${response.data.data.action}`);
      }
    },
    
    { 
      name: '获取收藏列表', 
      fn: async () => {
        const response = await apiRequest('/favorites?page=1&pageSize=10');
        
        if (response.status !== 200) {
          throw new Error(`获取收藏列表失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        if (response.data.data.total < 2) {
          throw new Error('收藏列表应该至少包含2个项目');
        }
        
        console.log(`   收藏总数: ${response.data.data.total}, 当前页: ${response.data.data.items.length}个`);
      }
    },
    
    { 
      name: '检查收藏状态', 
      fn: async () => {
        const response = await apiRequest(`/favorites/status?itemType=clothing&itemIds=${testClothingId}`);
        
        if (response.status !== 200) {
          throw new Error(`检查收藏状态失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        if (!response.data.data.statusMap[testClothingId]) {
          throw new Error('衣物应该处于收藏状态');
        }
        
        console.log(`   收藏状态检查成功`);
      }
    },
    
    { 
      name: '获取收藏统计', 
      fn: async () => {
        const response = await apiRequest('/favorites/stats');
        
        if (response.status !== 200) {
          throw new Error(`获取收藏统计失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        if (response.data.data.total < 2) {
          throw new Error('收藏统计总数应该至少为2');
        }
        
        console.log(`   收藏统计: 总计${response.data.data.total}, 衣物${response.data.data.clothingCount}, 穿搭${response.data.data.outfitCount}`);
      }
    },
    
    { 
      name: '批量收藏操作', 
      fn: async () => {
        // 先取消收藏，然后批量添加
        await apiRequest('/favorites/toggle', {
          method: 'POST',
          body: JSON.stringify({
            itemType: 'clothing',
            itemId: testClothingId
          })
        });
        
        const response = await apiRequest('/favorites/batch', {
          method: 'POST',
          body: JSON.stringify({
            action: 'add',
            items: [
              { itemType: 'clothing', itemId: testClothingId }
            ]
          })
        });
        
        if (response.status !== 200) {
          throw new Error(`批量收藏操作失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        if (response.data.data.successCount !== 1) {
          throw new Error('批量操作成功数量应该为1');
        }
        
        console.log(`   批量操作成功: ${response.data.data.successCount}/${response.data.data.totalCount}`);
      }
    },
    
    { 
      name: '取消收藏衣物', 
      fn: async () => {
        const response = await apiRequest('/favorites/toggle', {
          method: 'POST',
          body: JSON.stringify({
            itemType: 'clothing',
            itemId: testClothingId
          })
        });
        
        if (response.status !== 200) {
          throw new Error(`取消收藏失败：状态码${response.status}, 错误: ${response.data?.error || '未知错误'}`);
        }
        
        if (response.data.data.isFavorited) {
          throw new Error('收藏状态应该为false');
        }
        
        console.log(`   取消收藏成功，操作: ${response.data.data.action}`);
      }
    }
  ];
  
  for (const test of tests) {
    try {
      await test.fn();
      console.log(`✅ ${test.name}`);
      passedTests++;
    } catch (error) {
      console.log(`❌ ${test.name}: ${error.message}`);
      failedTests++;
    }
  }
  
  // 清理测试数据
  console.log('\n🧹 清理测试数据...');
  
  try {
    if (testOutfitId) {
      const response = await apiRequest(`/outfits/${testOutfitId}`, { method: 'DELETE' });
      if (response.status === 200) {
        console.log('✅ 测试穿搭已删除');
      }
    }
  } catch (error) {
    console.log('⚠️  清理穿搭失败:', error.message);
  }
  
  try {
    if (testClothingId) {
      const response = await apiRequest(`/clothing/${testClothingId}`, { method: 'DELETE' });
      if (response.status === 200) {
        console.log('✅ 测试衣物已删除');
      }
    }
  } catch (error) {
    console.log('⚠️  清理衣物失败:', error.message);
  }
  
  console.log(`\n📊 测试结果: ${passedTests} ✅ 通过, ${failedTests} ❌ 失败`);
  
  if (failedTests === 0) {
    console.log('🎉 所有测试都通过了！API功能正常');
  } else {
    console.log('⚠️  有测试失败，请检查API实现');
  }
  
  process.exit(failedTests > 0 ? 1 : 0);
}

// 执行测试
runTests().catch(error => {
  console.error('❌ 测试执行失败:', error);
  process.exit(1);
});