// 批量收藏操作API单元测试
import { describe, it, expect, beforeEach, vi } from 'vitest';
import { 
  FavoritesModel, 
  FavoriteBatchSchema 
} from '../src/models/favorites.js';
import { batch } from '../src/handlers/favorites.js';

// Mock database for testing
class MockDB {
  constructor() {
    this.data = new Map();
    this.queries = [];
    this.shouldFail = false;
    this.existingFavorites = new Set();
  }

  prepare(query) {
    this.queries.push(query);
    return {
      bind: (...params) => ({
        first: () => this.mockFirst(query, params),
        all: () => this.mockAll(query, params),
        run: () => this.mockRun(query, params)
      })
    };
  }

  mockFirst(query, params) {
    if (this.shouldFail) {
      throw new Error('Database error');
    }

    if (query.includes('SELECT id FROM favorites')) {
      const [userId, itemType, itemId] = params;
      const key = `${userId}-${itemType}-${itemId}`;
      return this.existingFavorites.has(key) ? { id: 'existing-id' } : null;
    }
    
    return null;
  }

  mockAll(query, params) {
    if (this.shouldFail) {
      throw new Error('Database error');
    }
    return { results: [] };
  }

  mockRun(query, params) {
    if (this.shouldFail) {
      throw new Error('Database error');
    }

    if (query.includes('INSERT INTO favorites')) {
      const [id, userId, itemType, itemId] = params;
      const key = `${userId}-${itemType}-${itemId}`;
      this.existingFavorites.add(key);
    } else if (query.includes('DELETE FROM favorites')) {
      const [userId, itemType, itemId] = params;
      const key = `${userId}-${itemType}-${itemId}`;
      this.existingFavorites.delete(key);
    }

    return { success: true };
  }

  setExistingFavorite(userId, itemType, itemId) {
    const key = `${userId}-${itemType}-${itemId}`;
    this.existingFavorites.add(key);
  }

  setShouldFail(shouldFail) {
    this.shouldFail = shouldFail;
  }
}

describe('Batch Favorites API', () => {
  let mockDB;
  let favoritesModel;

  beforeEach(() => {
    mockDB = new MockDB();
    favoritesModel = new FavoritesModel(mockDB);
  });

  describe('FavoriteBatchSchema Validation', () => {
    it('should validate correct batch add data', () => {
      const validData = {
        action: 'add',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
          { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' }
        ]
      };
      
      const result = FavoriteBatchSchema.safeParse(validData);
      expect(result.success).toBe(true);
      expect(result.data.action).toBe('add');
      expect(result.data.items).toHaveLength(2);
    });

    it('should validate correct batch remove data', () => {
      const validData = {
        action: 'remove',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' }
        ]
      };
      
      const result = FavoriteBatchSchema.safeParse(validData);
      expect(result.success).toBe(true);
      expect(result.data.action).toBe('remove');
    });

    it('should reject invalid action', () => {
      const invalidData = {
        action: 'invalid',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' }
        ]
      };
      
      const result = FavoriteBatchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject empty items array', () => {
      const invalidData = {
        action: 'add',
        items: []
      };
      
      const result = FavoriteBatchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject too many items (>50)', () => {
      const items = Array.from({ length: 51 }, (_, i) => ({
        itemType: 'clothing',
        itemId: `123e4567-e89b-12d3-a456-42661417400${i.toString().padStart(1, '0')}`
      }));
      
      const invalidData = {
        action: 'add',
        items
      };
      
      const result = FavoriteBatchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid UUID in items', () => {
      const invalidData = {
        action: 'add',
        items: [
          { itemType: 'clothing', itemId: 'invalid-uuid' }
        ]
      };
      
      const result = FavoriteBatchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });

    it('should reject invalid itemType in items', () => {
      const invalidData = {
        action: 'add',
        items: [
          { itemType: 'invalid', itemId: '123e4567-e89b-12d3-a456-************' }
        ]
      };
      
      const result = FavoriteBatchSchema.safeParse(invalidData);
      expect(result.success).toBe(false);
    });
  });

  describe('FavoritesModel.batchFavorites', () => {
    const userId = 'user123';

    it('should successfully add multiple new favorites', async () => {
      const action = 'add';
      const items = [
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' }
      ];

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(3);
      expect(result.totalCount).toBe(3);
      expect(result.results).toHaveLength(3);
      
      result.results.forEach((item, index) => {
        expect(item.itemType).toBe(items[index].itemType);
        expect(item.itemId).toBe(items[index].itemId);
        expect(item.success).toBe(true);
        expect(item.action).toBe('added');
      });
    });

    it('should handle adding existing favorites (should mark as already_exists)', async () => {
      const action = 'add';
      const items = [
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' }
      ];

      // Set first item as already existing
      mockDB.setExistingFavorite(userId, 'clothing', '123e4567-e89b-12d3-a456-************');

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(2);
      expect(result.totalCount).toBe(2);
      expect(result.results).toHaveLength(2);
      
      expect(result.results[0].action).toBe('already_exists');
      expect(result.results[1].action).toBe('added');
    });

    it('should successfully remove multiple favorites', async () => {
      const action = 'remove';
      const items = [
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' }
      ];

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(2);
      expect(result.totalCount).toBe(2);
      expect(result.results).toHaveLength(2);
      
      result.results.forEach((item, index) => {
        expect(item.itemType).toBe(items[index].itemType);
        expect(item.itemId).toBe(items[index].itemId);
        expect(item.success).toBe(true);
        expect(item.action).toBe('removed');
      });
    });

    it('should handle mixed success and failure scenarios', async () => {
      const action = 'add';
      const items = [
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' }
      ];

      // Mock database to fail on second item
      let callCount = 0;
      const originalMockFirst = mockDB.mockFirst.bind(mockDB);
      mockDB.mockFirst = (query, params) => {
        if (query.includes('SELECT id FROM favorites')) {
          callCount++;
          if (callCount === 2) {
            throw new Error('Database connection error');
          }
        }
        return originalMockFirst(query, params);
      };

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(1);
      expect(result.totalCount).toBe(2);
      expect(result.results).toHaveLength(2);
      
      expect(result.results[0].success).toBe(true);
      expect(result.results[1].success).toBe(false);
      expect(result.results[1].error).toBe('Database connection error');
    });

    it('should handle single item batch operation', async () => {
      const action = 'add';
      const items = [
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' }
      ];

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(1);
      expect(result.totalCount).toBe(1);
      expect(result.results).toHaveLength(1);
      expect(result.results[0].success).toBe(true);
      expect(result.results[0].action).toBe('added');
    });

    it('should handle maximum allowed items (50)', async () => {
      const action = 'add';
      const items = Array.from({ length: 50 }, (_, i) => ({
        itemType: i % 2 === 0 ? 'clothing' : 'outfit',
        itemId: `123e4567-e89b-12d3-a456-42661417400${i.toString().padStart(1, '0')}`
      }));

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(50);
      expect(result.totalCount).toBe(50);
      expect(result.results).toHaveLength(50);
    });
  });

  describe('Batch Favorites Handler', () => {
    it('should handle valid batch request', async () => {
      const requestBody = {
        action: 'add',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
          { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' }
        ]
      };

      const mockRequest = {
        json: vi.fn().mockResolvedValue(requestBody),
        user: { id: 'user123' },
        env: { DB: mockDB }
      };

      const response = await batch(mockRequest);
      const responseData = JSON.parse(await response.text());
      
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.data.successCount).toBe(2);
      expect(responseData.data.totalCount).toBe(2);
      expect(responseData.message).toContain('成功添加 2/2 个项目到收藏');
    });

    it('should handle invalid batch request data', async () => {
      const requestBody = {
        action: 'invalid',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' }
        ]
      };

      const mockRequest = {
        json: vi.fn().mockResolvedValue(requestBody),
        user: { id: 'user123' },
        env: { DB: mockDB }
      };

      const response = await batch(mockRequest);
      const responseData = JSON.parse(await response.text());
      
      expect(response.status).toBe(400);
      expect(responseData.success).toBe(false);
      expect(responseData.error).toBe('Validation failed');
    });

    it('should handle batch remove request', async () => {
      const requestBody = {
        action: 'remove',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
          { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' }
        ]
      };

      const mockRequest = {
        json: vi.fn().mockResolvedValue(requestBody),
        user: { id: 'user123' },
        env: { DB: mockDB }
      };

      const response = await batch(mockRequest);
      const responseData = JSON.parse(await response.text());
      
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.data.successCount).toBe(2);
      expect(responseData.data.totalCount).toBe(2);
      expect(responseData.message).toContain('成功从收藏中移除 2/2 个项目');
    });

    it('should handle request parsing error', async () => {
      const mockRequest = {
        json: vi.fn().mockRejectedValue(new Error('Invalid JSON')),
        user: { id: 'user123' },
        env: { DB: mockDB }
      };

      const response = await batch(mockRequest);
      const responseData = JSON.parse(await response.text());
      
      expect(response.status).toBe(500);
      expect(responseData.success).toBe(false);
      expect(responseData.error).toBe('批量收藏操作失败');
    });

    it('should handle database error gracefully', async () => {
      const requestBody = {
        action: 'add',
        items: [
          { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' }
        ]
      };

      // Set database to fail for this test
      mockDB.setShouldFail(true);

      const mockRequest = {
        json: vi.fn().mockResolvedValue(requestBody),
        user: { id: 'user123' },
        env: { DB: mockDB }
      };

      const response = await batch(mockRequest);
      const responseData = JSON.parse(await response.text());
      
      // The batch operation handles individual failures gracefully
      // It should return 200 but with failed items in the results
      expect(response.status).toBe(200);
      expect(responseData.success).toBe(true);
      expect(responseData.data.successCount).toBe(0);
      expect(responseData.data.totalCount).toBe(1);
      expect(responseData.data.results[0].success).toBe(false);
    });
  });

  describe('Edge Cases and Error Handling', () => {
    const userId = 'user123';

    it('should handle empty results gracefully', async () => {
      const action = 'add';
      const items = [];

      // This should be caught by validation, but test model directly
      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(0);
      expect(result.totalCount).toBe(0);
      expect(result.results).toHaveLength(0);
    });

    it('should handle mixed item types correctly', async () => {
      const action = 'add';
      const items = [
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-426614174003' }
      ];

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(4);
      expect(result.totalCount).toBe(4);
      
      const clothingItems = result.results.filter(r => r.itemType === 'clothing');
      const outfitItems = result.results.filter(r => r.itemType === 'outfit');
      
      expect(clothingItems).toHaveLength(2);
      expect(outfitItems).toHaveLength(2);
    });

    it('should maintain transaction-like behavior on partial failures', async () => {
      const action = 'add';
      const items = [
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'outfit', itemId: '123e4567-e89b-12d3-a456-************' },
        { itemType: 'clothing', itemId: '123e4567-e89b-12d3-a456-************' }
      ];

      // Mock failure on second item only
      let callCount = 0;
      const originalMockRun = mockDB.mockRun.bind(mockDB);
      mockDB.mockRun = (query, params) => {
        if (query.includes('INSERT INTO favorites')) {
          callCount++;
          if (callCount === 2) {
            throw new Error('Database constraint violation');
          }
        }
        return originalMockRun(query, params);
      };

      const result = await favoritesModel.batchFavorites(userId, action, items);
      
      expect(result.successCount).toBe(2);
      expect(result.totalCount).toBe(3);
      expect(result.results).toHaveLength(3);
      
      expect(result.results[0].success).toBe(true);
      expect(result.results[1].success).toBe(false);
      expect(result.results[2].success).toBe(true);
    });
  });
});