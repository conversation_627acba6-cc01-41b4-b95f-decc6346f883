/**
 * 分享API测试
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { ShareModel } from '../src/models/share.js';
import { ShareService } from '../src/services/shareService.js';
import { generateShareToken } from '../src/utils/id.js';

// 模拟数据库
class MockDB {
  constructor() {
    this.shares = new Map();
    this.nextId = 1;
  }

  prepare(sql) {
    return {
      bind: (...params) => ({
        run: async () => {
          if (sql.includes('INSERT INTO shares')) {
            const [id, userId, shareType, targetId, shareToken, expiresAt, createdAt] = params;
            this.shares.set(id, {
              id, user_id: userId, share_type: shareType, target_id: targetId,
              share_token: shareToken, expires_at: expiresAt, view_count: 0,
              created_at: createdAt
            });
            return { changes: 1 };
          }
          if (sql.includes('UPDATE shares') && sql.includes('view_count')) {
            const [shareToken] = params;
            for (const [id, share] of this.shares) {
              if (share.share_token === shareToken) {
                share.view_count += 1;
                return { changes: 1 };
              }
            }
            return { changes: 0 };
          }
          if (sql.includes('DELETE FROM shares')) {
            const [shareId, userId] = params;
            const share = this.shares.get(shareId);
            if (share && share.user_id === userId) {
              this.shares.delete(shareId);
              return { changes: 1 };
            }
            return { changes: 0 };
          }
          return { changes: 0 };
        },
        first: async () => {
          if (sql.includes('SELECT * FROM shares') && sql.includes('share_token')) {
            const [shareToken] = params;
            for (const share of this.shares.values()) {
              if (share.share_token === shareToken) {
                // 检查是否过期
                if (share.expires_at && new Date(share.expires_at) < new Date()) {
                  return null;
                }
                return share;
              }
            }
            return null;
          }
          if (sql.includes('COUNT(*)')) {
            const [userId, ...restParams] = params;
            let count = 0;
            for (const share of this.shares.values()) {
              if (share.user_id === userId) {
                // 检查是否有shareType筛选
                if (sql.includes('share_type = ?') && restParams.length > 0) {
                  const shareType = restParams[0];
                  if (share.share_type === shareType) {
                    count++;
                  }
                } else {
                  count++;
                }
              }
            }
            return { total: count };
          }
          return null;
        },
        all: async () => {
          if (sql.includes('SELECT * FROM shares') && sql.includes('user_id')) {
            const [userId, ...restParams] = params;
            const userShares = [];
            for (const share of this.shares.values()) {
              if (share.user_id === userId) {
                // 检查是否有shareType筛选
                if (sql.includes('share_type = ?') && restParams.length > 0) {
                  const shareType = restParams[0];
                  if (share.share_type === shareType) {
                    userShares.push(share);
                  }
                } else {
                  userShares.push(share);
                }
              }
            }
            return userShares.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
          }
          return [];
        }
      })
    };
  }

  reset() {
    this.shares.clear();
    this.nextId = 1;
  }
}

describe('ShareModel', () => {
  let db;
  let shareModel;

  beforeEach(() => {
    db = new MockDB();
    shareModel = new ShareModel(db);
  });

  afterEach(() => {
    db.reset();
  });

  describe('createShare', () => {
    it('应该成功创建分享记录', async () => {
      const shareData = {
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken: generateShareToken(),
        expiresAt: new Date(Date.now() + 3600000).toISOString()
      };

      const result = await shareModel.createShare(shareData);

      expect(result).toMatchObject({
        userId: shareData.userId,
        shareType: shareData.shareType,
        targetId: shareData.targetId,
        shareToken: shareData.shareToken,
        viewCount: 0
      });
      expect(result.id).toBeDefined();
      expect(result.createdAt).toBeDefined();
    });
  });

  describe('getShareByToken', () => {
    it('应该根据令牌获取分享记录', async () => {
      const shareToken = generateShareToken();
      const shareData = {
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken,
        expiresAt: new Date(Date.now() + 3600000).toISOString()
      };

      await shareModel.createShare(shareData);
      const result = await shareModel.getShareByToken(shareToken);

      expect(result).toMatchObject({
        userId: shareData.userId,
        shareType: shareData.shareType,
        targetId: shareData.targetId,
        shareToken: shareData.shareToken
      });
    });

    it('应该返回null当分享不存在时', async () => {
      const result = await shareModel.getShareByToken('nonexistent');
      expect(result).toBeNull();
    });

    it('应该返回null当分享已过期时', async () => {
      const shareToken = generateShareToken();
      const shareData = {
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken,
        expiresAt: new Date(Date.now() - 3600000).toISOString() // 已过期
      };

      await shareModel.createShare(shareData);
      const result = await shareModel.getShareByToken(shareToken);

      expect(result).toBeNull();
    });
  });

  describe('getUserShares', () => {
    it('应该获取用户的分享列表', async () => {
      const userId = 'user1';
      const shareData1 = {
        userId,
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken: generateShareToken(),
        expiresAt: null
      };
      const shareData2 = {
        userId,
        shareType: 'outfit',
        targetId: 'outfit1',
        shareToken: generateShareToken(),
        expiresAt: null
      };

      await shareModel.createShare(shareData1);
      await shareModel.createShare(shareData2);

      const result = await shareModel.getUserShares(userId);

      expect(result.shares).toHaveLength(2);
      expect(result.pagination.total).toBe(2);
      expect(result.pagination.page).toBe(1);
    });

    it('应该支持按分享类型筛选', async () => {
      const userId = 'user1';
      const shareData1 = {
        userId,
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken: generateShareToken(),
        expiresAt: null
      };
      const shareData2 = {
        userId,
        shareType: 'outfit',
        targetId: 'outfit1',
        shareToken: generateShareToken(),
        expiresAt: null
      };

      await shareModel.createShare(shareData1);
      await shareModel.createShare(shareData2);

      const result = await shareModel.getUserShares(userId, { shareType: 'clothing' });

      expect(result.shares).toHaveLength(1);
      expect(result.shares[0].shareType).toBe('clothing');
    });
  });

  describe('incrementViewCount', () => {
    it('应该增加分享的访问次数', async () => {
      const shareToken = generateShareToken();
      const shareData = {
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken,
        expiresAt: null
      };

      await shareModel.createShare(shareData);
      const success = await shareModel.incrementViewCount(shareToken);

      expect(success).toBe(true);

      const share = await shareModel.getShareByToken(shareToken);
      expect(share.viewCount).toBe(1);
    });

    it('应该返回false当分享不存在时', async () => {
      const success = await shareModel.incrementViewCount('nonexistent');
      expect(success).toBe(false);
    });
  });

  describe('deleteShare', () => {
    it('应该删除用户的分享记录', async () => {
      const userId = 'user1';
      const shareData = {
        userId,
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken: generateShareToken(),
        expiresAt: null
      };

      const share = await shareModel.createShare(shareData);
      const success = await shareModel.deleteShare(share.id, userId);

      expect(success).toBe(true);

      const result = await shareModel.getShareByToken(share.shareToken);
      expect(result).toBeNull();
    });

    it('应该返回false当用户无权限删除时', async () => {
      const shareData = {
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken: generateShareToken(),
        expiresAt: null
      };

      const share = await shareModel.createShare(shareData);
      const success = await shareModel.deleteShare(share.id, 'user2');

      expect(success).toBe(false);
    });
  });
});

describe('ShareService', () => {
  let db;
  let shareService;

  beforeEach(() => {
    db = new MockDB();
    shareService = new ShareService(db);
    
    // 模拟验证方法
    shareService.getClothingById = async (id) => ({ id, userId: 'user1' });
    shareService.getOutfitById = async (id) => ({ id, userId: 'user1' });
    shareService.getCollectionById = async (id) => ({ id, userId: 'user1' });
  });

  afterEach(() => {
    db.reset();
  });

  describe('createShare', () => {
    it('应该成功创建分享', async () => {
      const params = {
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1',
        options: {
          expiresIn: 3600,
          allowPublic: true
        }
      };

      const result = await shareService.createShare(params);

      expect(result).toMatchObject({
        userId: params.userId,
        shareType: params.shareType,
        targetId: params.targetId,
        allowPublic: true,
        isExpired: false
      });
      expect(result.shareUrl).toContain(result.shareToken);
      expect(result.shortUrl).toBeDefined();
      expect(result.expiresAt).toBeDefined();
    });

    it('应该验证分享类型', async () => {
      const params = {
        userId: 'user1',
        shareType: 'invalid',
        targetId: 'item1'
      };

      await expect(shareService.createShare(params)).rejects.toThrow('不支持的分享类型');
    });
  });

  describe('getShareContent', () => {
    it('应该获取分享内容并增加访问次数', async () => {
      const shareToken = generateShareToken();
      const shareData = {
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken,
        expiresAt: null
      };

      await shareService.shareModel.createShare(shareData);
      const result = await shareService.getShareContent(shareToken);

      expect(result.share.shareType).toBe('clothing');
      expect(result.share.viewCount).toBe(1);
      expect(result.content.type).toBe('clothing');
    });

    it('应该抛出错误当分享不存在时', async () => {
      await expect(shareService.getShareContent('nonexistent')).rejects.toThrow('分享链接不存在或已过期');
    });
  });

  describe('getUserShares', () => {
    it('应该获取用户分享列表并添加URL信息', async () => {
      const userId = 'user1';
      const shareData = {
        userId,
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken: generateShareToken(),
        expiresAt: null
      };

      await shareService.shareModel.createShare(shareData);
      const result = await shareService.getUserShares(userId);

      expect(result.shares).toHaveLength(1);
      expect(result.shares[0].shareUrl).toBeDefined();
      expect(result.shares[0].isExpired).toBe(false);
    });
  });

  describe('deleteShare', () => {
    it('应该删除用户的分享', async () => {
      const userId = 'user1';
      const shareData = {
        userId,
        shareType: 'clothing',
        targetId: 'clothing1',
        shareToken: generateShareToken(),
        expiresAt: null
      };

      const share = await shareService.shareModel.createShare(shareData);
      const success = await shareService.deleteShare(share.id, userId);

      expect(success).toBe(true);
    });
  });
});