/**
 * 收藏分享功能测试
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { FavoritesModel } from '../src/models/favorites.js';
import { ShareService } from '../src/services/shareService.js';

// 模拟数据库
class MockDB {
  constructor() {
    this.favorites = new Map();
    this.shares = new Map();
    this.clothing = new Map();
    this.outfits = new Map();
    this.nextId = 1;
  }

  prepare(sql) {
    return {
      bind: (...params) => ({
        run: async () => {
          if (sql.includes('INSERT INTO favorites')) {
            const [id, userId, itemType, itemId, createdAt] = params;
            this.favorites.set(id, {
              id, user_id: userId, item_type: itemType, item_id: itemId,
              created_at: createdAt
            });
            return { success: true, changes: 1 };
          }
          if (sql.includes('INSERT INTO shares')) {
            const [id, userId, shareType, targetId, shareToken, expiresAt, createdAt] = params;
            this.shares.set(id, {
              id, user_id: userId, share_type: shareType, target_id: targetId,
              share_token: shareToken, expires_at: expiresAt, view_count: 0,
              created_at: createdAt
            });
            return { changes: 1 };
          }
          if (sql.includes('UPDATE shares') && sql.includes('view_count')) {
            const [shareToken] = params;
            for (const [id, share] of this.shares) {
              if (share.share_token === shareToken) {
                share.view_count += 1;
                return { changes: 1 };
              }
            }
            return { changes: 0 };
          }
          if (sql.includes('DELETE FROM shares')) {
            const [shareId, userId] = params;
            const share = this.shares.get(shareId);
            if (share && share.user_id === userId) {
              this.shares.delete(shareId);
              return { changes: 1 };
            }
            return { changes: 0 };
          }
          return { changes: 0 };
        },
        first: async () => {
          if (sql.includes('SELECT id FROM favorites')) {
            const [userId, itemType, itemId] = params;
            for (const favorite of this.favorites.values()) {
              if (favorite.user_id === userId && 
                  favorite.item_type === itemType && 
                  favorite.item_id === itemId) {
                return favorite;
              }
            }
            return null;
          }
          if (sql.includes('SELECT id, user_id FROM clothing')) {
            const [itemId] = params;
            const clothing = this.clothing.get(itemId);
            return clothing ? { id: clothing.id, user_id: clothing.user_id } : null;
          }
          if (sql.includes('SELECT id, user_id FROM outfits')) {
            const [itemId] = params;
            const outfit = this.outfits.get(itemId);
            return outfit ? { id: outfit.id, user_id: outfit.user_id } : null;
          }
          if (sql.includes('SELECT * FROM shares') && sql.includes('share_token')) {
            const [shareToken] = params;
            for (const share of this.shares.values()) {
              if (share.share_token === shareToken) {
                // 检查是否过期
                if (share.expires_at && new Date(share.expires_at) < new Date()) {
                  return null;
                }
                return share;
              }
            }
            return null;
          }
          if (sql.includes('COUNT(*)')) {
            const [userId, ...restParams] = params;
            let count = 0;
            for (const share of this.shares.values()) {
              if (share.user_id === userId) {
                // 检查是否有shareType筛选
                if (sql.includes('share_type = ?') && restParams.length > 0) {
                  const shareType = restParams[0];
                  if (share.share_type === shareType) {
                    count++;
                  }
                } else {
                  count++;
                }
              }
            }
            return { total: count };
          }
          return null;
        },
        all: async () => {
          if (sql.includes('SELECT * FROM shares') && sql.includes('user_id')) {
            const [userId, ...restParams] = params;
            const userShares = [];
            for (const share of this.shares.values()) {
              if (share.user_id === userId) {
                // 检查是否有shareType筛选
                if (sql.includes('share_type = ?') && restParams.length > 0) {
                  const shareType = restParams[0];
                  if (share.share_type === shareType) {
                    userShares.push(share);
                  }
                } else {
                  userShares.push(share);
                }
              }
            }
            return userShares.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
          }
          if (sql.includes('SELECT item_id, 1 as is_favorited')) {
            const [userId, itemType, ...itemIds] = params;
            const results = [];
            for (const favorite of this.favorites.values()) {
              if (favorite.user_id === userId && 
                  favorite.item_type === itemType && 
                  itemIds.includes(favorite.item_id)) {
                results.push({ item_id: favorite.item_id, is_favorited: 1 });
              }
            }
            return results;
          }
          return [];
        }
      })
    };
  }

  reset() {
    this.favorites.clear();
    this.shares.clear();
    this.clothing.clear();
    this.outfits.clear();
    this.nextId = 1;
  }

  // 辅助方法：添加测试数据
  addClothing(id, userId, name = 'Test Clothing') {
    this.clothing.set(id, { id, user_id: userId, name });
  }

  addOutfit(id, userId, name = 'Test Outfit') {
    this.outfits.set(id, { id, user_id: userId, name });
  }

  addFavorite(userId, itemType, itemId) {
    const id = `fav_${this.nextId++}`;
    this.favorites.set(id, {
      id, user_id: userId, item_type: itemType, item_id: itemId,
      created_at: new Date().toISOString()
    });
    return id;
  }
}

describe('收藏分享功能', () => {
  let db;
  let shareService;
  let favoritesModel;

  beforeEach(() => {
    db = new MockDB();
    shareService = new ShareService(db);
    favoritesModel = new FavoritesModel(db);

    // 添加测试数据
    db.addClothing('clothing1', 'user1', '测试衣物');
    db.addOutfit('outfit1', 'user1', '测试搭配');
    db.addFavorite('user1', 'clothing', 'clothing1');
    db.addFavorite('user1', 'outfit', 'outfit1');
  });

  afterEach(() => {
    db.reset();
  });

  describe('ShareService', () => {
    it('应该成功创建衣物分享', async () => {
      const share = await shareService.createShare({
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1',
        options: {
          expiresIn: 3600,
          allowPublic: true
        }
      });

      expect(share.shareType).toBe('clothing');
      expect(share.targetId).toBe('clothing1');
      expect(share.userId).toBe('user1');
      expect(share.shareUrl).toBeDefined();
      expect(share.shortUrl).toBeDefined();
      expect(share.allowPublic).toBe(true);
    });

    it('应该成功创建搭配分享', async () => {
      const share = await shareService.createShare({
        userId: 'user1',
        shareType: 'outfit',
        targetId: 'outfit1',
        options: {
          expiresIn: 7200,
          allowPublic: false
        }
      });

      expect(share.shareType).toBe('outfit');
      expect(share.targetId).toBe('outfit1');
      expect(share.userId).toBe('user1');
      expect(share.allowPublic).toBe(false);
    });

    it('应该拒绝分享不存在的项目', async () => {
      await expect(shareService.createShare({
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'nonexistent'
      })).rejects.toThrow('衣物不存在或无权限分享');
    });

    it('应该拒绝分享其他用户的项目', async () => {
      db.addClothing('clothing2', 'user2', '其他用户的衣物');

      await expect(shareService.createShare({
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing2'
      })).rejects.toThrow('衣物不存在或无权限分享');
    });

    it('应该获取分享内容', async () => {
      const share = await shareService.createShare({
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1'
      });

      const content = await shareService.getShareContent(share.shareToken);

      expect(content.share.shareType).toBe('clothing');
      expect(content.share.viewCount).toBe(1);
      expect(content.content.type).toBe('clothing');
    });

    it('应该获取用户分享列表', async () => {
      await shareService.createShare({
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1'
      });
      await shareService.createShare({
        userId: 'user1',
        shareType: 'outfit',
        targetId: 'outfit1'
      });

      const result = await shareService.getUserShares('user1');

      expect(result.shares).toHaveLength(2);
      expect(result.shares.every(share => share.shareUrl)).toBe(true);
      expect(result.shares.every(share => share.isExpired === false)).toBe(true);
    });

    it('应该支持按类型筛选分享', async () => {
      await shareService.createShare({
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1'
      });
      await shareService.createShare({
        userId: 'user1',
        shareType: 'outfit',
        targetId: 'outfit1'
      });

      const result = await shareService.getUserShares('user1', {
        shareType: 'clothing'
      });

      expect(result.shares).toHaveLength(1);
      expect(result.shares[0].shareType).toBe('clothing');
    });

    it('应该删除分享', async () => {
      const share = await shareService.createShare({
        userId: 'user1',
        shareType: 'clothing',
        targetId: 'clothing1'
      });

      const success = await shareService.deleteShare(share.id, 'user1');
      expect(success).toBe(true);

      await expect(shareService.getShareContent(share.shareToken))
        .rejects.toThrow('分享链接不存在或已过期');
    });
  });

  describe('FavoritesModel', () => {
    it('应该检查收藏状态', async () => {
      const isFavorited = await favoritesModel.isFavorited('user1', 'clothing', 'clothing1');
      expect(isFavorited).toBe(true);

      const isNotFavorited = await favoritesModel.isFavorited('user1', 'clothing', 'clothing2');
      expect(isNotFavorited).toBe(false);
    });

    it('应该批量检查收藏状态', async () => {
      db.addClothing('clothing2', 'user1', '测试衣物2');
      
      const statusMap = await favoritesModel.checkFavoriteStatus('user1', 'clothing', ['clothing1', 'clothing2']);
      
      expect(statusMap['clothing1']).toBe(true);
      expect(statusMap['clothing2']).toBe(false);
    });
  });
});