/**
 * 收藏集合API测试
 */

import { describe, it, expect, beforeEach, afterEach } from 'vitest';
import { Collection, CollectionDAO, CollectionItem, CollectionItemDAO } from '../src/models/collection.js';

// 模拟数据库
class MockDB {
  constructor() {
    this.collections = new Map();
    this.collectionItems = new Map();
  }

  prepare(query) {
    const self = this;
    return {
      bind: (...params) => {
        self.lastParams = params;
        return {
          run: async () => {
            if (query.includes('INSERT INTO collections')) {
              const id = self.lastParams[0];
              self.collections.set(id, {
                id: self.lastParams[0],
                user_id: self.lastParams[1],
                name: self.lastParams[2],
                description: self.lastParams[3],
                is_public: self.lastParams[4],
                cover_image_url: self.lastParams[5],
                created_at: self.lastParams[6],
                updated_at: self.lastParams[7]
              });
              return { success: true };
            }
            
            if (query.includes('INSERT INTO collection_items') || query.includes('INSERT OR IGNORE INTO collection_items')) {
              const key = `${self.lastParams[0]}-${self.lastParams[1]}-${self.lastParams[2]}`;
              self.collectionItems.set(key, {
                collection_id: self.lastParams[0],
                item_type: self.lastParams[1],
                item_id: self.lastParams[2],
                added_at: self.lastParams[3]
              });
              return { success: true };
            }
            
            if (query.includes('UPDATE collections')) {
              const id = self.lastParams[5];
              if (self.collections.has(id)) {
                const existing = self.collections.get(id);
                self.collections.set(id, {
                  ...existing,
                  name: self.lastParams[0],
                  description: self.lastParams[1],
                  is_public: self.lastParams[2],
                  cover_image_url: self.lastParams[3],
                  updated_at: self.lastParams[4]
                });
                return { success: true };
              }
              return { success: false };
            }
            
            if (query.includes('DELETE FROM collections')) {
              const id = self.lastParams[0];
              self.collections.delete(id);
              return { success: true };
            }
            
            if (query.includes('DELETE FROM collection_items')) {
              if (self.lastParams.length === 1) {
                const collectionId = self.lastParams[0];
                for (const [key, item] of self.collectionItems.entries()) {
                  if (item.collection_id === collectionId) {
                    self.collectionItems.delete(key);
                  }
                }
              } else {
                const key = `${self.lastParams[0]}-${self.lastParams[1]}-${self.lastParams[2]}`;
                const existed = self.collectionItems.has(key);
                self.collectionItems.delete(key);
                return { success: true, changes: existed ? 1 : 0 };
              }
              return { success: true };
            }
            
            return { success: true };
          },
          first: async () => {
            if (query.includes('SELECT * FROM collections WHERE id')) {
              const id = self.lastParams[0];
              return self.collections.get(id) || null;
            }
            
            if (query.includes('SELECT id FROM collections WHERE id = ? AND user_id')) {
              const id = self.lastParams[0];
              const userId = self.lastParams[1];
              const collection = self.collections.get(id);
              return (collection && collection.user_id === userId) ? { id } : null;
            }
            
            if (query.includes('COUNT(*) as total_collections')) {
              const userId = self.lastParams[0];
              let total = 0, publicCount = 0, privateCount = 0;
              
              for (const collection of self.collections.values()) {
                if (collection.user_id === userId) {
                  total++;
                  if (collection.is_public) {
                    publicCount++;
                  } else {
                    privateCount++;
                  }
                }
              }
              
              return {
                total_collections: total,
                public_collections: publicCount,
                private_collections: privateCount
              };
            }
            
            if (query.includes('COUNT(*) as total_items')) {
              const collectionId = self.lastParams[0];
              let total = 0, clothingCount = 0, outfitCount = 0;
              
              for (const item of self.collectionItems.values()) {
                if (item.collection_id === collectionId) {
                  total++;
                  if (item.item_type === 'clothing') {
                    clothingCount++;
                  } else if (item.item_type === 'outfit') {
                    outfitCount++;
                  }
                }
              }
              
              return {
                total_items: total,
                clothing_count: clothingCount,
                outfit_count: outfitCount
              };
            }
            
            if (query.includes('SELECT 1 FROM collection_items')) {
              const key = `${self.lastParams[0]}-${self.lastParams[1]}-${self.lastParams[2]}`;
              return self.collectionItems.has(key) ? { 1: 1 } : null;
            }
            
            return null;
          },
          all: async () => {
            if (query.includes('SELECT * FROM collections')) {
              const userId = self.lastParams[0];
              const results = [];
              
              for (const collection of self.collections.values()) {
                if (collection.user_id === userId) {
                  results.push(collection);
                }
              }
              
              return results.sort((a, b) => new Date(b.created_at) - new Date(a.created_at));
            }
            
            if (query.includes('SELECT ci.*')) {
              const collectionId = self.lastParams[0];
              const results = [];
              
              for (const item of self.collectionItems.values()) {
                if (item.collection_id === collectionId) {
                  results.push({
                    ...item,
                    item_name: `Mock Item ${item.item_id}`,
                    item_category: item.item_type === 'clothing' ? 'tops' : 'casual'
                  });
                }
              }
              
              return results.sort((a, b) => new Date(b.added_at) - new Date(a.added_at));
            }
            
            return [];
          }
        };
      }
    };
  }

  reset() {
    this.collections.clear();
    this.collectionItems.clear();
  }
}

describe('Collection Model', () => {
  let mockDB;
  let collectionDAO;
  let collectionItemDAO;

  beforeEach(() => {
    mockDB = new MockDB();
    collectionDAO = new CollectionDAO(mockDB);
    collectionItemDAO = new CollectionItemDAO(mockDB);
  });

  afterEach(() => {
    mockDB.reset();
  });

  describe('Collection类', () => {
    it('应该创建有效的Collection实例', () => {
      const collection = new Collection({
        user_id: 'user123',
        name: '我的夏日收藏',
        description: '夏季服装收藏'
      });

      expect(collection.user_id).toBe('user123');
      expect(collection.name).toBe('我的夏日收藏');
      expect(collection.description).toBe('夏季服装收藏');
      expect(collection.is_public).toBe(false);
      expect(collection.id).toBeDefined();
    });

    it('应该验证必填字段', () => {
      const collection = new Collection({});
      const validation = collection.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('用户ID不能为空');
      expect(validation.errors).toContain('集合名称不能为空');
    });

    it('应该验证字段长度', () => {
      const collection = new Collection({
        user_id: 'user123',
        name: 'a'.repeat(101),
        description: 'b'.repeat(501)
      });

      const validation = collection.validate();
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('集合名称不能超过100个字符');
      expect(validation.errors).toContain('集合描述不能超过500个字符');
    });

    it('应该正确转换为数据库格式', () => {
      const collection = new Collection({
        user_id: 'user123',
        name: '  我的收藏  ',
        description: '测试描述'
      });

      const dbFormat = collection.toDbFormat();
      expect(dbFormat.name).toBe('我的收藏');
      expect(dbFormat.updated_at).toBeDefined();
    });
  });

  describe('CollectionDAO', () => {
    it('应该成功创建收藏集合', async () => {
      const collection = new Collection({
        user_id: 'user123',
        name: '我的夏日收藏',
        description: '夏季服装收藏'
      });

      const result = await collectionDAO.create(collection);
      
      expect(result).toBeDefined();
      expect(result.name).toBe('我的夏日收藏');
      expect(result.user_id).toBe('user123');
    });

    it('应该拒绝无效数据', async () => {
      const collection = new Collection({
        name: '',
        user_id: 'user123'
      });

      await expect(collectionDAO.create(collection)).rejects.toThrow('数据验证失败');
    });

    it('应该根据ID查找收藏集合', async () => {
      const collection = new Collection({
        user_id: 'user123',
        name: '测试集合'
      });

      await collectionDAO.create(collection);
      const found = await collectionDAO.findById(collection.id);

      expect(found).toBeDefined();
      expect(found.name).toBe('测试集合');
    });

    it('应该根据用户ID获取收藏集合列表', async () => {
      const collection1 = new Collection({
        user_id: 'user123',
        name: '集合1'
      });
      const collection2 = new Collection({
        user_id: 'user123',
        name: '集合2'
      });

      await collectionDAO.create(collection1);
      await collectionDAO.create(collection2);

      const collections = await collectionDAO.findByUserId('user123');
      expect(collections).toHaveLength(2);
    });

    it('应该成功更新收藏集合', async () => {
      const collection = new Collection({
        user_id: 'user123',
        name: '原始名称'
      });

      await collectionDAO.create(collection);
      
      const updated = await collectionDAO.update(collection.id, {
        name: '更新后的名称',
        description: '新描述'
      });

      expect(updated.name).toBe('更新后的名称');
      expect(updated.description).toBe('新描述');
    });

    it('应该成功删除收藏集合', async () => {
      const collection = new Collection({
        user_id: 'user123',
        name: '待删除集合'
      });

      await collectionDAO.create(collection);
      const result = await collectionDAO.delete(collection.id);

      expect(result).toBe(true);
      
      const found = await collectionDAO.findById(collection.id);
      expect(found).toBeNull();
    });

    it('应该检查用户所有权', async () => {
      const collection = new Collection({
        user_id: 'user123',
        name: '测试集合'
      });

      await collectionDAO.create(collection);

      const hasOwnership = await collectionDAO.checkOwnership(collection.id, 'user123');
      expect(hasOwnership).toBe(true);

      const noOwnership = await collectionDAO.checkOwnership(collection.id, 'user456');
      expect(noOwnership).toBe(false);
    });

    it('应该获取收藏集合统计信息', async () => {
      const collection1 = new Collection({
        user_id: 'user123',
        name: '公开集合',
        is_public: true
      });
      const collection2 = new Collection({
        user_id: 'user123',
        name: '私有集合',
        is_public: false
      });

      await collectionDAO.create(collection1);
      await collectionDAO.create(collection2);

      const stats = await collectionDAO.getStats('user123');
      expect(stats.totalCollections).toBe(2);
      expect(stats.publicCollections).toBe(1);
      expect(stats.privateCollections).toBe(1);
    });
  });

  describe('CollectionItem类', () => {
    it('应该创建有效的CollectionItem实例', () => {
      const item = new CollectionItem({
        collection_id: 'collection123',
        item_type: 'clothing',
        item_id: 'item123'
      });

      expect(item.collection_id).toBe('collection123');
      expect(item.item_type).toBe('clothing');
      expect(item.item_id).toBe('item123');
      expect(item.added_at).toBeDefined();
    });

    it('应该验证必填字段', () => {
      const item = new CollectionItem({});
      const validation = item.validate();

      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('集合ID不能为空');
      expect(validation.errors).toContain('项目类型不能为空');
      expect(validation.errors).toContain('项目ID不能为空');
    });

    it('应该验证项目类型', () => {
      const item = new CollectionItem({
        collection_id: 'collection123',
        item_type: 'invalid',
        item_id: 'item123'
      });

      const validation = item.validate();
      expect(validation.isValid).toBe(false);
      expect(validation.errors).toContain('项目类型必须是 clothing 或 outfit');
    });
  });

  describe('CollectionItemDAO', () => {
    let collectionId;

    beforeEach(async () => {
      const collection = new Collection({
        user_id: 'user123',
        name: '测试集合'
      });
      await collectionDAO.create(collection);
      collectionId = collection.id;
    });

    it('应该成功添加项目到集合', async () => {
      const item = new CollectionItem({
        collection_id: collectionId,
        item_type: 'clothing',
        item_id: 'clothing123'
      });

      const result = await collectionItemDAO.addItem(item);
      expect(result).toBe(true);
    });

    it('应该批量添加项目到集合', async () => {
      const items = [
        { itemType: 'clothing', itemId: 'clothing1' },
        { itemType: 'outfit', itemId: 'outfit1' }
      ];

      const result = await collectionItemDAO.addItems(collectionId, items);
      expect(result).toBe(true);
    });

    it('应该成功移除项目', async () => {
      const item = new CollectionItem({
        collection_id: collectionId,
        item_type: 'clothing',
        item_id: 'clothing123'
      });

      await collectionItemDAO.addItem(item);
      const removed = await collectionItemDAO.removeItem(collectionId, 'clothing', 'clothing123');
      
      expect(removed).toBe(true);
    });

    it('应该批量移除项目', async () => {
      const items = [
        { itemType: 'clothing', itemId: 'clothing1' },
        { itemType: 'outfit', itemId: 'outfit1' }
      ];

      await collectionItemDAO.addItems(collectionId, items);
      const removedCount = await collectionItemDAO.removeItems(collectionId, items);
      
      expect(removedCount).toBe(2);
    });

    it('应该获取集合项目列表', async () => {
      const items = [
        { itemType: 'clothing', itemId: 'clothing1' },
        { itemType: 'outfit', itemId: 'outfit1' }
      ];

      await collectionItemDAO.addItems(collectionId, items);
      const itemList = await collectionItemDAO.getItems(collectionId);
      
      expect(itemList).toHaveLength(2);
      expect(itemList[0].itemName).toBeDefined();
    });

    it('应该获取项目统计信息', async () => {
      const items = [
        { itemType: 'clothing', itemId: 'clothing1' },
        { itemType: 'clothing', itemId: 'clothing2' },
        { itemType: 'outfit', itemId: 'outfit1' }
      ];

      await collectionItemDAO.addItems(collectionId, items);
      const stats = await collectionItemDAO.getItemStats(collectionId);
      
      expect(stats.totalItems).toBe(3);
      expect(stats.clothingCount).toBe(2);
      expect(stats.outfitCount).toBe(1);
    });

    it('应该检查项目是否在集合中', async () => {
      const item = new CollectionItem({
        collection_id: collectionId,
        item_type: 'clothing',
        item_id: 'clothing123'
      });

      await collectionItemDAO.addItem(item);
      
      const isInCollection = await collectionItemDAO.isItemInCollection(collectionId, 'clothing', 'clothing123');
      expect(isInCollection).toBe(true);

      const notInCollection = await collectionItemDAO.isItemInCollection(collectionId, 'clothing', 'clothing456');
      expect(notInCollection).toBe(false);
    });
  });
});