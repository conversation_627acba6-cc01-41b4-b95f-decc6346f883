# Yigui Backend API Documentation

This document provides detailed information about all API endpoints available in the Yigui backend service.

## Base URL

- **Development**: `http://localhost:8787`
- **Production**: `https://yigui-backend.your-domain.workers.dev`

## Authentication

Most endpoints require authentication using JWT tokens. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Response Format

All API responses follow a consistent JSON format:

**Success Response**:
```json
{
  "success": true,
  "message": "Operation completed successfully",
  "data": { ... }
}
```

**Error Response**:
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE",
  "details": [ ... ] // Optional validation details
}
```

## Health Check

### `GET /api/health`

Check the service health status.

- **Authentication**: None required
- **Description**: Returns service status and timestamp

**Response:**
```json
{
  "status": "ok",
  "timestamp": "2024-01-01T00:00:00.000Z",
  "service": "yigui-backend"
}
```

---

## Authentication Endpoints

### `POST /api/auth/register`

Register a new user account.

- **Authentication**: None required
- **Description**: Creates a new user account and returns authentication tokens

**Request:**
- **Headers**: 
  - `Content-Type: application/json`
- **Body**:
```json
{
  "username": "string (3-20 chars, alphanumeric + underscore + Chinese)",
  "email": "string (valid email, max 100 chars)",
  "password": "string (6-50 chars)"
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "注册成功",
  "data": {
    "user": {
      "id": "uuid",
      "username": "string",
      "email": "string",
      "created_at": "ISO 8601 timestamp"
    },
    "token": "jwt-access-token",
    "refreshToken": "jwt-refresh-token"
  }
}
```
- **400 Bad Request**: Validation errors
- **409 Conflict**: Email or username already exists

### `POST /api/auth/login`

Authenticate user and get access tokens.

- **Authentication**: None required
- **Description**: Validates user credentials and returns authentication tokens

**Request:**
- **Headers**: 
  - `Content-Type: application/json`
- **Body**:
```json
{
  "email": "string (valid email)",
  "password": "string"
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "user": {
      "id": "uuid",
      "username": "string",
      "email": "string",
      "created_at": "ISO 8601 timestamp",
      "updated_at": "ISO 8601 timestamp"
    },
    "token": "jwt-access-token",
    "refreshToken": "jwt-refresh-token"
  }
}
```
- **401 Unauthorized**: Invalid credentials

### `POST /api/auth/refresh`

Refresh access token using refresh token.

- **Authentication**: None required (uses refresh token in body)
- **Description**: Generates new access and refresh tokens

**Request:**
- **Headers**: 
  - `Content-Type: application/json`
- **Body**:
```json
{
  "refreshToken": "jwt-refresh-token"
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Token刷新成功",
  "data": {
    "token": "new-jwt-access-token",
    "refreshToken": "new-jwt-refresh-token"
  }
}
```
- **400 Bad Request**: Missing refresh token
- **401 Unauthorized**: Invalid or expired refresh token

### `GET /api/auth/profile`

Get current user profile information.

- **Authentication**: Required
- **Description**: Returns the authenticated user's profile data

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "created_at": "ISO 8601 timestamp",
    "updated_at": "ISO 8601 timestamp"
  }
}
```
- **401 Unauthorized**: Invalid or missing token
- **404 Not Found**: User not found

### `PUT /api/auth/profile`

Update user profile information.

- **Authentication**: Required
- **Description**: Updates username and/or password for the authenticated user

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Body**:
```json
{
  "username": "string (optional, 3-20 chars)",
  "currentPassword": "string (required if changing password)",
  "newPassword": "string (optional, 6-50 chars)"
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "资料更新成功",
  "data": {
    "id": "uuid",
    "username": "string",
    "email": "string",
    "created_at": "ISO 8601 timestamp",
    "updated_at": "ISO 8601 timestamp"
  }
}
```
- **400 Bad Request**: Validation errors or incorrect current password
- **401 Unauthorized**: Invalid or missing token
- **409 Conflict**: Username already taken

---

*Note: This document will be extended with clothing, outfit, and upload endpoint documentation in the next implementation phase.*## Clot
hing Management Endpoints

### `GET /api/clothing`

Get a paginated list of clothing items for the authenticated user.

- **Authentication**: Required
- **Description**: Returns clothing items with optional filtering and pagination

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `page` (optional): Page number (default: 1)
  - `pageSize` (optional): Items per page (default: 20, max: 100)
  - `category` (optional): Filter by category
  - `subcategory` (optional): Filter by subcategory
  - `color` (optional): Filter by color
  - `season` (optional): Filter by season
  - `brand` (optional): Filter by brand
  - `search` (optional): Search in name and description

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "uuid",
        "user_id": "uuid",
        "name": "string",
        "category": "string",
        "subcategory": "string",
        "type": "string",
        "brand": "string",
        "color": "string",
        "size": "string",
        "season": "string",
        "purchase_date": "YYYY-MM-DD",
        "price": 99.99,
        "description": "string",
        "created_at": "ISO 8601 timestamp",
        "updated_at": "ISO 8601 timestamp",
        "imageUrls": ["url1", "url2"]
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "totalPages": 3
  }
}
```
- **401 Unauthorized**: Invalid or missing token

### `POST /api/clothing`

Create a new clothing item.

- **Authentication**: Required
- **Description**: Creates a new clothing item with optional images

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Body**:
```json
{
  "name": "string (1-100 chars, required)",
  "category": "string (required)",
  "subcategory": "string (required)",
  "type": "string (required)",
  "brand": "string (optional, max 50 chars)",
  "color": "string (required)",
  "size": "string (required)",
  "season": "string (required)",
  "purchaseDate": "YYYY-MM-DD (optional)",
  "price": 99.99,
  "description": "string (optional, max 500 chars)",
  "imageUrls": ["url1", "url2"]
}
```

**Response:**
- **200 OK**: Returns the created clothing item (same structure as GET)
- **400 Bad Request**: Validation errors
- **401 Unauthorized**: Invalid or missing token

### `GET /api/clothing/:id`

Get a specific clothing item by ID.

- **Authentication**: Required
- **Description**: Returns detailed information about a specific clothing item

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Parameters**:
  - `id`: Clothing item UUID

**Response:**
- **200 OK**: Returns the clothing item (same structure as POST response)
- **401 Unauthorized**: Invalid or missing token
- **404 Not Found**: Clothing item not found or doesn't belong to user

### `PUT /api/clothing/:id`

Update a specific clothing item.

- **Authentication**: Required
- **Description**: Updates an existing clothing item (partial updates allowed)

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Parameters**:
  - `id`: Clothing item UUID
- **Body**: Same as POST, but all fields are optional

**Response:**
- **200 OK**: Returns the updated clothing item
- **400 Bad Request**: Validation errors
- **401 Unauthorized**: Invalid or missing token
- **404 Not Found**: Clothing item not found

### `DELETE /api/clothing/:id`

Delete a specific clothing item.

- **Authentication**: Required
- **Description**: Permanently deletes a clothing item and associated images

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Parameters**:
  - `id`: Clothing item UUID

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "衣物删除成功",
  "data": null
}
```
- **401 Unauthorized**: Invalid or missing token
- **404 Not Found**: Clothing item not found

---

## Outfit Management Endpoints

### `GET /api/outfits`

Get a paginated list of outfits for the authenticated user.

- **Authentication**: Required
- **Description**: Returns outfits with optional filtering and pagination

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `page` (optional): Page number (default: 1)
  - `pageSize` (optional): Items per page (default: 20, max: 100)
  - `occasion` (optional): Filter by occasion
  - `search` (optional): Search in name and notes

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "uuid",
        "user_id": "uuid",
        "name": "string",
        "occasion": "string",
        "notes": "string",
        "created_at": "ISO 8601 timestamp",
        "updated_at": "ISO 8601 timestamp",
        "itemCount": 3,
        "previewImage": "url"
      }
    ],
    "total": 25,
    "page": 1,
    "pageSize": 20,
    "totalPages": 2
  }
}
```

### `POST /api/outfits`

Create a new outfit.

- **Authentication**: Required
- **Description**: Creates a new outfit with associated clothing items

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Body**:
```json
{
  "name": "string (1-100 chars, required)",
  "occasion": "string (optional, max 50 chars)",
  "notes": "string (optional, max 500 chars)",
  "clothingItems": ["clothing-uuid1", "clothing-uuid2"]
}
```

**Response:**
- **200 OK**: Returns the created outfit with full clothing item details
- **400 Bad Request**: Validation errors or clothing items don't belong to user

### `GET /api/outfits/:id`

Get a specific outfit by ID.

- **Authentication**: Required
- **Description**: Returns detailed information about a specific outfit including all clothing items

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Parameters**:
  - `id`: Outfit UUID

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "id": "uuid",
    "user_id": "uuid",
    "name": "string",
    "occasion": "string",
    "notes": "string",
    "created_at": "ISO 8601 timestamp",
    "updated_at": "ISO 8601 timestamp",
    "clothingItems": [
      {
        "id": "uuid",
        "name": "string",
        "category": "string",
        "color": "string",
        "imageUrls": ["url1", "url2"]
      }
    ]
  }
}
```

### `PUT /api/outfits/:id`

Update a specific outfit.

- **Authentication**: Required
- **Description**: Updates an existing outfit (partial updates allowed)

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Parameters**:
  - `id`: Outfit UUID
- **Body**: Same as POST, but all fields are optional

**Response:**
- **200 OK**: Returns the updated outfit with full details

### `DELETE /api/outfits/:id`

Delete a specific outfit.

- **Authentication**: Required
- **Description**: Permanently deletes an outfit and its clothing associations

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Parameters**:
  - `id`: Outfit UUID

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "穿搭删除成功",
  "data": null
}
```

### `GET /api/outfits/stats`

Get outfit statistics for the authenticated user.

- **Authentication**: Required
- **Description**: Returns statistics about user's outfits by occasion

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "total": 25,
    "casual": 10,
    "work": 8,
    "formal": 3,
    "party": 2,
    "date": 1,
    "sport": 1
  }
}
```

### `GET /api/outfits/recommendations`

Get outfit recommendations for the authenticated user.

- **Authentication**: Required
- **Description**: Returns recommended outfits based on occasion and usage patterns

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `occasion` (optional): Filter recommendations by occasion
  - `limit` (optional): Number of recommendations (default: 10)

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "uuid",
        "name": "string",
        "occasion": "string",
        "item_count": 3
      }
    ]
  }
}
```

### `POST /api/outfits/:id/items`

Add a clothing item to an existing outfit.

- **Authentication**: Required
- **Description**: Adds a single clothing item to an outfit

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Parameters**:
  - `id`: Outfit UUID
- **Body**:
```json
{
  "clothingId": "clothing-uuid"
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "衣物添加成功",
  "data": null
}
```

### `DELETE /api/outfits/:id/items/:clothingId`

Remove a clothing item from an outfit.

- **Authentication**: Required
- **Description**: Removes a specific clothing item from an outfit

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Parameters**:
  - `id`: Outfit UUID
  - `clothingId`: Clothing item UUID

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "衣物移除成功",
  "data": null
}
```

---

## File Upload Endpoints

### `GET /api/upload/signature`

Get a presigned URL for file upload.

- **Authentication**: Required
- **Description**: Returns a presigned URL for direct upload to R2 storage

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `fileName`: Original file name
  - `fileType`: MIME type (image/jpeg, image/png, image/gif, image/webp)

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "uploadUrl": "presigned-url-or-null",
    "key": "user-id/timestamp-uuid.ext",
    "expires": "ISO 8601 timestamp",
    "fileUrl": "https://pub-account.r2.dev/yigui-images/key",
    "useDirectUpload": false
  }
}
```
- **400 Bad Request**: Missing parameters or unsupported file type

### `POST /api/upload`

Direct upload file to R2 storage.

- **Authentication**: Required
- **Description**: Directly uploads a file to R2 storage (alternative to presigned URL)

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: multipart/form-data`
- **Body**: 
  - `file`: Image file (max 10MB, supported types: jpeg, png, gif, webp)

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "文件上传成功",
  "data": {
    "url": "https://pub-account.r2.dev/yigui-images/key",
    "key": "user-id/timestamp-uuid.ext",
    "size": 1024000,
    "type": "image/jpeg",
    "originalName": "photo.jpg"
  }
}
```
- **400 Bad Request**: No file, unsupported type, or file too large

### `DELETE /api/upload/:key`

Delete a file from R2 storage.

- **Authentication**: Required
- **Description**: Deletes a file from R2 storage (user can only delete their own files)

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Parameters**:
  - `key`: File key (must start with user ID)

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "文件删除成功",
  "data": null
}
```
- **400 Bad Request**: Missing file key
- **403 Forbidden**: File doesn't belong to user

---

## Error Codes

Common error codes returned by the API:

- `BAD_REQUEST`: Invalid request parameters or body
- `UNAUTHORIZED`: Missing or invalid authentication token
- `FORBIDDEN`: User doesn't have permission for the requested resource
- `NOT_FOUND`: Requested resource doesn't exist
- `VALIDATION_ERROR`: Request data failed validation (includes details array)
- `SERVER_ERROR`: Internal server error

---

## Favorites Management Endpoints

### `POST /api/favorites/toggle`

Toggle favorite status for a clothing item or outfit.

- **Authentication**: Required
- **Description**: Adds or removes an item from the user's favorites

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Body**:
```json
{
  "itemType": "clothing|outfit",
  "itemId": "uuid"
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "已添加到收藏|已从收藏中移除",
  "data": {
    "itemType": "clothing",
    "itemId": "uuid",
    "isFavorited": true,
    "action": "added|removed"
  }
}
```
- **400 Bad Request**: Validation errors
- **404 Not Found**: Item not found or no access permission

### `GET /api/favorites`

Get paginated list of user's favorite items.

- **Authentication**: Required
- **Description**: Returns user's favorites with optional filtering and pagination

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `type` (optional): Filter by item type (clothing|outfit)
  - `page` (optional): Page number (default: 1)
  - `pageSize` (optional): Items per page (default: 20, max: 100)
  - `category` (optional): Filter by category (for clothing items)
  - `sortBy` (optional): Sort field (created_at|name, default: created_at)
  - `order` (optional): Sort order (asc|desc, default: desc)

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "uuid",
        "itemType": "clothing|outfit",
        "itemId": "uuid",
        "favoritedAt": "ISO 8601 timestamp",
        "details": {
          "id": "uuid",
          "name": "string",
          "category": "string",
          "color": "string",
          "imageUrls": ["url1", "url2"]
        }
      }
    ],
    "total": 50,
    "page": 1,
    "pageSize": 20,
    "totalPages": 3
  }
}
```

### `POST /api/favorites/batch`

Batch add or remove multiple items from favorites.

- **Authentication**: Required
- **Description**: Performs batch favorite operations on multiple items

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Body**:
```json
{
  "action": "add|remove",
  "items": [
    {
      "itemType": "clothing|outfit",
      "itemId": "uuid"
    }
  ]
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "成功添加 X/Y 个项目到收藏",
  "data": {
    "results": [
      {
        "itemType": "clothing",
        "itemId": "uuid",
        "success": true,
        "action": "added|removed|already_exists"
      }
    ],
    "successCount": 5,
    "totalCount": 6
  }
}
```
- **400 Bad Request**: Validation errors (max 50 items per batch)

### `GET /api/favorites/status`

Check favorite status for multiple items.

- **Authentication**: Required
- **Description**: Returns favorite status for specified items

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `itemType`: Item type (clothing|outfit)
  - `itemIds`: Comma-separated list of item UUIDs (max 100)

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "itemType": "clothing",
    "statusMap": {
      "uuid1": true,
      "uuid2": false,
      "uuid3": true
    }
  }
}
```

### `GET /api/favorites/stats`

Get user's favorite statistics.

- **Authentication**: Required
- **Description**: Returns statistics about user's favorites

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "total": 25,
    "clothingCount": 15,
    "outfitCount": 10,
    "dailyActivity": [
      {
        "date": "2024-01-01",
        "daily_count": 3
      }
    ],
    "categoryDistribution": [
      {
        "category": "tops",
        "count": 8
      }
    ]
  }
}
```

---

## Collections Management Endpoints

### `GET /api/collections`

Get paginated list of user's collections.

- **Authentication**: Required
- **Description**: Returns user's collections with optional filtering and pagination

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `page` (optional): Page number (default: 1)
  - `pageSize` (optional): Items per page (default: 20, max: 100)
  - `isPublic` (optional): Filter by public status (true|false)
  - `sortBy` (optional): Sort field (created_at|name|updated_at, default: created_at)
  - `order` (optional): Sort order (asc|desc, default: desc)

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "items": [
      {
        "id": "uuid",
        "name": "string",
        "description": "string",
        "isPublic": false,
        "coverImageUrl": "url",
        "itemCount": 5,
        "createdAt": "ISO 8601 timestamp",
        "updatedAt": "ISO 8601 timestamp"
      }
    ],
    "total": 10,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  }
}
```

### `POST /api/collections`

Create a new collection.

- **Authentication**: Required
- **Description**: Creates a new collection for organizing favorites

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Body**:
```json
{
  "name": "string (1-100 chars, required)",
  "description": "string (optional, max 500 chars)",
  "isPublic": false,
  "coverImageUrl": "string (optional)"
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "收藏集合创建成功",
  "data": {
    "id": "uuid",
    "name": "string",
    "description": "string",
    "isPublic": false,
    "coverImageUrl": "url",
    "itemCount": 0,
    "createdAt": "ISO 8601 timestamp",
    "updatedAt": "ISO 8601 timestamp"
  }
}
```

### `GET /api/collections/:id`

Get a specific collection by ID.

- **Authentication**: Required (or public access for public collections)
- **Description**: Returns detailed information about a collection including all items

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>` (optional for public collections)
- **Parameters**:
  - `id`: Collection UUID

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "id": "uuid",
    "name": "string",
    "description": "string",
    "isPublic": false,
    "coverImageUrl": "url",
    "itemCount": 5,
    "createdAt": "ISO 8601 timestamp",
    "updatedAt": "ISO 8601 timestamp",
    "items": [
      {
        "itemType": "clothing|outfit",
        "itemId": "uuid",
        "addedAt": "ISO 8601 timestamp",
        "details": {
          "id": "uuid",
          "name": "string",
          "category": "string",
          "imageUrls": ["url1", "url2"]
        }
      }
    ]
  }
}
```

### `PUT /api/collections/:id`

Update a specific collection.

- **Authentication**: Required
- **Description**: Updates an existing collection (partial updates allowed)

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Parameters**:
  - `id`: Collection UUID
- **Body**: Same as POST, but all fields are optional

**Response:**
- **200 OK**: Returns the updated collection

### `DELETE /api/collections/:id`

Delete a specific collection.

- **Authentication**: Required
- **Description**: Permanently deletes a collection and all its items

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Parameters**:
  - `id`: Collection UUID

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "收藏集合删除成功",
  "data": null
}
```

### `POST /api/collections/:id/items`

Add items to a collection.

- **Authentication**: Required
- **Description**: Adds multiple items to a collection

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Parameters**:
  - `id`: Collection UUID
- **Body**:
```json
{
  "items": [
    {
      "itemType": "clothing|outfit",
      "itemId": "uuid"
    }
  ]
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "成功添加 X/Y 个项目到集合",
  "data": {
    "results": [
      {
        "itemType": "clothing",
        "itemId": "uuid",
        "success": true,
        "action": "added|already_exists"
      }
    ],
    "successCount": 5,
    "totalCount": 6
  }
}
```

### `DELETE /api/collections/:id/items`

Remove items from a collection.

- **Authentication**: Required
- **Description**: Removes multiple items from a collection

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Parameters**:
  - `id`: Collection UUID
- **Body**:
```json
{
  "items": [
    {
      "itemType": "clothing|outfit",
      "itemId": "uuid"
    }
  ]
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "成功从集合中移除 X 个项目",
  "data": {
    "removedCount": 3
  }
}
```

---

## Share Management Endpoints

### `POST /api/favorites/share`

Create a share link for favorites.

- **Authentication**: Required
- **Description**: Generates a shareable link for selected favorite items

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Body**:
```json
{
  "items": [
    {
      "itemType": "clothing|outfit",
      "itemId": "uuid"
    }
  ],
  "shareType": "link|image",
  "expiresIn": 7200,
  "title": "string (optional)",
  "description": "string (optional)"
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "分享链接创建成功",
  "data": {
    "shareId": "uuid",
    "shareToken": "random-token",
    "shareUrl": "https://domain.com/share/token",
    "expiresAt": "ISO 8601 timestamp",
    "itemCount": 3
  }
}
```

### `POST /api/collections/:id/share`

Create a share link for a collection.

- **Authentication**: Required
- **Description**: Generates a shareable link for a collection

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
  - `Content-Type: application/json`
- **Parameters**:
  - `id`: Collection UUID
- **Body**:
```json
{
  "shareType": "link|image",
  "expiresIn": 7200
}
```

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "分享链接创建成功",
  "data": {
    "shareId": "uuid",
    "shareToken": "random-token",
    "shareUrl": "https://domain.com/share/token",
    "expiresAt": "ISO 8601 timestamp",
    "collection": {
      "id": "uuid",
      "name": "string",
      "itemCount": 5
    }
  }
}
```

### `GET /api/share/:token`

Access shared content via token.

- **Authentication**: None required
- **Description**: Returns shared content for public viewing

**Request:**
- **Parameters**:
  - `token`: Share token

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "shareId": "uuid",
    "shareType": "favorites|collection",
    "title": "string",
    "description": "string",
    "createdAt": "ISO 8601 timestamp",
    "expiresAt": "ISO 8601 timestamp",
    "viewCount": 10,
    "items": [
      {
        "itemType": "clothing|outfit",
        "itemId": "uuid",
        "details": {
          "id": "uuid",
          "name": "string",
          "category": "string",
          "imageUrls": ["url1", "url2"]
        }
      }
    ]
  }
}
```
- **404 Not Found**: Share not found or expired

### `GET /api/share/:token/stats`

Get share statistics.

- **Authentication**: Required (must be share owner)
- **Description**: Returns view statistics for a share

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Parameters**:
  - `token`: Share token

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "shareId": "uuid",
    "viewCount": 25,
    "uniqueViews": 18,
    "createdAt": "ISO 8601 timestamp",
    "lastViewedAt": "ISO 8601 timestamp",
    "dailyViews": [
      {
        "date": "2024-01-01",
        "views": 5
      }
    ]
  }
}
```

---

## Search and Filter Endpoints

### `GET /api/favorites/search`

Search within user's favorites.

- **Authentication**: Required
- **Description**: Performs full-text search across favorite items

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `q`: Search query (required, min 1 char)
  - `type` (optional): Filter by item type (clothing|outfit)
  - `category` (optional): Filter by category
  - `color` (optional): Filter by color
  - `season` (optional): Filter by season
  - `page` (optional): Page number (default: 1)
  - `pageSize` (optional): Items per page (default: 20, max: 100)

**Response:**
- **200 OK**:
```json
{
  "success": true,
  "message": "Success",
  "data": {
    "query": "search term",
    "items": [
      {
        "id": "uuid",
        "itemType": "clothing|outfit",
        "itemId": "uuid",
        "relevanceScore": 0.95,
        "matchedFields": ["name", "description"],
        "details": {
          "id": "uuid",
          "name": "string",
          "category": "string",
          "imageUrls": ["url1", "url2"]
        }
      }
    ],
    "total": 15,
    "page": 1,
    "pageSize": 20,
    "totalPages": 1
  }
}
```

### `GET /api/collections/search`

Search within user's collections.

- **Authentication**: Required
- **Description**: Searches collections by name and description

**Request:**
- **Headers**: 
  - `Authorization: Bearer <token>`
- **Query Parameters**:
  - `q`: Search query (required, min 1 char)
  - `isPublic` (optional): Filter by public status
  - `page` (optional): Page number (default: 1)
  - `pageSize` (optional): Items per page (default: 20, max: 100)

**Response:**
- **200 OK**: Similar structure to collections list with search relevance

---

## Rate Limiting

Currently, no rate limiting is implemented, but it may be added in future versions.

## CORS

The API supports CORS with the following configuration:
- **Allowed Origins**: `*` (configurable)
- **Allowed Methods**: `GET, POST, PUT, DELETE, OPTIONS`
- **Allowed Headers**: `Content-Type, Authorization, X-Requested-With`