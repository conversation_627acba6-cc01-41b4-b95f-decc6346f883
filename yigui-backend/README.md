# Yigui Backend API

A Cloudflare Workers-based backend service for the Yigui Virtual Wardrobe application, providing RESTful APIs for user authentication, clothing management, outfit creation, and file uploads.

## Architecture Overview

This backend is built using **Cloudflare Workers** with the following key technologies:

- **Runtime**: Cloudflare Workers (V8 JavaScript runtime)
- **Router**: itty-router for HTTP request routing
- **Database**: Cloudflare D1 (SQLite-based serverless database)
- **Storage**: Cloudflare R2 (S3-compatible object storage) for image files
- **Session Storage**: Cloudflare KV for refresh token management
- **Authentication**: JWT tokens with @tsndr/cloudflare-worker-jwt
- **Validation**: Zod for request/response validation

## Project Structure

```
yigui-backend/
├── src/
│   ├── handlers/          # API endpoint handlers
│   │   ├── auth.js        # Authentication endpoints
│   │   ├── clothing.js    # Clothing management endpoints
│   │   ├── outfits.js     # Outfit management endpoints
│   │   └── upload.js      # File upload endpoints
│   ├── middleware/        # Request middleware
│   │   ├── auth.js        # JWT authentication middleware
│   │   └── cors.js        # CORS configuration and headers
│   ├── models/            # Data models and validation schemas
│   │   ├── user.js        # User model and validation
│   │   ├── clothing.js    # Clothing model and validation
│   │   └── outfit.js      # Outfit model and validation
│   ├── utils/             # Utility functions
│   │   ├── jwt.js         # JWT token generation and verification
│   │   └── response.js    # Standardized API response helpers
│   └── index.js           # Main application entry point
├── schema.sql             # Database schema definition
├── wrangler.toml          # Cloudflare Workers configuration
└── package.json           # Dependencies and scripts
```

### Key Directories Explained

#### `/src/handlers/`
Contains the main business logic for each API endpoint group:
- **auth.js**: User registration, login, token refresh, profile management
- **clothing.js**: CRUD operations for clothing items with image management
- **outfits.js**: Outfit creation, management, and recommendations
- **upload.js**: File upload to R2 storage with presigned URLs

#### `/src/middleware/`
Request processing middleware:
- **auth.js**: JWT token validation and user context injection
- **cors.js**: Cross-origin resource sharing configuration

#### `/src/models/`
Data access layer with validation:
- **user.js**: User data operations and Zod validation schemas
- **clothing.js**: Clothing item data operations and validation
- **outfit.js**: Outfit data operations and validation

## Database Schema

The application uses Cloudflare D1 with the following main tables:

- **users**: User accounts and authentication
- **clothing**: Individual clothing items with metadata
- **clothing_images**: Image URLs associated with clothing items
- **outfits**: Outfit collections
- **outfit_items**: Many-to-many relationship between outfits and clothing
- **favorites**: User favorites (optional)
- **tags**: Flexible tagging system (optional)

See `schema.sql` for the complete database structure.

## Environment Configuration

The service uses Cloudflare Workers environment variables and bindings:

### Environment Variables (wrangler.toml)
- `JWT_SECRET`: Secret key for JWT token signing
- `ENVIRONMENT`: Current environment (development/production)
- `CORS_ORIGIN`: Allowed CORS origins
- `CLOUDFLARE_ACCOUNT_ID`: Cloudflare account identifier

### Cloudflare Bindings
- `DB`: D1 database binding for data persistence
- `STORAGE`: R2 bucket binding for image storage
- `SESSIONS`: KV namespace binding for session management

## Running the Backend

### Prerequisites
- Node.js 18+ 
- npm or yarn
- Cloudflare account with Workers, D1, R2, and KV enabled

### Development Setup

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Configure wrangler.toml**:
   Update the configuration file with your Cloudflare account details and resource IDs.

3. **Initialize database**:
   ```bash
   npx wrangler d1 execute yigui-db --file=./schema.sql
   ```

4. **Start development server**:
   ```bash
   npm run dev
   ```

   The API will be available at `http://localhost:8787`

### Production Deployment

```bash
npm run deploy
```

### Testing

```bash
npm test
```

## API Authentication

The API uses JWT-based authentication:

1. **Public endpoints**: `/api/auth/register`, `/api/auth/login`
2. **Protected endpoints**: All other endpoints require `Authorization: Bearer <token>` header
3. **Token refresh**: Use `/api/auth/refresh` with refresh token to get new access tokens

## Health Check

The service provides a health check endpoint:

```
GET /api/health
```

Returns service status and timestamp information.

## Error Handling

All API responses follow a consistent format:

**Success Response**:
```json
{
  "success": true,
  "message": "Operation completed",
  "data": { ... }
}
```

**Error Response**:
```json
{
  "success": false,
  "error": "Error description",
  "code": "ERROR_CODE"
}
```

## Development Notes

- The service automatically handles CORS for cross-origin requests
- All database operations use prepared statements for security
- File uploads are handled through R2 with automatic cleanup on deletion
- JWT tokens have configurable expiration times
- Refresh tokens are stored in KV with automatic expiration

For detailed API documentation, see `docs/api.md`.