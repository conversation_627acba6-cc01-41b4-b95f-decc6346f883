{"name": "yigui-backend", "version": "1.0.0", "description": "Yigui Virtual Wardrobe Backend API", "main": "src/index.js", "scripts": {"dev": "wrangler dev", "deploy": "wrangler deploy", "test": "vitest", "test:watch": "vitest --watch"}, "keywords": ["cloudflare", "workers", "api", "wardrobe"], "author": "", "license": "MIT", "dependencies": {"itty-router": "^4.0.25", "@tsndr/cloudflare-worker-jwt": "^2.5.0", "zod": "^3.22.4"}, "devDependencies": {"wrangler": "^4.0.0", "vitest": "^1.0.0", "@cloudflare/workers-types": "^4.0.0"}}