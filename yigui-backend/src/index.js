// Cloudflare Workers 入口文件
import { Router } from 'itty-router';
import { corsHeaders, corsOptions } from './middleware/cors';
import { authMiddleware } from './middleware/auth';
import * as authHandler from './handlers/auth';
import * as clothingHandler from './handlers/clothing';
import * as outfitsHandler from './handlers/outfits';
import * as uploadHandler from './handlers/upload';
import * as favoritesHandler from './handlers/favorites';
import * as shareHandler from './handlers/share';
import * as collectionHandler from './handlers/collection';

const router = Router();

// 预检请求处理
router.options('*', () => new Response(null, { headers: corsHeaders }));

// 健康检查
router.get('/api/health', () => {
  return new Response(JSON.stringify({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    service: 'yigui-backend'
  }), {
    headers: { 'Content-Type': 'application/json', ...corsHeaders }
  });
});

// 公共路由 - 不需要认证
router.post('/api/auth/register', authHandler.register);
router.post('/api/auth/login', authHandler.login);
router.post('/api/auth/refresh', authHandler.refreshToken);

// 需要认证的路由
router.get('/api/auth/profile', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return authHandler.getProfile(request);
});

router.put('/api/auth/profile', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return authHandler.updateProfile(request);
});

// 衣物相关路由
router.get('/api/clothing', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return clothingHandler.list(request);
});

router.post('/api/clothing', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return clothingHandler.create(request);
});

router.get('/api/clothing/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return clothingHandler.get(request);
});

router.put('/api/clothing/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return clothingHandler.update(request);
});

router.delete('/api/clothing/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return clothingHandler.deleteClothing(request);
});

// 穿搭相关路由
router.get('/api/outfits', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.list(request);
});

// 穿搭统计和推荐路由（需要在参数路由之前）
router.get('/api/outfits/stats', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.getStats(request);
});

router.get('/api/outfits/recommendations', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.getRecommendations(request);
});

router.post('/api/outfits', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.create(request);
});

router.get('/api/outfits/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.get(request);
});

router.put('/api/outfits/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.update(request);
});

router.delete('/api/outfits/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.deleteOutfit(request);
});

// 穿搭衣物管理路由
router.post('/api/outfits/:id/items', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.addItem(request);
});

router.delete('/api/outfits/:id/items/:clothingId', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return outfitsHandler.removeItem(request);
});

// 文件上传路由
router.get('/api/upload/signature', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return uploadHandler.getSignature(request);
});

router.post('/api/upload', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return uploadHandler.handleUpload(request);
});

router.delete('/api/upload/:key', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return uploadHandler.deleteFile(request);
});

// 收藏相关路由
router.post('/api/favorites/toggle', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.toggle(request);
});

router.get('/api/favorites', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.list(request);
});

router.post('/api/favorites/batch', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.batch(request);
});

router.get('/api/favorites/status', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.status(request);
});

router.get('/api/favorites/search', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.search(request);
});

router.get('/api/favorites/suggestions', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.suggestions(request);
});

router.get('/api/favorites/popular', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.popular(request);
});

router.get('/api/favorites/stats', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.stats(request);
});

router.post('/api/favorites/share', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.share(request);
});

router.post('/api/favorites/share/batch', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.batchShare(request);
});

router.get('/api/favorites/shares', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return favoritesHandler.getShares(request);
});

// 分享相关路由
router.post('/api/shares', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return shareHandler.createShare(request, request.env);
});

router.get('/api/shares', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return shareHandler.getUserShares(request, request.env);
});

router.post('/api/shares/batch', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return shareHandler.batchCreateShares(request, request.env);
});

router.get('/api/shares/stats', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return shareHandler.getShareStats(request, request.env);
});

router.delete('/api/shares/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return shareHandler.deleteShare(request, request.env);
});

// 公共分享内容访问路由（不需要认证）
router.get('/api/share/:token', (request) => {
  return shareHandler.getShareContent(request, request.env);
});

// 收藏集合相关路由
router.get('/api/collections', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.getCollections(request, request.env);
});

router.post('/api/collections', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.createCollection(request, request.env);
});

router.get('/api/collections/stats', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.getCollectionStats(request, request.env);
});

router.get('/api/collections/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.getCollection(request, request.env);
});

router.put('/api/collections/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.updateCollection(request, request.env);
});

router.delete('/api/collections/:id', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.deleteCollection(request, request.env);
});

router.get('/api/collections/:id/items', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.getCollectionItems(request, request.env);
});

router.post('/api/collections/:id/items', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.addItemsToCollection(request, request.env);
});

router.delete('/api/collections/:id/items', async (request) => {
  const authResult = await authMiddleware(request);
  if (authResult) return authResult;
  return collectionHandler.removeItemsFromCollection(request, request.env);
});

// 404处理
router.all('*', () => new Response('Not Found', { 
  status: 404,
  headers: corsHeaders
}));

// Worker 入口点
export default {
  async fetch(request, env, ctx) {
    try {
      // 添加环境变量到请求对象，供中间件和处理器使用
      request.env = env;
      request.ctx = ctx;
      
      const response = await router.handle(request);
      
      // 确保所有响应都包含 CORS 头
      const headers = new Headers(response.headers);
      Object.entries(corsHeaders).forEach(([key, value]) => {
        headers.set(key, value);
      });
      
      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers
      });
    } catch (error) {
      console.error('Worker Error:', error);
      return new Response(JSON.stringify({ 
        error: 'Internal Server Error',
        message: error.message 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }
  }
};