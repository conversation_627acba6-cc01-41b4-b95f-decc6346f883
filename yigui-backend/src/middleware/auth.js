// 认证中间件
import { verify } from '@tsndr/cloudflare-worker-jwt';
import { corsHeaders } from './cors';

export async function authMiddleware(request) {
  const authHeader = request.headers.get('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    return new Response(JSON.stringify({ 
      error: 'Unauthorized',
      message: 'No token provided'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }

  const token = authHeader.split(' ')[1];
  
  try {
    // 验证JWT token
    const isValid = await verify(token, request.env.JWT_SECRET);
    
    if (!isValid) {
      return new Response(JSON.stringify({ 
        error: 'Unauthorized',
        message: 'Invalid token'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }
    
    // 解析用户信息并添加到请求中
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      
      // 检查token是否过期
      if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
        return new Response(JSON.stringify({ 
          error: 'Unauthorized',
          message: 'Token expired'
        }), {
          status: 401,
          headers: { 'Content-Type': 'application/json', ...corsHeaders }
        });
      }
      
      request.user = {
        id: payload.userId || payload.sub,
        username: payload.username,
        email: payload.email
      };
      
      return null; // 继续处理请求
    } catch (parseError) {
      console.error('Token parse error:', parseError);
      return new Response(JSON.stringify({ 
        error: 'Unauthorized',
        message: 'Invalid token format'
      }), {
        status: 401,
        headers: { 'Content-Type': 'application/json', ...corsHeaders }
      });
    }
    
  } catch (error) {
    console.error('Token verification error:', error);
    return new Response(JSON.stringify({ 
      error: 'Unauthorized',
      message: 'Token verification failed'
    }), {
      status: 401,
      headers: { 'Content-Type': 'application/json', ...corsHeaders }
    });
  }
}

// 可选认证中间件（如果有token则验证，没有token则继续）
export async function optionalAuthMiddleware(request) {
  const authHeader = request.headers.get('Authorization');
  
  if (!authHeader || !authHeader.startsWith('Bearer ')) {
    // 没有token，设置匿名用户
    request.user = null;
    return null;
  }

  const token = authHeader.split(' ')[1];
  
  try {
    const isValid = await verify(token, request.env.JWT_SECRET);
    
    if (isValid) {
      const payload = JSON.parse(atob(token.split('.')[1]));
      
      if (!payload.exp || payload.exp >= Math.floor(Date.now() / 1000)) {
        request.user = {
          id: payload.userId || payload.sub,
          username: payload.username,
          email: payload.email
        };
      } else {
        request.user = null; // token过期
      }
    } else {
      request.user = null; // token无效
    }
  } catch (error) {
    console.error('Optional auth error:', error);
    request.user = null; // 认证失败，设为匿名用户
  }
  
  return null; // 总是继续处理请求
}