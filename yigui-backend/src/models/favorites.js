// 收藏数据模型和验证
import { z } from 'zod';

// 收藏操作验证模式
export const FavoriteToggleSchema = z.object({
  itemType: z.enum(['clothing', 'outfit'], {
    errorMap: () => ({ message: '项目类型必须是 clothing 或 outfit' })
  }),
  itemId: z.string()
    .min(1, '项目ID不能为空')
    .uuid('项目ID格式不正确')
});

// 收藏查询验证模式
export const FavoriteQuerySchema = z.object({
  type: z.enum(['clothing', 'outfit']).optional(),
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(20),
  category: z.string().optional(),
  sortBy: z.enum(['created_at', 'name', 'relevance']).default('created_at'),
  order: z.enum(['asc', 'desc']).default('desc'),
  // 搜索相关参数
  search: z.string().optional(),
  color: z.string().optional(),
  season: z.string().optional(),
  occasion: z.string().optional(),
  brand: z.string().optional()
});

// 批量收藏操作验证模式
export const FavoriteBatchSchema = z.object({
  action: z.enum(['add', 'remove'], {
    errorMap: () => ({ message: '操作类型必须是 add 或 remove' })
  }),
  items: z.array(z.object({
    itemType: z.enum(['clothing', 'outfit']),
    itemId: z.string().uuid('项目ID格式不正确')
  })).min(1, '至少需要一个项目').max(50, '一次最多操作50个项目')
});

// 收藏状态检查验证模式
export const FavoriteStatusSchema = z.object({
  itemType: z.enum(['clothing', 'outfit']),
  itemIds: z.string().transform((str) => str.split(',').filter(id => id.trim()))
    .pipe(z.array(z.string().uuid()).max(100, '一次最多检查100个项目'))
});

// 收藏数据模型
export class FavoritesModel {
  constructor(db) {
    this.db = db;
  }

  // 切换收藏状态
  async toggleFavorite(userId, itemType, itemId) {
    const now = new Date().toISOString();
    
    // 检查是否已收藏
    const existing = await this.db.prepare(`
      SELECT id FROM favorites 
      WHERE user_id = ? AND item_type = ? AND item_id = ?
    `).bind(userId, itemType, itemId).first();

    if (existing) {
      // 取消收藏
      const result = await this.db.prepare(`
        DELETE FROM favorites 
        WHERE user_id = ? AND item_type = ? AND item_id = ?
      `).bind(userId, itemType, itemId).run();

      return {
        success: result.success,
        isFavorited: false,
        action: 'removed'
      };
    } else {
      // 添加收藏
      const id = crypto.randomUUID();
      const result = await this.db.prepare(`
        INSERT INTO favorites (id, user_id, item_type, item_id, created_at)
        VALUES (?, ?, ?, ?, ?)
      `).bind(id, userId, itemType, itemId, now).run();

      return {
        success: result.success,
        isFavorited: true,
        action: 'added'
      };
    }
  }

  // 获取收藏列表
  async getFavorites(userId, queryParams = {}) {
    const {
      type, page = 1, pageSize = 20, category, sortBy = 'created_at', order = 'desc',
      search, color, season, occasion, brand
    } = queryParams;

    // 构建基础查询
    const { query, countQuery, bindings } = this._buildSearchQuery(userId, {
      type, category, search, color, season, occasion, brand
    });

    // 添加排序
    const orderDirection = order.toUpperCase();
    let finalQuery = query;
    
    if (sortBy === 'name') {
      finalQuery += ` ORDER BY COALESCE(c.name, o.name) ${orderDirection}`;
    } else if (sortBy === 'relevance' && search) {
      // 按相关性排序：名称匹配优先，然后按创建时间
      finalQuery += ` ORDER BY 
        CASE 
          WHEN LOWER(COALESCE(c.name, o.name)) LIKE LOWER(?) THEN 1
          WHEN LOWER(COALESCE(c.description, o.notes)) LIKE LOWER(?) THEN 2
          ELSE 3
        END,
        f.created_at ${orderDirection}`;
      bindings.push(`%${search}%`, `%${search}%`);
    } else {
      finalQuery += ` ORDER BY f.created_at ${orderDirection}`;
    }

    // 添加分页
    finalQuery += ' LIMIT ? OFFSET ?';
    const offset = (page - 1) * pageSize;
    bindings.push(pageSize, offset);

    // 执行查询
    const [items, count] = await Promise.all([
      this.db.prepare(finalQuery).bind(...bindings).all(),
      this.db.prepare(countQuery).bind(...bindings.slice(0, bindings.length - 2)).first()
    ]);

    // 获取详细信息
    const itemsWithDetails = await Promise.all(
      items.results.map(async (favorite) => {
        const details = await this.getItemDetails(favorite.item_type, favorite.item_id);
        return {
          id: favorite.id,
          itemType: favorite.item_type,
          itemId: favorite.item_id,
          favoritedAt: favorite.favorited_at,
          details
        };
      })
    );

    return {
      items: itemsWithDetails,
      total: count.total,
      page,
      pageSize,
      totalPages: Math.ceil(count.total / pageSize),
      searchQuery: search || null,
      filters: {
        type, category, color, season, occasion, brand
      }
    };
  }

  // 构建搜索查询的辅助方法
  _buildSearchQuery(userId, filters) {
    const { type, category, search, color, season, occasion, brand } = filters;
    let bindings = [userId];
    let whereConditions = ['f.user_id = ?'];
    let joinClause = '';
    let selectFields = 'f.*, f.created_at as favorited_at';

    // 根据类型决定JOIN表
    if (type === 'clothing' || (!type && (category || color || season || brand))) {
      joinClause = 'JOIN clothing c ON f.item_id = c.id';
      selectFields += ', c.name, c.category, c.color, c.season, c.brand, c.description';
      
      if (type !== 'outfit') {
        whereConditions.push("(f.item_type = 'clothing' OR f.item_type IS NULL)");
      }
    } else if (type === 'outfit' || (!type && occasion)) {
      joinClause = 'JOIN outfits o ON f.item_id = o.id';
      selectFields += ', o.name, o.occasion, o.notes';
      
      if (type !== 'clothing') {
        whereConditions.push("(f.item_type = 'outfit' OR f.item_type IS NULL)");
      }
    } else if (!type) {
      // 如果没有指定类型，需要LEFT JOIN两个表
      joinClause = `
        LEFT JOIN clothing c ON f.item_id = c.id AND f.item_type = 'clothing'
        LEFT JOIN outfits o ON f.item_id = o.id AND f.item_type = 'outfit'
      `;
      selectFields += ', c.name as clothing_name, c.category, c.color, c.season, c.brand, c.description';
      selectFields += ', o.name as outfit_name, o.occasion, o.notes';
    }

    // 添加类型筛选
    if (type) {
      whereConditions.push('f.item_type = ?');
      bindings.push(type);
    }

    // 添加搜索条件
    if (search) {
      const searchConditions = [];
      const searchTerm = `%${search}%`;
      
      if (type === 'clothing' || !type) {
        searchConditions.push('LOWER(c.name) LIKE LOWER(?)');
        searchConditions.push('LOWER(c.description) LIKE LOWER(?)');
        searchConditions.push('LOWER(c.brand) LIKE LOWER(?)');
        bindings.push(searchTerm, searchTerm, searchTerm);
      }
      
      if (type === 'outfit' || !type) {
        searchConditions.push('LOWER(o.name) LIKE LOWER(?)');
        searchConditions.push('LOWER(o.notes) LIKE LOWER(?)');
        bindings.push(searchTerm, searchTerm);
      }
      
      if (searchConditions.length > 0) {
        whereConditions.push(`(${searchConditions.join(' OR ')})`);
      }
    }

    // 添加其他筛选条件
    if (category) {
      whereConditions.push('c.category = ?');
      bindings.push(category);
    }
    
    if (color) {
      whereConditions.push('LOWER(c.color) LIKE LOWER(?)');
      bindings.push(`%${color}%`);
    }
    
    if (season) {
      whereConditions.push('c.season = ?');
      bindings.push(season);
    }
    
    if (occasion) {
      whereConditions.push('o.occasion = ?');
      bindings.push(occasion);
    }
    
    if (brand) {
      whereConditions.push('LOWER(c.brand) LIKE LOWER(?)');
      bindings.push(`%${brand}%`);
    }

    // 构建最终查询
    const query = `
      SELECT ${selectFields}
      FROM favorites f
      ${joinClause}
      WHERE ${whereConditions.join(' AND ')}
    `;

    const countQuery = `
      SELECT COUNT(*) as total
      FROM favorites f
      ${joinClause}
      WHERE ${whereConditions.join(' AND ')}
    `;

    return { query, countQuery, bindings };
  }

  // 获取项目详细信息
  async getItemDetails(itemType, itemId) {
    if (itemType === 'clothing') {
      const clothing = await this.db.prepare(`
        SELECT * FROM clothing WHERE id = ?
      `).bind(itemId).first();

      if (!clothing) return null;

      // 获取图片
      const images = await this.db.prepare(`
        SELECT image_url FROM clothing_images WHERE clothing_id = ?
        ORDER BY created_at
      `).bind(itemId).all();

      return {
        ...clothing,
        imageUrls: images.results.map(img => img.image_url)
      };
    } else if (itemType === 'outfit') {
      const outfit = await this.db.prepare(`
        SELECT * FROM outfits WHERE id = ?
      `).bind(itemId).first();

      if (!outfit) return null;

      // 获取搭配中的衣物数量（优化：只获取数量，不获取详细信息）
      const itemCount = await this.db.prepare(`
        SELECT COUNT(*) as count FROM outfit_items WHERE outfit_id = ?
      `).bind(itemId).first();

      // 获取搭配预览图片（优化：只获取第一张图片作为预览）
      const previewImage = await this.db.prepare(`
        SELECT ci.image_url
        FROM clothing_images ci
        JOIN outfit_items oi ON ci.clothing_id = oi.clothing_id
        WHERE oi.outfit_id = ?
        ORDER BY ci.created_at
        LIMIT 1
      `).bind(itemId).first();

      // 获取搭配中的衣物详细信息（仅在需要时）
      const items = await this.db.prepare(`
        SELECT c.id, c.name, c.category, c.color, ci.image_url
        FROM outfit_items oi
        JOIN clothing c ON oi.clothing_id = c.id
        LEFT JOIN clothing_images ci ON c.id = ci.clothing_id
        WHERE oi.outfit_id = ?
        ORDER BY c.category, oi.created_at
      `).bind(itemId).all();

      // 组织衣物数据
      const clothingMap = new Map();
      items.results.forEach(item => {
        if (!clothingMap.has(item.id)) {
          clothingMap.set(item.id, {
            id: item.id,
            name: item.name,
            category: item.category,
            color: item.color,
            imageUrls: []
          });
        }
        if (item.image_url) {
          clothingMap.get(item.id).imageUrls.push(item.image_url);
        }
      });

      return {
        ...outfit,
        itemCount: itemCount.count,
        previewImage: previewImage?.image_url || null,
        items: Array.from(clothingMap.values())
      };
    }

    return null;
  }

  // 批量收藏操作
  async batchFavorites(userId, action, items) {
    const now = new Date().toISOString();
    const results = [];

    // 使用事务处理批量操作
    for (const item of items) {
      try {
        if (action === 'add') {
          // 检查是否已存在
          const existing = await this.db.prepare(`
            SELECT id FROM favorites 
            WHERE user_id = ? AND item_type = ? AND item_id = ?
          `).bind(userId, item.itemType, item.itemId).first();

          if (!existing) {
            const id = crypto.randomUUID();
            const result = await this.db.prepare(`
              INSERT INTO favorites (id, user_id, item_type, item_id, created_at)
              VALUES (?, ?, ?, ?, ?)
            `).bind(id, userId, item.itemType, item.itemId, now).run();

            results.push({
              itemType: item.itemType,
              itemId: item.itemId,
              success: result.success,
              action: 'added'
            });
          } else {
            results.push({
              itemType: item.itemType,
              itemId: item.itemId,
              success: true,
              action: 'already_exists'
            });
          }
        } else if (action === 'remove') {
          const result = await this.db.prepare(`
            DELETE FROM favorites 
            WHERE user_id = ? AND item_type = ? AND item_id = ?
          `).bind(userId, item.itemType, item.itemId).run();

          results.push({
            itemType: item.itemType,
            itemId: item.itemId,
            success: result.success,
            action: 'removed'
          });
        }
      } catch (error) {
        results.push({
          itemType: item.itemType,
          itemId: item.itemId,
          success: false,
          error: error.message
        });
      }
    }

    return {
      results,
      successCount: results.filter(r => r.success).length,
      totalCount: results.length
    };
  }

  // 检查单个项目是否已收藏
  async isFavorited(userId, itemType, itemId) {
    const result = await this.db.prepare(`
      SELECT id FROM favorites 
      WHERE user_id = ? AND item_type = ? AND item_id = ?
    `).bind(userId, itemType, itemId).first();

    return !!result;
  }

  // 检查收藏状态
  async checkFavoriteStatus(userId, itemType, itemIds) {
    if (itemIds.length === 0) {
      return {};
    }

    const placeholders = itemIds.map(() => '?').join(',');
    const query = `
      SELECT item_id, 1 as is_favorited
      FROM favorites 
      WHERE user_id = ? AND item_type = ? AND item_id IN (${placeholders})
    `;

    const bindings = [userId, itemType, ...itemIds];
    const result = await this.db.prepare(query).bind(...bindings).all();

    // 构建状态映射
    const statusMap = {};
    itemIds.forEach(id => {
      statusMap[id] = false;
    });

    const results = Array.isArray(result) ? result : (result.results || []);
    results.forEach(row => {
      statusMap[row.item_id] = true;
    });

    return statusMap;
  }

  // 获取收藏统计
  async getFavoriteStats(userId) {
    const stats = await this.db.prepare(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN item_type = 'clothing' THEN 1 END) as clothing_count,
        COUNT(CASE WHEN item_type = 'outfit' THEN 1 END) as outfit_count,
        DATE(created_at) as date,
        COUNT(*) as daily_count
      FROM favorites 
      WHERE user_id = ?
      GROUP BY DATE(created_at)
      ORDER BY date DESC
      LIMIT 30
    `).bind(userId).all();

    const totalStats = await this.db.prepare(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN item_type = 'clothing' THEN 1 END) as clothing_count,
        COUNT(CASE WHEN item_type = 'outfit' THEN 1 END) as outfit_count
      FROM favorites 
      WHERE user_id = ?
    `).bind(userId).first();

    // 获取衣物分类分布
    const categoryStats = await this.db.prepare(`
      SELECT c.category, COUNT(*) as count
      FROM favorites f
      JOIN clothing c ON f.item_id = c.id
      WHERE f.user_id = ? AND f.item_type = 'clothing'
      GROUP BY c.category
      ORDER BY count DESC
    `).bind(userId).all();

    // 获取搭配场合分布
    const occasionStats = await this.db.prepare(`
      SELECT o.occasion, COUNT(*) as count
      FROM favorites f
      JOIN outfits o ON f.item_id = o.id
      WHERE f.user_id = ? AND f.item_type = 'outfit'
      GROUP BY o.occasion
      ORDER BY count DESC
    `).bind(userId).all();

    // 获取最近收藏的搭配
    const recentOutfits = await this.db.prepare(`
      SELECT f.created_at as favorited_at, o.name, o.occasion
      FROM favorites f
      JOIN outfits o ON f.item_id = o.id
      WHERE f.user_id = ? AND f.item_type = 'outfit'
      ORDER BY f.created_at DESC
      LIMIT 5
    `).bind(userId).all();

    return {
      total: totalStats.total,
      clothingCount: totalStats.clothing_count,
      outfitCount: totalStats.outfit_count,
      dailyActivity: stats.results,
      categoryDistribution: categoryStats.results,
      occasionDistribution: occasionStats.results,
      recentOutfits: recentOutfits.results
    };
  }

  // 获取搜索建议
  async getSearchSuggestions(userId, query, limit = 10) {
    if (!query || query.length < 2) {
      return [];
    }

    const searchTerm = `%${query}%`;
    
    // 获取衣物名称建议
    const clothingSuggestions = await this.db.prepare(`
      SELECT DISTINCT c.name as suggestion, 'clothing' as type, c.category
      FROM favorites f
      JOIN clothing c ON f.item_id = c.id
      WHERE f.user_id = ? AND f.item_type = 'clothing' 
        AND LOWER(c.name) LIKE LOWER(?)
      ORDER BY c.name
      LIMIT ?
    `).bind(userId, searchTerm, Math.ceil(limit / 2)).all();

    // 获取搭配名称建议
    const outfitSuggestions = await this.db.prepare(`
      SELECT DISTINCT o.name as suggestion, 'outfit' as type, o.occasion
      FROM favorites f
      JOIN outfits o ON f.item_id = o.id
      WHERE f.user_id = ? AND f.item_type = 'outfit' 
        AND LOWER(o.name) LIKE LOWER(?)
      ORDER BY o.name
      LIMIT ?
    `).bind(userId, searchTerm, Math.ceil(limit / 2)).all();

    // 获取品牌建议
    const brandSuggestions = await this.db.prepare(`
      SELECT DISTINCT c.brand as suggestion, 'brand' as type
      FROM favorites f
      JOIN clothing c ON f.item_id = c.id
      WHERE f.user_id = ? AND f.item_type = 'clothing' 
        AND c.brand IS NOT NULL 
        AND LOWER(c.brand) LIKE LOWER(?)
      ORDER BY c.brand
      LIMIT 3
    `).bind(userId, searchTerm).all();

    // 合并并去重建议
    const allSuggestions = [
      ...clothingSuggestions.results,
      ...outfitSuggestions.results,
      ...brandSuggestions.results
    ];

    return allSuggestions.slice(0, limit);
  }

  // 获取热门搜索词
  async getPopularSearchTerms(userId, limit = 5) {
    // 基于用户收藏的物品名称生成热门搜索词
    const popularTerms = await this.db.prepare(`
      SELECT 
        CASE 
          WHEN f.item_type = 'clothing' THEN c.name
          WHEN f.item_type = 'outfit' THEN o.name
        END as term,
        COUNT(*) as frequency
      FROM favorites f
      LEFT JOIN clothing c ON f.item_id = c.id AND f.item_type = 'clothing'
      LEFT JOIN outfits o ON f.item_id = o.id AND f.item_type = 'outfit'
      WHERE f.user_id = ?
      GROUP BY term
      HAVING term IS NOT NULL
      ORDER BY frequency DESC, term
      LIMIT ?
    `).bind(userId, limit).all();

    return popularTerms.results.map(item => item.term);
  }

  // 删除用户的所有收藏（用于用户删除时的清理）
  async deleteUserFavorites(userId) {
    const result = await this.db.prepare(`
      DELETE FROM favorites WHERE user_id = ?
    `).bind(userId).run();

    return result.success;
  }
}