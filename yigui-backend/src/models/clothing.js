// 衣物数据模型和验证
import { z } from 'zod';

// 衣物创建验证模式
export const ClothingCreateSchema = z.object({
  name: z.string()
    .min(1, '衣物名称不能为空')
    .max(100, '衣物名称最多100个字符'),
  category: z.string()
    .min(1, '请选择分类'),
  subcategory: z.string()
    .min(1, '请选择子分类')
    .optional(),
  type: z.string()
    .min(1, '请选择类型')
    .optional(),
  brand: z.string()
    .max(50, '品牌名称最多50个字符')
    .optional(),
  color: z.string()
    .min(1, '请选择颜色'),
  size: z.string()
    .min(1, '请选择尺码'),
  season: z.string()
    .min(1, { message: '请选择季节' }),
  purchaseDate: z.string()
    .regex(/^\d{4}-\d{2}-\d{2}$/, '日期格式应为YYYY-MM-DD')
    .optional(),
  price: z.number()
    .min(0, '价格不能为负数')
    .max(999999.99, '价格过高')
    .optional(),
  description: z.string()
    .max(500, '描述最多500个字符')
    .optional(),
  imageUrls: z.array(z.string().url('图片URL格式不正确'))
    .max(5, '最多上传5张图片')
    .optional()
    .default([])
});

// 衣物更新验证模式
export const ClothingUpdateSchema = ClothingCreateSchema.partial();

// 衣物查询验证模式
export const ClothingQuerySchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(20),
  category: z.string().optional(),
  subcategory: z.string().optional(),
  color: z.string().optional(),
  season: z.string().optional(),
  brand: z.string().optional(),
  search: z.string().optional()
});

// 衣物数据模型
export class ClothingModel {
  constructor(db) {
    this.db = db;
  }

  // 创建衣物
  async createClothing(userId, clothingData) {
    const {
      name, category, subcategory, type, brand, color, size, season,
      purchaseDate, price, description, imageUrls = []
    } = clothingData;
    
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    // 插入衣物基本信息
    const result = await this.db.prepare(`
      INSERT INTO clothing (
        id, user_id, name, category, subcategory, type, brand, 
        color, size, season, purchase_date, price, description, 
        created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id, userId, name, category, subcategory || null, type || null, brand || null,
      color, size, season, purchaseDate || null, price || null,
      description || null, now, now
    ).run();

    if (!result.success) {
      throw new Error('Failed to create clothing');
    }

    // 插入图片URLs
    if (imageUrls.length > 0) {
      await this.addClothingImages(id, imageUrls);
    }

    return await this.findById(id, userId);
  }

  // 根据ID查找衣物
  async findById(id, userId) {
    const clothing = await this.db.prepare(`
      SELECT * FROM clothing WHERE id = ? AND user_id = ?
    `).bind(id, userId).first();

    if (!clothing) {
      return null;
    }

    // 获取图片URLs
    const images = await this.db.prepare(`
      SELECT image_url FROM clothing_images WHERE clothing_id = ?
      ORDER BY created_at
    `).bind(id).all();

    return {
      ...clothing,
      imageUrls: images.results.map(img => img.image_url)
    };
  }

  // 查询衣物列表
  async findByUserId(userId, queryParams = {}) {
    const {
      page = 1, pageSize = 20, category, subcategory, color, 
      season, brand, search
    } = queryParams;

    let query = 'SELECT * FROM clothing WHERE user_id = ?';
    let countQuery = 'SELECT COUNT(*) as total FROM clothing WHERE user_id = ?';
    let bindings = [userId];

    // 构建查询条件
    const conditions = [];
    
    if (category) {
      conditions.push('category = ?');
      bindings.push(category);
    }
    
    if (subcategory) {
      conditions.push('subcategory = ?');
      bindings.push(subcategory);
    }
    
    if (color) {
      conditions.push('color = ?');
      bindings.push(color);
    }
    
    if (season) {
      conditions.push('season = ?');
      bindings.push(season);
    }
    
    if (brand) {
      conditions.push('brand = ?');
      bindings.push(brand);
    }
    
    if (search) {
      conditions.push('(name LIKE ? OR description LIKE ?)');
      bindings.push(`%${search}%`, `%${search}%`);
    }

    if (conditions.length > 0) {
      const whereClause = ' AND ' + conditions.join(' AND ');
      query += whereClause;
      countQuery += whereClause;
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    const offset = (page - 1) * pageSize;
    bindings.push(pageSize, offset);

    // 执行查询
    const [items, count] = await Promise.all([
      this.db.prepare(query).bind(...bindings).all(),
      this.db.prepare(countQuery).bind(...bindings.slice(0, -2)).first()
    ]);

    // 为每个衣物获取图片URLs
    const itemsWithImages = await Promise.all(
      items.results.map(async (item) => {
        const images = await this.db.prepare(`
          SELECT image_url FROM clothing_images WHERE clothing_id = ?
          ORDER BY created_at
        `).bind(item.id).all();

        return {
          ...item,
          imageUrls: images.results.map(img => img.image_url)
        };
      })
    );

    return {
      items: itemsWithImages,
      total: count.total,
      page,
      pageSize,
      totalPages: Math.ceil(count.total / pageSize)
    };
  }

  // 更新衣物
  async updateClothing(id, userId, updates) {
    const { imageUrls, ...clothingUpdates } = updates;
    const now = new Date().toISOString();

    // 构建更新查询
    const updateFields = [];
    const bindings = [];

    Object.entries(clothingUpdates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        bindings.push(value);
      }
    });

    if (updateFields.length > 0) {
      updateFields.push('updated_at = ?');
      bindings.push(now, id, userId);

      const query = `UPDATE clothing SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ?`;
      const result = await this.db.prepare(query).bind(...bindings).run();

      if (!result.success) {
        throw new Error('Failed to update clothing');
      }
    }

    // 更新图片URLs
    if (imageUrls !== undefined) {
      await this.updateClothingImages(id, imageUrls);
    }

    return await this.findById(id, userId);
  }

  // 删除衣物
  async deleteClothing(id, userId) {
    // 首先获取所有关联的图片URLs，用于后续删除R2中的文件
    const imageResult = await this.db.prepare(`
      SELECT image_url FROM clothing_images WHERE clothing_id = ?
    `).bind(id).all();
    
    // 删除图片记录
    await this.db.prepare(`
      DELETE FROM clothing_images WHERE clothing_id = ?
    `).bind(id).run();

    // 删除衣物记录
    const result = await this.db.prepare(`
      DELETE FROM clothing WHERE id = ? AND user_id = ?
    `).bind(id, userId).run();

    // 返回删除结果和需要清理的图片URLs
    return {
      success: result.success && result.changes > 0,
      imageUrls: imageResult.results?.map(row => row.image_url) || []
    };
  }

  // 添加衣物图片
  async addClothingImages(clothingId, imageUrls) {
    const now = new Date().toISOString();
    
    for (const imageUrl of imageUrls) {
      const imageId = crypto.randomUUID();
      await this.db.prepare(`
        INSERT INTO clothing_images (id, clothing_id, image_url, created_at)
        VALUES (?, ?, ?, ?)
      `).bind(imageId, clothingId, imageUrl, now).run();
    }
  }

  // 更新衣物图片
  async updateClothingImages(clothingId, imageUrls) {
    // 删除现有图片
    await this.db.prepare(`
      DELETE FROM clothing_images WHERE clothing_id = ?
    `).bind(clothingId).run();

    // 添加新图片
    if (imageUrls.length > 0) {
      await this.addClothingImages(clothingId, imageUrls);
    }
  }

  // 获取用户的衣物统计
  async getClothingStats(userId) {
    const stats = await this.db.prepare(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN category = 'tops' THEN 1 END) as tops,
        COUNT(CASE WHEN category = 'bottoms' THEN 1 END) as bottoms,
        COUNT(CASE WHEN category = 'dresses' THEN 1 END) as dresses,
        COUNT(CASE WHEN category = 'outerwear' THEN 1 END) as outerwear,
        COUNT(CASE WHEN category = 'shoes' THEN 1 END) as shoes,
        COUNT(CASE WHEN category = 'accessories' THEN 1 END) as accessories
      FROM clothing WHERE user_id = ?
    `).bind(userId).first();

    return stats;
  }
}