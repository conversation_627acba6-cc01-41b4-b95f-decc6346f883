/**
 * 收藏集合数据模型
 * 处理收藏集合的数据库操作和业务逻辑
 */

import { generateId } from '../utils/id.js';

/**
 * 收藏集合模型类
 */
export class Collection {
  constructor(data = {}) {
    this.id = data.id || generateId();
    this.user_id = data.user_id;
    this.name = data.name;
    this.description = data.description || null;
    this.is_public = data.is_public || false;
    this.cover_image_url = data.cover_image_url || null;
    this.created_at = data.created_at || new Date().toISOString();
    this.updated_at = data.updated_at || new Date().toISOString();
  }

  /**
   * 验证集合数据
   */
  validate() {
    const errors = [];

    if (!this.user_id) {
      errors.push('用户ID不能为空');
    }

    if (!this.name || this.name.trim().length === 0) {
      errors.push('集合名称不能为空');
    }

    if (this.name && this.name.length > 100) {
      errors.push('集合名称不能超过100个字符');
    }

    if (this.description && this.description.length > 500) {
      errors.push('集合描述不能超过500个字符');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 转换为数据库格式
   */
  toDbFormat() {
    return {
      id: this.id,
      user_id: this.user_id,
      name: this.name.trim(),
      description: this.description,
      is_public: this.is_public,
      cover_image_url: this.cover_image_url,
      created_at: this.created_at,
      updated_at: new Date().toISOString()
    };
  }

  /**
   * 从数据库记录创建实例
   */
  static fromDbRecord(record) {
    return new Collection(record);
  }
}

/**
 * 收藏集合数据访问对象
 */
export class CollectionDAO {
  constructor(db) {
    this.db = db;
  }

  /**
   * 创建收藏集合
   */
  async create(collection) {
    const validation = collection.validate();
    if (!validation.isValid) {
      throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
    }

    const data = collection.toDbFormat();
    
    try {
      const result = await this.db.prepare(`
        INSERT INTO collections (id, user_id, name, description, is_public, cover_image_url, created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
      `).bind(
        data.id,
        data.user_id,
        data.name,
        data.description,
        data.is_public,
        data.cover_image_url,
        data.created_at,
        data.updated_at
      ).run();

      if (!result.success) {
        throw new Error('创建收藏集合失败');
      }

      return await this.findById(data.id);
    } catch (error) {
      if (error.message.includes('UNIQUE constraint failed')) {
        throw new Error('集合名称已存在');
      }
      throw error;
    }
  }

  /**
   * 根据ID查找收藏集合
   */
  async findById(id) {
    const result = await this.db.prepare(`
      SELECT * FROM collections WHERE id = ?
    `).bind(id).first();

    return result ? Collection.fromDbRecord(result) : null;
  }

  /**
   * 根据用户ID获取收藏集合列表
   */
  async findByUserId(userId, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      sortBy = 'created_at',
      sortOrder = 'DESC',
      includePublic = false
    } = options;

    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE user_id = ?';
    let params = [userId];
    
    if (includePublic) {
      whereClause = 'WHERE (user_id = ? OR is_public = TRUE)';
    }

    const validSortFields = ['created_at', 'updated_at', 'name'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'created_at';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const query = `
      SELECT * FROM collections 
      ${whereClause}
      ORDER BY ${sortField} ${order}
      LIMIT ? OFFSET ?
    `;

    const results = await this.db.prepare(query)
      .bind(...params, pageSize, offset)
      .all();

    return results.map(record => Collection.fromDbRecord(record));
  }

  /**
   * 更新收藏集合
   */
  async update(id, updates) {
    const existing = await this.findById(id);
    if (!existing) {
      throw new Error('收藏集合不存在');
    }

    // 合并更新数据
    const updatedCollection = new Collection({
      ...existing,
      ...updates,
      id: existing.id, // 确保ID不被更改
      user_id: existing.user_id, // 确保用户ID不被更改
      updated_at: new Date().toISOString()
    });

    const validation = updatedCollection.validate();
    if (!validation.isValid) {
      throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
    }

    const data = updatedCollection.toDbFormat();

    const result = await this.db.prepare(`
      UPDATE collections 
      SET name = ?, description = ?, is_public = ?, cover_image_url = ?, updated_at = ?
      WHERE id = ?
    `).bind(
      data.name,
      data.description,
      data.is_public,
      data.cover_image_url,
      data.updated_at,
      id
    ).run();

    if (!result.success) {
      throw new Error('更新收藏集合失败');
    }

    return await this.findById(id);
  }

  /**
   * 删除收藏集合
   */
  async delete(id) {
    const existing = await this.findById(id);
    if (!existing) {
      throw new Error('收藏集合不存在');
    }

    // 先删除集合中的所有项目
    await this.db.prepare(`
      DELETE FROM collection_items WHERE collection_id = ?
    `).bind(id).run();

    // 删除集合
    const result = await this.db.prepare(`
      DELETE FROM collections WHERE id = ?
    `).bind(id).run();

    if (!result.success) {
      throw new Error('删除收藏集合失败');
    }

    return true;
  }

  /**
   * 检查用户是否拥有指定集合
   */
  async checkOwnership(collectionId, userId) {
    const result = await this.db.prepare(`
      SELECT id FROM collections WHERE id = ? AND user_id = ?
    `).bind(collectionId, userId).first();

    return !!result;
  }

  /**
   * 获取收藏集合统计信息
   */
  async getStats(userId) {
    const stats = await this.db.prepare(`
      SELECT 
        COUNT(*) as total_collections,
        COUNT(CASE WHEN is_public = TRUE THEN 1 END) as public_collections,
        COUNT(CASE WHEN is_public = FALSE THEN 1 END) as private_collections
      FROM collections 
      WHERE user_id = ?
    `).bind(userId).first();

    return {
      totalCollections: stats.total_collections || 0,
      publicCollections: stats.public_collections || 0,
      privateCollections: stats.private_collections || 0
    };
  }
}

/**
 * 收藏集合项目模型类
 */
export class CollectionItem {
  constructor(data = {}) {
    this.collection_id = data.collection_id;
    this.item_type = data.item_type;
    this.item_id = data.item_id;
    this.added_at = data.added_at || new Date().toISOString();
  }

  /**
   * 验证集合项目数据
   */
  validate() {
    const errors = [];

    if (!this.collection_id) {
      errors.push('集合ID不能为空');
    }

    if (!this.item_type) {
      errors.push('项目类型不能为空');
    }

    if (!['clothing', 'outfit'].includes(this.item_type)) {
      errors.push('项目类型必须是 clothing 或 outfit');
    }

    if (!this.item_id) {
      errors.push('项目ID不能为空');
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 转换为数据库格式
   */
  toDbFormat() {
    return {
      collection_id: this.collection_id,
      item_type: this.item_type,
      item_id: this.item_id,
      added_at: this.added_at
    };
  }

  /**
   * 从数据库记录创建实例
   */
  static fromDbRecord(record) {
    return new CollectionItem(record);
  }
}

/**
 * 收藏集合项目数据访问对象
 */
export class CollectionItemDAO {
  constructor(db) {
    this.db = db;
  }

  /**
   * 添加项目到收藏集合
   */
  async addItem(collectionItem) {
    const validation = collectionItem.validate();
    if (!validation.isValid) {
      throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
    }

    const data = collectionItem.toDbFormat();

    try {
      const result = await this.db.prepare(`
        INSERT INTO collection_items (collection_id, item_type, item_id, added_at)
        VALUES (?, ?, ?, ?)
      `).bind(
        data.collection_id,
        data.item_type,
        data.item_id,
        data.added_at
      ).run();

      if (!result.success) {
        throw new Error('添加项目到集合失败');
      }

      return true;
    } catch (error) {
      if (error.message.includes('UNIQUE constraint failed')) {
        throw new Error('项目已存在于集合中');
      }
      throw error;
    }
  }

  /**
   * 批量添加项目到收藏集合
   */
  async addItems(collectionId, items) {
    if (!Array.isArray(items) || items.length === 0) {
      throw new Error('项目列表不能为空');
    }

    const collectionItems = items.map(item => new CollectionItem({
      collection_id: collectionId,
      item_type: item.itemType,
      item_id: item.itemId
    }));

    // 验证所有项目
    for (const item of collectionItems) {
      const validation = item.validate();
      if (!validation.isValid) {
        throw new Error(`数据验证失败: ${validation.errors.join(', ')}`);
      }
    }

    // 批量插入
    const stmt = this.db.prepare(`
      INSERT OR IGNORE INTO collection_items (collection_id, item_type, item_id, added_at)
      VALUES (?, ?, ?, ?)
    `);

    try {
      for (const item of collectionItems) {
        const data = item.toDbFormat();
        await stmt.bind(
          data.collection_id,
          data.item_type,
          data.item_id,
          data.added_at
        ).run();
      }

      return true;
    } catch (error) {
      throw new Error(`批量添加项目失败: ${error.message}`);
    }
  }

  /**
   * 从收藏集合移除项目
   */
  async removeItem(collectionId, itemType, itemId) {
    const result = await this.db.prepare(`
      DELETE FROM collection_items 
      WHERE collection_id = ? AND item_type = ? AND item_id = ?
    `).bind(collectionId, itemType, itemId).run();

    if (!result.success) {
      throw new Error('移除项目失败');
    }

    return result.changes > 0;
  }

  /**
   * 批量移除项目
   */
  async removeItems(collectionId, items) {
    if (!Array.isArray(items) || items.length === 0) {
      throw new Error('项目列表不能为空');
    }

    const stmt = this.db.prepare(`
      DELETE FROM collection_items 
      WHERE collection_id = ? AND item_type = ? AND item_id = ?
    `);

    let removedCount = 0;
    try {
      for (const item of items) {
        const result = await stmt.bind(collectionId, item.itemType, item.itemId).run();
        if (result.success && result.changes > 0) {
          removedCount++;
        }
      }

      return removedCount;
    } catch (error) {
      throw new Error(`批量移除项目失败: ${error.message}`);
    }
  }

  /**
   * 获取收藏集合中的项目列表
   */
  async getItems(collectionId, options = {}) {
    const {
      page = 1,
      pageSize = 20,
      itemType = null,
      sortBy = 'added_at',
      sortOrder = 'DESC'
    } = options;

    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE ci.collection_id = ?';
    let params = [collectionId];
    
    if (itemType && ['clothing', 'outfit'].includes(itemType)) {
      whereClause += ' AND ci.item_type = ?';
      params.push(itemType);
    }

    const validSortFields = ['added_at'];
    const sortField = validSortFields.includes(sortBy) ? sortBy : 'added_at';
    const order = sortOrder.toUpperCase() === 'ASC' ? 'ASC' : 'DESC';

    const query = `
      SELECT ci.*, 
        CASE 
          WHEN ci.item_type = 'clothing' THEN c.name
          WHEN ci.item_type = 'outfit' THEN o.name
        END as item_name,
        CASE 
          WHEN ci.item_type = 'clothing' THEN c.category
          WHEN ci.item_type = 'outfit' THEN o.occasion
        END as item_category
      FROM collection_items ci
      LEFT JOIN clothing c ON ci.item_type = 'clothing' AND ci.item_id = c.id
      LEFT JOIN outfits o ON ci.item_type = 'outfit' AND ci.item_id = o.id
      ${whereClause}
      ORDER BY ci.${sortField} ${order}
      LIMIT ? OFFSET ?
    `;

    const results = await this.db.prepare(query)
      .bind(...params, pageSize, offset)
      .all();

    return results.map(record => ({
      ...CollectionItem.fromDbRecord(record),
      itemName: record.item_name,
      itemCategory: record.item_category
    }));
  }

  /**
   * 获取收藏集合项目统计
   */
  async getItemStats(collectionId) {
    const stats = await this.db.prepare(`
      SELECT 
        COUNT(*) as total_items,
        COUNT(CASE WHEN item_type = 'clothing' THEN 1 END) as clothing_count,
        COUNT(CASE WHEN item_type = 'outfit' THEN 1 END) as outfit_count
      FROM collection_items 
      WHERE collection_id = ?
    `).bind(collectionId).first();

    return {
      totalItems: stats.total_items || 0,
      clothingCount: stats.clothing_count || 0,
      outfitCount: stats.outfit_count || 0
    };
  }

  /**
   * 检查项目是否在集合中
   */
  async isItemInCollection(collectionId, itemType, itemId) {
    const result = await this.db.prepare(`
      SELECT 1 FROM collection_items 
      WHERE collection_id = ? AND item_type = ? AND item_id = ?
    `).bind(collectionId, itemType, itemId).first();

    return !!result;
  }
}