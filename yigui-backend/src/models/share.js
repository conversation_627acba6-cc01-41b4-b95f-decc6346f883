/**
 * 分享数据模型
 * 处理分享相关的数据库操作
 */

import { generateId } from '../utils/id.js';

export class ShareModel {
  constructor(db) {
    this.db = db;
  }

  /**
   * 创建分享记录
   * @param {Object} shareData - 分享数据
   * @param {string} shareData.userId - 用户ID
   * @param {string} shareData.shareType - 分享类型 ('collection', 'clothing', 'outfit')
   * @param {string} shareData.targetId - 目标ID
   * @param {string} shareData.shareToken - 分享令牌
   * @param {Date} shareData.expiresAt - 过期时间
   * @returns {Promise<Object>} 创建的分享记录
   */
  async createShare(shareData) {
    const { userId, shareType, targetId, shareToken, expiresAt } = shareData;
    
    const shareId = generateId();
    const now = new Date().toISOString();
    
    const stmt = this.db.prepare(`
      INSERT INTO shares (id, user_id, share_type, target_id, share_token, expires_at, created_at)
      VALUES (?, ?, ?, ?, ?, ?, ?)
    `);
    
    await stmt.bind(shareId, userId, shareType, targetId, shareToken, expiresAt, now).run();
    
    return {
      id: shareId,
      userId,
      shareType,
      targetId,
      shareToken,
      expiresAt,
      viewCount: 0,
      createdAt: now
    };
  }

  /**
   * 根据分享令牌获取分享记录
   * @param {string} shareToken - 分享令牌
   * @returns {Promise<Object|null>} 分享记录
   */
  async getShareByToken(shareToken) {
    const stmt = this.db.prepare(`
      SELECT * FROM shares 
      WHERE share_token = ? AND (expires_at IS NULL OR expires_at > datetime('now'))
    `);
    
    const result = await stmt.bind(shareToken).first();
    
    if (!result) {
      return null;
    }
    
    return {
      id: result.id,
      userId: result.user_id,
      shareType: result.share_type,
      targetId: result.target_id,
      shareToken: result.share_token,
      expiresAt: result.expires_at,
      viewCount: result.view_count,
      createdAt: result.created_at
    };
  }

  /**
   * 获取用户的分享记录列表
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @param {number} options.page - 页码
   * @param {number} options.pageSize - 每页大小
   * @param {string} options.shareType - 分享类型筛选
   * @returns {Promise<Object>} 分享记录列表和分页信息
   */
  async getUserShares(userId, options = {}) {
    const { page = 1, pageSize = 20, shareType } = options;
    const offset = (page - 1) * pageSize;
    
    let whereClause = 'WHERE user_id = ?';
    let params = [userId];
    
    if (shareType) {
      whereClause += ' AND share_type = ?';
      params.push(shareType);
    }
    
    // 获取总数
    const countStmt = this.db.prepare(`
      SELECT COUNT(*) as total FROM shares ${whereClause}
    `);
    const countResult = await countStmt.bind(...params).first();
    const total = countResult.total;
    
    // 获取分享列表
    const stmt = this.db.prepare(`
      SELECT * FROM shares 
      ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `);
    
    const results = await stmt.bind(...params, pageSize, offset).all();
    
    const shares = results.map(result => ({
      id: result.id,
      userId: result.user_id,
      shareType: result.share_type,
      targetId: result.target_id,
      shareToken: result.share_token,
      expiresAt: result.expires_at,
      viewCount: result.view_count,
      createdAt: result.created_at
    }));
    
    return {
      shares,
      pagination: {
        page,
        pageSize,
        total,
        totalPages: Math.ceil(total / pageSize),
        hasMore: page * pageSize < total
      }
    };
  }

  /**
   * 增加分享访问次数
   * @param {string} shareToken - 分享令牌
   * @returns {Promise<boolean>} 是否成功
   */
  async incrementViewCount(shareToken) {
    const stmt = this.db.prepare(`
      UPDATE shares 
      SET view_count = view_count + 1 
      WHERE share_token = ?
    `);
    
    const result = await stmt.bind(shareToken).run();
    return result.changes > 0;
  }

  /**
   * 删除分享记录
   * @param {string} shareId - 分享ID
   * @param {string} userId - 用户ID（用于权限验证）
   * @returns {Promise<boolean>} 是否成功删除
   */
  async deleteShare(shareId, userId) {
    const stmt = this.db.prepare(`
      DELETE FROM shares 
      WHERE id = ? AND user_id = ?
    `);
    
    const result = await stmt.bind(shareId, userId).run();
    return result.changes > 0;
  }

  /**
   * 删除过期的分享记录
   * @returns {Promise<number>} 删除的记录数
   */
  async deleteExpiredShares() {
    const stmt = this.db.prepare(`
      DELETE FROM shares 
      WHERE expires_at IS NOT NULL AND expires_at <= datetime('now')
    `);
    
    const result = await stmt.run();
    return result.changes;
  }

  /**
   * 检查用户是否有权限访问分享内容
   * @param {string} shareToken - 分享令牌
   * @param {string} targetId - 目标ID
   * @param {string} shareType - 分享类型
   * @returns {Promise<boolean>} 是否有权限
   */
  async validateShareAccess(shareToken, targetId, shareType) {
    const share = await this.getShareByToken(shareToken);
    
    if (!share) {
      return false;
    }
    
    return share.targetId === targetId && share.shareType === shareType;
  }
}