// 穿搭数据模型和验证
import { z } from 'zod';

// 穿搭创建验证模式
export const OutfitCreateSchema = z.object({
  name: z.string()
    .min(1, '穿搭名称不能为空')
    .max(100, '穿搭名称最多100个字符'),
  occasion: z.string()
    .max(50, '场合描述最多50个字符')
    .optional(),
  notes: z.string()
    .max(500, '备注最多500个字符')
    .optional(),
  clothingItems: z.array(z.string())
    .max(20, '一个穿搭最多包含20件衣物')
    .optional()
    .default([])
    .refine(items => items.length >= 1, {
      message: '至少需要选择一件衣物'
    })
});

// 穿搭更新验证模式
export const OutfitUpdateSchema = OutfitCreateSchema.partial();

// 穿搭查询验证模式
export const OutfitQuerySchema = z.object({
  page: z.number().min(1).default(1),
  pageSize: z.number().min(1).max(100).default(20),
  occasion: z.string().optional(),
  search: z.string().optional()
});

// 穿搭数据模型
export class OutfitModel {
  constructor(db) {
    this.db = db;
  }

  // 创建穿搭
  async createOutfit(userId, outfitData) {
    const { name, occasion, notes, clothingItems = [] } = outfitData;
    
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    // 插入穿搭基本信息
    const result = await this.db.prepare(`
      INSERT INTO outfits (
        id, user_id, name, occasion, notes, created_at, updated_at
      ) VALUES (?, ?, ?, ?, ?, ?, ?)
    `).bind(
      id, userId, name, occasion || null, notes || null, now, now
    ).run();

    if (!result.success) {
      throw new Error('Failed to create outfit');
    }

    // 添加衣物关联
    if (clothingItems.length > 0) {
      await this.addOutfitItems(id, clothingItems);
    }

    return await this.findById(id, userId);
  }

  // 根据ID查找穿搭
  async findById(id, userId) {
    const outfit = await this.db.prepare(`
      SELECT * FROM outfits WHERE id = ? AND user_id = ?
    `).bind(id, userId).first();

    if (!outfit) {
      return null;
    }

    // 获取关联的衣物
    const clothingItems = await this.db.prepare(`
      SELECT c.*, ci.image_url
      FROM clothing c
      LEFT JOIN outfit_items oi ON c.id = oi.clothing_id
      LEFT JOIN clothing_images ci ON c.id = ci.clothing_id
      WHERE oi.outfit_id = ?
      ORDER BY c.category, c.created_at
    `).bind(id).all();

    // 整理衣物数据，合并图片URLs
    const clothingMap = new Map();
    clothingItems.results.forEach(item => {
      if (!clothingMap.has(item.id)) {
        clothingMap.set(item.id, {
          ...item,
          imageUrls: []
        });
        delete clothingMap.get(item.id).image_url;
      }
      
      if (item.image_url) {
        clothingMap.get(item.id).imageUrls.push(item.image_url);
      }
    });

    return {
      ...outfit,
      clothingItems: Array.from(clothingMap.values())
    };
  }

  // 查询穿搭列表
  async findByUserId(userId, queryParams = {}) {
    const { page = 1, pageSize = 20, occasion, search } = queryParams;

    let query = 'SELECT * FROM outfits WHERE user_id = ?';
    let countQuery = 'SELECT COUNT(*) as total FROM outfits WHERE user_id = ?';
    let bindings = [userId];

    // 构建查询条件
    const conditions = [];
    
    if (occasion) {
      conditions.push('occasion = ?');
      bindings.push(occasion);
    }
    
    if (search) {
      conditions.push('(name LIKE ? OR notes LIKE ?)');
      bindings.push(`%${search}%`, `%${search}%`);
    }

    if (conditions.length > 0) {
      const whereClause = ' AND ' + conditions.join(' AND ');
      query += whereClause;
      countQuery += whereClause;
    }

    query += ' ORDER BY created_at DESC LIMIT ? OFFSET ?';
    const offset = (page - 1) * pageSize;
    bindings.push(pageSize, offset);

    // 执行查询
    const [items, count] = await Promise.all([
      this.db.prepare(query).bind(...bindings).all(),
      this.db.prepare(countQuery).bind(...bindings.slice(0, -2)).first()
    ]);

    // 为每个穿搭获取衣物数量和预览图片
    const itemsWithPreview = await Promise.all(
      items.results.map(async (outfit) => {
        // 获取穿搭中的衣物数量
        const itemCount = await this.db.prepare(`
          SELECT COUNT(*) as count FROM outfit_items WHERE outfit_id = ?
        `).bind(outfit.id).first();

        // 获取第一张预览图片
        const previewImage = await this.db.prepare(`
          SELECT ci.image_url
          FROM clothing_images ci
          JOIN outfit_items oi ON ci.clothing_id = oi.clothing_id
          WHERE oi.outfit_id = ?
          ORDER BY ci.created_at
          LIMIT 1
        `).bind(outfit.id).first();

        return {
          ...outfit,
          itemCount: itemCount.count,
          previewImage: previewImage?.image_url || null
        };
      })
    );

    return {
      items: itemsWithPreview,
      total: count.total,
      page,
      pageSize,
      totalPages: Math.ceil(count.total / pageSize)
    };
  }

  // 更新穿搭
  async updateOutfit(id, userId, updates) {
    const { clothingItems, ...outfitUpdates } = updates;
    const now = new Date().toISOString();

    // 构建更新查询
    const updateFields = [];
    const bindings = [];

    Object.entries(outfitUpdates).forEach(([key, value]) => {
      if (value !== undefined) {
        updateFields.push(`${key} = ?`);
        bindings.push(value);
      }
    });

    if (updateFields.length > 0) {
      updateFields.push('updated_at = ?');
      bindings.push(now, id, userId);

      const query = `UPDATE outfits SET ${updateFields.join(', ')} WHERE id = ? AND user_id = ?`;
      const result = await this.db.prepare(query).bind(...bindings).run();

      if (!result.success) {
        throw new Error('Failed to update outfit');
      }
    }

    // 更新衣物关联
    if (clothingItems !== undefined) {
      await this.updateOutfitItems(id, clothingItems);
    }

    return await this.findById(id, userId);
  }

  // 删除穿搭
  async deleteOutfit(id, userId) {
    // 先删除衣物关联记录
    await this.db.prepare(`
      DELETE FROM outfit_items WHERE outfit_id = ?
    `).bind(id).run();

    // 删除穿搭记录
    const result = await this.db.prepare(`
      DELETE FROM outfits WHERE id = ? AND user_id = ?
    `).bind(id, userId).run();

    return result.success && result.changes > 0;
  }

  // 添加穿搭衣物关联
  async addOutfitItems(outfitId, clothingIds) {
    const now = new Date().toISOString();

    for (const clothingId of clothingIds) {
      const result = await this.db.prepare(`
        INSERT INTO outfit_items (outfit_id, clothing_id, created_at)
        VALUES (?, ?, ?)
      `).bind(outfitId, clothingId, now).run();

      if (!result.success) {
        throw new Error(`Failed to add clothing item ${clothingId} to outfit ${outfitId}`);
      }
    }
  }

  // 更新穿搭衣物关联
  async updateOutfitItems(outfitId, clothingIds) {
    // 删除现有关联
    await this.db.prepare(`
      DELETE FROM outfit_items WHERE outfit_id = ?
    `).bind(outfitId).run();

    // 添加新关联
    if (clothingIds.length > 0) {
      await this.addOutfitItems(outfitId, clothingIds);
    }
  }

  // 移除穿搭中的特定衣物
  async removeOutfitItem(outfitId, clothingId) {
    const result = await this.db.prepare(`
      DELETE FROM outfit_items WHERE outfit_id = ? AND clothing_id = ?
    `).bind(outfitId, clothingId).run();

    return result.success && result.changes > 0;
  }

  // 添加衣物到穿搭
  async addOutfitItem(outfitId, clothingId) {
    const now = new Date().toISOString();
    
    const result = await this.db.prepare(`
      INSERT INTO outfit_items (outfit_id, clothing_id, created_at)
      VALUES (?, ?, ?)
    `).bind(outfitId, clothingId, now).run();

    return result.success;
  }

  // 获取用户的穿搭统计
  async getOutfitStats(userId) {
    const stats = await this.db.prepare(`
      SELECT 
        COUNT(*) as total,
        COUNT(CASE WHEN occasion = 'casual' THEN 1 END) as casual,
        COUNT(CASE WHEN occasion = 'work' THEN 1 END) as work,
        COUNT(CASE WHEN occasion = 'formal' THEN 1 END) as formal,
        COUNT(CASE WHEN occasion = 'party' THEN 1 END) as party,
        COUNT(CASE WHEN occasion = 'date' THEN 1 END) as date,
        COUNT(CASE WHEN occasion = 'sport' THEN 1 END) as sport
      FROM outfits WHERE user_id = ?
    `).bind(userId).first();

    return stats;
  }

  // 验证衣物是否属于用户
  async verifyClothingOwnership(userId, clothingIds) {
    if (clothingIds.length === 0) return true;

    const placeholders = clothingIds.map(() => '?').join(',');
    const result = await this.db.prepare(`
      SELECT COUNT(*) as count FROM clothing 
      WHERE user_id = ? AND id IN (${placeholders})
    `).bind(userId, ...clothingIds).first();

    return result.count === clothingIds.length;
  }

  // 获取穿搭推荐（基于颜色搭配和场合）
  async getOutfitRecommendations(userId, occasion = null, limit = 10) {
    let query = `
      SELECT DISTINCT o.*, COUNT(oi.clothing_id) as item_count
      FROM outfits o
      LEFT JOIN outfit_items oi ON o.id = oi.outfit_id
      WHERE o.user_id = ?
    `;
    
    let bindings = [userId];
    
    if (occasion) {
      query += ' AND o.occasion = ?';
      bindings.push(occasion);
    }
    
    query += `
      GROUP BY o.id
      ORDER BY o.created_at DESC
      LIMIT ?
    `;
    bindings.push(limit);

    const recommendations = await this.db.prepare(query).bind(...bindings).all();
    
    return recommendations.results || [];
  }
}