// 用户数据模型和验证
import { z } from 'zod';

// 用户注册验证模式
export const UserRegistrationSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, '用户名只能包含字母、数字、下划线和中文'),
  email: z.string()
    .email('请输入有效的邮箱地址')
    .max(100, '邮箱地址过长'),
  password: z.string()
    .min(6, '密码至少6个字符')
    .max(50, '密码最多50个字符')
});

// 用户登录验证模式
export const UserLoginSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址'),
  password: z.string().min(1, '请输入密码')
});

// 用户资料更新验证模式
export const UserUpdateSchema = z.object({
  username: z.string()
    .min(3, '用户名至少3个字符')
    .max(20, '用户名最多20个字符')
    .regex(/^[a-zA-Z0-9_\u4e00-\u9fa5]+$/, '用户名只能包含字母、数字、下划线和中文')
    .optional(),
  currentPassword: z.string().optional(),
  newPassword: z.string()
    .min(6, '新密码至少6个字符')
    .max(50, '新密码最多50个字符')
    .optional()
}).refine(data => {
  // 如果要更改密码，必须提供当前密码
  if (data.newPassword && !data.currentPassword) {
    return false;
  }
  return true;
}, {
  message: '更改密码时必须提供当前密码',
  path: ['currentPassword']
});

// 密码重置验证模式
export const PasswordResetSchema = z.object({
  email: z.string().email('请输入有效的邮箱地址')
});

// 用户数据模型
export class UserModel {
  constructor(db) {
    this.db = db;
  }

  // 创建用户
  async createUser(userData) {
    const { username, email, passwordHash } = userData;
    const id = crypto.randomUUID();
    const now = new Date().toISOString();

    const result = await this.db.prepare(`
      INSERT INTO users (id, username, email, password_hash, created_at, updated_at)
      VALUES (?, ?, ?, ?, ?, ?)
    `).bind(id, username, email, passwordHash, now, now).run();

    if (!result.success) {
      throw new Error('Failed to create user');
    }

    return { id, username, email, created_at: now, updated_at: now };
  }

  // 根据邮箱查找用户
  async findByEmail(email) {
    const result = await this.db.prepare(`
      SELECT id, username, email, password_hash, created_at, updated_at
      FROM users WHERE email = ?
    `).bind(email).first();

    return result;
  }

  // 根据用户名查找用户
  async findByUsername(username) {
    const result = await this.db.prepare(`
      SELECT id, username, email, password_hash, created_at, updated_at
      FROM users WHERE username = ?
    `).bind(username).first();

    return result;
  }

  // 根据ID查找用户
  async findById(id) {
    const result = await this.db.prepare(`
      SELECT id, username, email, created_at, updated_at
      FROM users WHERE id = ?
    `).bind(id).first();

    return result;
  }

  // 更新用户信息
  async updateUser(id, updates) {
    const { username, passwordHash } = updates;
    const now = new Date().toISOString();
    
    let query = 'UPDATE users SET updated_at = ?';
    let bindings = [now];
    
    if (username) {
      query += ', username = ?';
      bindings.push(username);
    }
    
    if (passwordHash) {
      query += ', password_hash = ?';
      bindings.push(passwordHash);
    }
    
    query += ' WHERE id = ?';
    bindings.push(id);

    const result = await this.db.prepare(query).bind(...bindings).run();

    if (!result.success) {
      throw new Error('Failed to update user');
    }

    return await this.findById(id);
  }

  // 删除用户
  async deleteUser(id) {
    const result = await this.db.prepare(`
      DELETE FROM users WHERE id = ?
    `).bind(id).run();

    return result.success;
  }

  // 检查邮箱是否已存在
  async emailExists(email) {
    const result = await this.db.prepare(`
      SELECT 1 FROM users WHERE email = ? LIMIT 1
    `).bind(email).first();

    return !!result;
  }

  // 检查用户名是否已存在
  async usernameExists(username) {
    const result = await this.db.prepare(`
      SELECT 1 FROM users WHERE username = ? LIMIT 1
    `).bind(username).first();

    return !!result;
  }
}