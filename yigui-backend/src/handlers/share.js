/**
 * 分享API处理器
 */

import { ShareService } from '../services/shareService.js';
import { createResponse, createErrorResponse } from '../utils/response.js';

/**
 * 创建分享链接
 */
export async function createShare(request, env) {
  try {
    const { user } = request;
    const { shareType, targetId, expiresIn, allowPublic } = await request.json();

    // 验证必需参数
    if (!shareType || !targetId) {
      return createErrorResponse('分享类型和目标ID不能为空', 400);
    }

    // 验证分享类型
    const validShareTypes = ['clothing', 'outfit', 'collection'];
    if (!validShareTypes.includes(shareType)) {
      return createErrorResponse('不支持的分享类型', 400);
    }

    const shareService = new ShareService(env.DB);
    
    const share = await shareService.createShare({
      userId: user.id,
      shareType,
      targetId,
      options: {
        expiresIn,
        allowPublic
      }
    });

    return createResponse({
      success: true,
      data: share
    });

  } catch (error) {
    console.error('创建分享失败:', error);
    return createErrorResponse(error.message || '创建分享失败', 500);
  }
}

/**
 * 获取分享内容
 */
export async function getShareContent(request, env) {
  try {
    const url = new URL(request.url);
    const shareToken = url.pathname.split('/').pop();

    if (!shareToken) {
      return createErrorResponse('分享令牌不能为空', 400);
    }

    const shareService = new ShareService(env.DB);
    const shareContent = await shareService.getShareContent(shareToken);

    return createResponse({
      success: true,
      data: shareContent
    });

  } catch (error) {
    console.error('获取分享内容失败:', error);
    
    if (error.message.includes('不存在') || error.message.includes('过期')) {
      return createErrorResponse(error.message, 404);
    }
    
    return createErrorResponse(error.message || '获取分享内容失败', 500);
  }
}

/**
 * 获取用户分享列表
 */
export async function getUserShares(request, env) {
  try {
    const { user } = request;
    const url = new URL(request.url);
    
    const page = parseInt(url.searchParams.get('page')) || 1;
    const pageSize = Math.min(parseInt(url.searchParams.get('pageSize')) || 20, 100);
    const shareType = url.searchParams.get('shareType');

    const shareService = new ShareService(env.DB);
    const result = await shareService.getUserShares(user.id, {
      page,
      pageSize,
      shareType
    });

    return createResponse({
      success: true,
      data: result
    });

  } catch (error) {
    console.error('获取用户分享列表失败:', error);
    return createErrorResponse(error.message || '获取分享列表失败', 500);
  }
}

/**
 * 删除分享
 */
export async function deleteShare(request, env) {
  try {
    const { user } = request;
    const url = new URL(request.url);
    const shareId = url.pathname.split('/').pop();

    if (!shareId) {
      return createErrorResponse('分享ID不能为空', 400);
    }

    const shareService = new ShareService(env.DB);
    const success = await shareService.deleteShare(shareId, user.id);

    if (!success) {
      return createErrorResponse('分享不存在或无权限删除', 404);
    }

    return createResponse({
      success: true,
      message: '分享删除成功'
    });

  } catch (error) {
    console.error('删除分享失败:', error);
    return createErrorResponse(error.message || '删除分享失败', 500);
  }
}

/**
 * 批量创建分享
 */
export async function batchCreateShares(request, env) {
  try {
    const { user } = request;
    const { items, expiresIn, allowPublic } = await request.json();

    if (!items || !Array.isArray(items) || items.length === 0) {
      return createErrorResponse('分享项目列表不能为空', 400);
    }

    if (items.length > 50) {
      return createErrorResponse('批量分享数量不能超过50个', 400);
    }

    const shareService = new ShareService(env.DB);
    const results = [];
    const errors = [];

    // 批量创建分享
    for (const item of items) {
      try {
        const { shareType, targetId } = item;
        
        if (!shareType || !targetId) {
          errors.push({
            item,
            error: '分享类型和目标ID不能为空'
          });
          continue;
        }

        const share = await shareService.createShare({
          userId: user.id,
          shareType,
          targetId,
          options: {
            expiresIn,
            allowPublic
          }
        });

        results.push(share);

      } catch (error) {
        errors.push({
          item,
          error: error.message
        });
      }
    }

    return createResponse({
      success: true,
      data: {
        shares: results,
        errors,
        summary: {
          total: items.length,
          success: results.length,
          failed: errors.length
        }
      }
    });

  } catch (error) {
    console.error('批量创建分享失败:', error);
    return createErrorResponse(error.message || '批量创建分享失败', 500);
  }
}

/**
 * 获取分享统计信息
 */
export async function getShareStats(request, env) {
  try {
    const { user } = request;
    const shareService = new ShareService(env.DB);
    
    // 获取用户所有分享
    const allShares = await shareService.getUserShares(user.id, { 
      page: 1, 
      pageSize: 1000 
    });

    // 计算统计信息
    const stats = {
      totalShares: allShares.shares.length,
      totalViews: allShares.shares.reduce((sum, share) => sum + share.viewCount, 0),
      sharesByType: {},
      recentShares: allShares.shares.slice(0, 5),
      expiredShares: allShares.shares.filter(share => share.isExpired).length
    };

    // 按类型统计
    allShares.shares.forEach(share => {
      stats.sharesByType[share.shareType] = (stats.sharesByType[share.shareType] || 0) + 1;
    });

    return createResponse({
      success: true,
      data: stats
    });

  } catch (error) {
    console.error('获取分享统计失败:', error);
    return createErrorResponse(error.message || '获取分享统计失败', 500);
  }
}