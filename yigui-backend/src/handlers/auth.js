// 认证处理器
import { UserModel, UserRegistrationSchema, UserLoginSchema, UserUpdateSchema } from '../models/user';
import { generateToken, generateRefreshToken, verifyToken } from '../utils/jwt';
import { json, success, error, validationError } from '../utils/response';

// 密码哈希工具函数
async function hashPassword(password) {
  const encoder = new TextEncoder();
  const data = encoder.encode(password);
  const hashBuffer = await crypto.subtle.digest('SHA-256', data);
  const hashArray = Array.from(new Uint8Array(hashBuffer));
  return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
}

async function verifyPassword(password, hash) {
  const hashedPassword = await hashPassword(password);
  return hashedPassword === hash;
}

// 用户注册
export async function register(request) {
  try {
    const body = await request.json();
    
    // 验证输入数据
    const validation = UserRegistrationSchema.safeParse(body);
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const { username, email, password } = validation.data;
    const userModel = new UserModel(request.env.DB);

    // 检查邮箱是否已存在
    if (await userModel.emailExists(email)) {
      return error('邮箱已被注册', 409);
    }

    // 检查用户名是否已存在
    if (await userModel.usernameExists(username)) {
      return error('用户名已被使用', 409);
    }

    // 创建用户
    const passwordHash = await hashPassword(password);
    const user = await userModel.createUser({
      username,
      email,
      passwordHash
    });

    // 生成JWT token
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email
    };

    const [accessToken, refreshToken] = await Promise.all([
      generateToken(tokenPayload, request.env.JWT_SECRET, '7d'),
      generateRefreshToken(tokenPayload, request.env.JWT_SECRET)
    ]);

    // 存储refresh token (使用KV存储)
    await request.env.SESSIONS.put(`refresh_${user.id}`, refreshToken, { expirationTtl: 30 * 24 * 60 * 60 });

    return success({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        created_at: user.created_at
      },
      token: accessToken,
      refreshToken: refreshToken
    }, '注册成功');

  } catch (err) {
    console.error('Registration error:', err);
    return error('注册失败', 500);
  }
}

// 用户登录
export async function login(request) {
  try {
    const body = await request.json();
    
    // 验证输入数据
    const validation = UserLoginSchema.safeParse(body);
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const { email, password } = validation.data;
    const userModel = new UserModel(request.env.DB);

    // 查找用户
    const user = await userModel.findByEmail(email);
    if (!user) {
      return error('邮箱或密码错误', 401);
    }

    // 验证密码
    const isValidPassword = await verifyPassword(password, user.password_hash);
    if (!isValidPassword) {
      return error('邮箱或密码错误', 401);
    }

    // 生成JWT token
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email
    };

    const [accessToken, refreshToken] = await Promise.all([
      generateToken(tokenPayload, request.env.JWT_SECRET, '7d'),
      generateRefreshToken(tokenPayload, request.env.JWT_SECRET)
    ]);

    // 存储refresh token
    await request.env.SESSIONS.put(`refresh_${user.id}`, refreshToken, { expirationTtl: 30 * 24 * 60 * 60 });

    return success({
      user: {
        id: user.id,
        username: user.username,
        email: user.email,
        created_at: user.created_at,
        updated_at: user.updated_at
      },
      token: accessToken,
      refreshToken: refreshToken
    }, '登录成功');

  } catch (err) {
    console.error('Login error:', err);
    return error('登录失败', 500);
  }
}

// 刷新token
export async function refreshToken(request) {
  try {
    const body = await request.json();
    const { refreshToken } = body;

    if (!refreshToken) {
      return error('缺少refresh token', 400);
    }

    // 验证refresh token
    const verification = await verifyToken(refreshToken, request.env.JWT_SECRET);
    if (!verification.valid) {
      return error('无效的refresh token', 401);
    }

    const { payload } = verification;
    const userId = payload.userId;

    // 检查KV中是否存在该refresh token
    const storedToken = await request.env.SESSIONS.get(`refresh_${userId}`);
    if (storedToken !== refreshToken) {
      return error('refresh token已失效', 401);
    }

    // 获取最新用户信息
    const userModel = new UserModel(request.env.DB);
    const user = await userModel.findById(userId);
    if (!user) {
      return error('用户不存在', 404);
    }

    // 生成新的tokens
    const tokenPayload = {
      userId: user.id,
      username: user.username,
      email: user.email
    };

    const [newAccessToken, newRefreshToken] = await Promise.all([
      generateToken(tokenPayload, request.env.JWT_SECRET, '7d'),
      generateRefreshToken(tokenPayload, request.env.JWT_SECRET)
    ]);

    // 更新存储的refresh token
    await request.env.SESSIONS.put(`refresh_${userId}`, newRefreshToken, { expirationTtl: 30 * 24 * 60 * 60 });

    return success({
      token: newAccessToken,
      refreshToken: newRefreshToken
    }, 'Token刷新成功');

  } catch (err) {
    console.error('Token refresh error:', err);
    return error('Token刷新失败', 500);
  }
}

// 获取用户资料
export async function getProfile(request) {
  try {
    const userModel = new UserModel(request.env.DB);
    const user = await userModel.findById(request.user.id);

    if (!user) {
      return error('用户不存在', 404);
    }

    return success({
      id: user.id,
      username: user.username,
      email: user.email,
      created_at: user.created_at,
      updated_at: user.updated_at
    });

  } catch (err) {
    console.error('Get profile error:', err);
    return error('获取用户资料失败', 500);
  }
}

// 更新用户资料
export async function updateProfile(request) {
  try {
    const body = await request.json();
    
    // 验证输入数据
    const validation = UserUpdateSchema.safeParse(body);
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const { username, currentPassword, newPassword } = validation.data;
    const userModel = new UserModel(request.env.DB);

    // 获取当前用户信息
    const currentUser = await userModel.findById(request.user.id);
    if (!currentUser) {
      return error('用户不存在', 404);
    }

    const updates = {};

    // 更新用户名
    if (username && username !== currentUser.username) {
      // 检查新用户名是否已被使用
      if (await userModel.usernameExists(username)) {
        return error('用户名已被使用', 409);
      }
      updates.username = username;
    }

    // 更新密码
    if (newPassword && currentPassword) {
      // 验证当前密码
      const isValidPassword = await verifyPassword(currentPassword, currentUser.password_hash);
      if (!isValidPassword) {
        return error('当前密码错误', 400);
      }
      updates.passwordHash = await hashPassword(newPassword);
    }

    // 执行更新
    if (Object.keys(updates).length > 0) {
      const updatedUser = await userModel.updateUser(request.user.id, updates);
      
      return success({
        id: updatedUser.id,
        username: updatedUser.username,
        email: updatedUser.email,
        created_at: updatedUser.created_at,
        updated_at: updatedUser.updated_at
      }, '资料更新成功');
    } else {
      return success({
        id: currentUser.id,
        username: currentUser.username,
        email: currentUser.email,
        created_at: currentUser.created_at,
        updated_at: currentUser.updated_at
      }, '没有需要更新的内容');
    }

  } catch (err) {
    console.error('Update profile error:', err);
    return error('更新用户资料失败', 500);
  }
}

// 注销登录
export async function logout(request) {
  try {
    const userId = request.user.id;
    
    // 删除存储的refresh token
    await request.env.SESSIONS.delete(`refresh_${userId}`);
    
    return success(null, '注销成功');

  } catch (err) {
    console.error('Logout error:', err);
    return error('注销失败', 500);
  }
}