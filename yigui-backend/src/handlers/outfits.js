// 穿搭管理处理器
import { success, error, notFound, badRequest } from '../utils/response';
import { OutfitModel, OutfitCreateSchema, OutfitUpdateSchema, OutfitQuerySchema } from '../models/outfit.js';

// 获取穿搭列表
export async function list(request) {
  try {
    const url = new URL(request.url);
    const queryParams = {
      page: parseInt(url.searchParams.get('page')) || 1,
      pageSize: parseInt(url.searchParams.get('pageSize')) || 20,
      occasion: url.searchParams.get('occasion') || undefined,
      search: url.searchParams.get('search') || undefined
    };

    // 验证查询参数
    const validatedParams = OutfitQuerySchema.parse(queryParams);
    
    const outfitModel = new OutfitModel(request.env.DB);
    const result = await outfitModel.findByUserId(request.user.id, validatedParams);
    
    return success(result);

  } catch (err) {
    console.error('List outfits error:', err);
    if (err.name === 'ZodError') {
      return badRequest('查询参数错误: ' + err.errors.map(e => e.message).join(', '));
    }
    return error('获取穿搭列表失败', 500);
  }
}

// 创建穿搭
export async function create(request) {
  try {
    const body = await request.json();
    console.log('Create outfit request body:', body);

    // 验证输入数据
    const validatedData = OutfitCreateSchema.parse(body);
    console.log('Validated outfit data:', validatedData);

    const outfitModel = new OutfitModel(request.env.DB);

    // 验证衣物所有权
    if (validatedData.clothingItems && validatedData.clothingItems.length > 0) {
      const hasPermission = await outfitModel.verifyClothingOwnership(
        request.user.id,
        validatedData.clothingItems
      );

      if (!hasPermission) {
        console.error('Clothing ownership verification failed for user:', request.user.id);
        return badRequest('包含不属于您的衣物');
      }
    }

    const outfit = await outfitModel.createOutfit(request.user.id, validatedData);
    console.log('Created outfit:', outfit);
    return success(outfit, '穿搭创建成功');

  } catch (err) {
    console.error('Create outfit error:', err);
    if (err.name === 'ZodError') {
      const errorMessages = err.errors.map(e => `${e.path.join('.')}: ${e.message}`).join(', ');
      console.error('Validation errors:', errorMessages);
      return badRequest('输入数据错误: ' + errorMessages);
    }
    if (err.message === 'Failed to create outfit') {
      return error('数据库操作失败，请重试', 500);
    }
    return error('创建穿搭失败: ' + err.message, 500);
  }
}

// 获取单个穿搭
export async function get(request) {
  try {
    const { id } = request.params;
    
    if (!id) {
      return badRequest('穿搭ID不能为空');
    }
    
    const outfitModel = new OutfitModel(request.env.DB);
    const outfit = await outfitModel.findById(id, request.user.id);
    
    if (!outfit) {
      return notFound('穿搭不存在');
    }
    
    return success(outfit);

  } catch (err) {
    console.error('Get outfit error:', err);
    return error('获取穿搭失败', 500);
  }
}

// 更新穿搭
export async function update(request) {
  try {
    const { id } = request.params;
    const body = await request.json();
    
    if (!id) {
      return badRequest('穿搭ID不能为空');
    }
    
    // 验证输入数据
    const validatedData = OutfitUpdateSchema.parse(body);
    
    const outfitModel = new OutfitModel(request.env.DB);
    
    // 检查穿搭是否存在
    const existingOutfit = await outfitModel.findById(id, request.user.id);
    if (!existingOutfit) {
      return notFound('穿搭不存在');
    }
    
    // 验证衣物所有权
    if (validatedData.clothingItems && validatedData.clothingItems.length > 0) {
      const hasPermission = await outfitModel.verifyClothingOwnership(
        request.user.id, 
        validatedData.clothingItems
      );
      
      if (!hasPermission) {
        return badRequest('包含不属于您的衣物');
      }
    }
    
    const updatedOutfit = await outfitModel.updateOutfit(id, request.user.id, validatedData);
    return success(updatedOutfit, '穿搭更新成功');

  } catch (err) {
    console.error('Update outfit error:', err);
    if (err.name === 'ZodError') {
      return badRequest('输入数据错误: ' + err.errors.map(e => e.message).join(', '));
    }
    return error('更新穿搭失败', 500);
  }
}

// 删除穿搭
export async function deleteOutfit(request) {
  try {
    const { id } = request.params;
    
    if (!id) {
      return badRequest('穿搭ID不能为空');
    }
    
    const outfitModel = new OutfitModel(request.env.DB);
    
    // 检查穿搭是否存在
    const existingOutfit = await outfitModel.findById(id, request.user.id);
    if (!existingOutfit) {
      return notFound('穿搭不存在');
    }
    
    const deleted = await outfitModel.deleteOutfit(id, request.user.id);
    
    if (!deleted) {
      return error('删除穿搭失败', 500);
    }
    
    return success(null, '穿搭删除成功');

  } catch (err) {
    console.error('Delete outfit error:', err);
    return error('删除穿搭失败', 500);
  }
}

// 添加衣物到穿搭
export async function addItem(request) {
  try {
    const { id } = request.params;
    const body = await request.json();
    const { clothingId } = body;
    
    if (!id || !clothingId) {
      return badRequest('穿搭ID和衣物ID不能为空');
    }
    
    const outfitModel = new OutfitModel(request.env.DB);
    
    // 检查穿搭是否存在
    const existingOutfit = await outfitModel.findById(id, request.user.id);
    if (!existingOutfit) {
      return notFound('穿搭不存在');
    }
    
    // 验证衣物所有权
    const hasPermission = await outfitModel.verifyClothingOwnership(
      request.user.id, 
      [clothingId]
    );
    
    if (!hasPermission) {
      return badRequest('衣物不属于您');
    }
    
    const added = await outfitModel.addOutfitItem(id, clothingId);
    
    if (!added) {
      return error('添加衣物失败', 500);
    }
    
    return success(null, '衣物添加成功');

  } catch (err) {
    console.error('Add outfit item error:', err);
    return error('添加衣物失败', 500);
  }
}

// 从穿搭中移除衣物
export async function removeItem(request) {
  try {
    const { id, clothingId } = request.params;
    
    if (!id || !clothingId) {
      return badRequest('穿搭ID和衣物ID不能为空');
    }
    
    const outfitModel = new OutfitModel(request.env.DB);
    
    // 检查穿搭是否存在
    const existingOutfit = await outfitModel.findById(id, request.user.id);
    if (!existingOutfit) {
      return notFound('穿搭不存在');
    }
    
    const removed = await outfitModel.removeOutfitItem(id, clothingId);
    
    if (!removed) {
      return error('移除衣物失败', 500);
    }
    
    return success(null, '衣物移除成功');

  } catch (err) {
    console.error('Remove outfit item error:', err);
    return error('移除衣物失败', 500);
  }
}

// 获取穿搭统计信息
export async function getStats(request) {
  try {
    const outfitModel = new OutfitModel(request.env.DB);
    const stats = await outfitModel.getOutfitStats(request.user.id);
    
    return success(stats);

  } catch (err) {
    console.error('Get outfit stats error:', err);
    return error('获取统计信息失败', 500);
  }
}

// 获取穿搭推荐
export async function getRecommendations(request) {
  try {
    const url = new URL(request.url);
    const occasion = url.searchParams.get('occasion') || null;
    const limit = parseInt(url.searchParams.get('limit')) || 10;
    
    const outfitModel = new OutfitModel(request.env.DB);
    const recommendations = await outfitModel.getOutfitRecommendations(
      request.user.id, 
      occasion, 
      limit
    );
    
    return success({ items: recommendations });

  } catch (err) {
    console.error('Get outfit recommendations error:', err);
    return error('获取推荐失败', 500);
  }
}