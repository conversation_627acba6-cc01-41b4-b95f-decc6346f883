/**
 * 收藏集合API处理器
 * 处理收藏集合相关的HTTP请求
 */

import { CollectionDAO, CollectionItemDAO, Collection, CollectionItem } from '../models/collection.js';
import { successResponse, errorResponse } from '../utils/response.js';

/**
 * 创建收藏集合
 */
export async function createCollection(request, env) {
  try {
    const userId = request.userId; // 从认证中间件获取
    const body = await request.json();

    const collection = new Collection({
      user_id: userId,
      name: body.name,
      description: body.description,
      is_public: body.isPublic || false,
      cover_image_url: body.coverImageUrl
    });

    const collectionDAO = new CollectionDAO(env.DB);
    const result = await collectionDAO.create(collection);

    return successResponse({
      collection: result,
      message: '收藏集合创建成功'
    });
  } catch (error) {
    console.error('创建收藏集合失败:', error);
    return errorResponse(error.message, 400);
  }
}

/**
 * 获取收藏集合列表
 */
export async function getCollections(request, env) {
  try {
    const userId = request.userId;
    const url = new URL(request.url);
    
    const page = parseInt(url.searchParams.get('page')) || 1;
    const pageSize = Math.min(parseInt(url.searchParams.get('pageSize')) || 20, 50);
    const sortBy = url.searchParams.get('sortBy') || 'created_at';
    const sortOrder = url.searchParams.get('sortOrder') || 'DESC';
    const includePublic = url.searchParams.get('includePublic') === 'true';

    const collectionDAO = new CollectionDAO(env.DB);
    const collections = await collectionDAO.findByUserId(userId, {
      page,
      pageSize,
      sortBy,
      sortOrder,
      includePublic
    });

    // 获取每个集合的项目统计
    const collectionItemDAO = new CollectionItemDAO(env.DB);
    const collectionsWithStats = await Promise.all(
      collections.map(async (collection) => {
        const stats = await collectionItemDAO.getItemStats(collection.id);
        return {
          ...collection,
          stats
        };
      })
    );

    return successResponse({
      collections: collectionsWithStats,
      pagination: {
        page,
        pageSize,
        hasMore: collections.length === pageSize
      }
    });
  } catch (error) {
    console.error('获取收藏集合列表失败:', error);
    return errorResponse('获取收藏集合列表失败', 500);
  }
}

/**
 * 获取单个收藏集合详情
 */
export async function getCollection(request, env) {
  try {
    const userId = request.userId;
    const collectionId = request.params.id;

    const collectionDAO = new CollectionDAO(env.DB);
    const collection = await collectionDAO.findById(collectionId);

    if (!collection) {
      return errorResponse('收藏集合不存在', 404);
    }

    // 检查访问权限
    if (collection.user_id !== userId && !collection.is_public) {
      return errorResponse('无权访问此收藏集合', 403);
    }

    // 获取集合项目统计
    const collectionItemDAO = new CollectionItemDAO(env.DB);
    const stats = await collectionItemDAO.getItemStats(collection.id);

    return successResponse({
      collection: {
        ...collection,
        stats
      }
    });
  } catch (error) {
    console.error('获取收藏集合详情失败:', error);
    return errorResponse('获取收藏集合详情失败', 500);
  }
}

/**
 * 更新收藏集合
 */
export async function updateCollection(request, env) {
  try {
    const userId = request.userId;
    const collectionId = request.params.id;
    const body = await request.json();

    const collectionDAO = new CollectionDAO(env.DB);
    
    // 检查所有权
    const hasOwnership = await collectionDAO.checkOwnership(collectionId, userId);
    if (!hasOwnership) {
      return errorResponse('无权修改此收藏集合', 403);
    }

    const updates = {};
    if (body.name !== undefined) updates.name = body.name;
    if (body.description !== undefined) updates.description = body.description;
    if (body.isPublic !== undefined) updates.is_public = body.isPublic;
    if (body.coverImageUrl !== undefined) updates.cover_image_url = body.coverImageUrl;

    const result = await collectionDAO.update(collectionId, updates);

    return successResponse({
      collection: result,
      message: '收藏集合更新成功'
    });
  } catch (error) {
    console.error('更新收藏集合失败:', error);
    return errorResponse(error.message, 400);
  }
}

/**
 * 删除收藏集合
 */
export async function deleteCollection(request, env) {
  try {
    const userId = request.userId;
    const collectionId = request.params.id;

    const collectionDAO = new CollectionDAO(env.DB);
    
    // 检查所有权
    const hasOwnership = await collectionDAO.checkOwnership(collectionId, userId);
    if (!hasOwnership) {
      return errorResponse('无权删除此收藏集合', 403);
    }

    await collectionDAO.delete(collectionId);

    return successResponse({
      message: '收藏集合删除成功'
    });
  } catch (error) {
    console.error('删除收藏集合失败:', error);
    return errorResponse(error.message, 400);
  }
}

/**
 * 获取收藏集合中的项目列表
 */
export async function getCollectionItems(request, env) {
  try {
    const userId = request.userId;
    const collectionId = request.params.id;
    const url = new URL(request.url);
    
    const page = parseInt(url.searchParams.get('page')) || 1;
    const pageSize = Math.min(parseInt(url.searchParams.get('pageSize')) || 20, 50);
    const itemType = url.searchParams.get('itemType');
    const sortBy = url.searchParams.get('sortBy') || 'added_at';
    const sortOrder = url.searchParams.get('sortOrder') || 'DESC';

    const collectionDAO = new CollectionDAO(env.DB);
    const collection = await collectionDAO.findById(collectionId);

    if (!collection) {
      return errorResponse('收藏集合不存在', 404);
    }

    // 检查访问权限
    if (collection.user_id !== userId && !collection.is_public) {
      return errorResponse('无权访问此收藏集合', 403);
    }

    const collectionItemDAO = new CollectionItemDAO(env.DB);
    const items = await collectionItemDAO.getItems(collectionId, {
      page,
      pageSize,
      itemType,
      sortBy,
      sortOrder
    });

    return successResponse({
      items,
      pagination: {
        page,
        pageSize,
        hasMore: items.length === pageSize
      }
    });
  } catch (error) {
    console.error('获取收藏集合项目失败:', error);
    return errorResponse('获取收藏集合项目失败', 500);
  }
}

/**
 * 添加项目到收藏集合
 */
export async function addItemsToCollection(request, env) {
  try {
    const userId = request.userId;
    const collectionId = request.params.id;
    const body = await request.json();

    const collectionDAO = new CollectionDAO(env.DB);
    
    // 检查所有权
    const hasOwnership = await collectionDAO.checkOwnership(collectionId, userId);
    if (!hasOwnership) {
      return errorResponse('无权修改此收藏集合', 403);
    }

    const collectionItemDAO = new CollectionItemDAO(env.DB);
    
    if (body.items && Array.isArray(body.items)) {
      // 批量添加
      await collectionItemDAO.addItems(collectionId, body.items);
      return successResponse({
        message: `成功添加 ${body.items.length} 个项目到收藏集合`
      });
    } else if (body.itemType && body.itemId) {
      // 单个添加
      const collectionItem = new CollectionItem({
        collection_id: collectionId,
        item_type: body.itemType,
        item_id: body.itemId
      });
      
      await collectionItemDAO.addItem(collectionItem);
      return successResponse({
        message: '项目添加到收藏集合成功'
      });
    } else {
      return errorResponse('请提供有效的项目信息', 400);
    }
  } catch (error) {
    console.error('添加项目到收藏集合失败:', error);
    return errorResponse(error.message, 400);
  }
}

/**
 * 从收藏集合移除项目
 */
export async function removeItemsFromCollection(request, env) {
  try {
    const userId = request.userId;
    const collectionId = request.params.id;
    const body = await request.json();

    const collectionDAO = new CollectionDAO(env.DB);
    
    // 检查所有权
    const hasOwnership = await collectionDAO.checkOwnership(collectionId, userId);
    if (!hasOwnership) {
      return errorResponse('无权修改此收藏集合', 403);
    }

    const collectionItemDAO = new CollectionItemDAO(env.DB);
    
    if (body.items && Array.isArray(body.items)) {
      // 批量移除
      const removedCount = await collectionItemDAO.removeItems(collectionId, body.items);
      return successResponse({
        message: `成功从收藏集合移除 ${removedCount} 个项目`
      });
    } else if (body.itemType && body.itemId) {
      // 单个移除
      const removed = await collectionItemDAO.removeItem(collectionId, body.itemType, body.itemId);
      if (removed) {
        return successResponse({
          message: '项目从收藏集合移除成功'
        });
      } else {
        return errorResponse('项目不在收藏集合中', 404);
      }
    } else {
      return errorResponse('请提供有效的项目信息', 400);
    }
  } catch (error) {
    console.error('从收藏集合移除项目失败:', error);
    return errorResponse(error.message, 400);
  }
}

/**
 * 获取收藏集合统计信息
 */
export async function getCollectionStats(request, env) {
  try {
    const userId = request.userId;

    const collectionDAO = new CollectionDAO(env.DB);
    const stats = await collectionDAO.getStats(userId);

    return successResponse({
      stats
    });
  } catch (error) {
    console.error('获取收藏集合统计失败:', error);
    return errorResponse('获取收藏集合统计失败', 500);
  }
}