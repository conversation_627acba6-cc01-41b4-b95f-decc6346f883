// 衣物管理处理器
import { ClothingModel, ClothingCreateSchema, ClothingUpdateSchema, ClothingQuerySchema } from '../models/clothing';
import { success, error, validationError, notFound } from '../utils/response';

// 获取衣物列表
export async function list(request) {
  try {
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams);
    
    // 验证查询参数
    const validation = ClothingQuerySchema.safeParse({
      ...queryParams,
      page: queryParams.page ? parseInt(queryParams.page) : 1,
      pageSize: queryParams.pageSize ? parseInt(queryParams.pageSize) : 20
    });
    
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const clothingModel = new ClothingModel(request.env.DB);
    const result = await clothingModel.findByUserId(request.user.id, validation.data);

    return success(result);

  } catch (err) {
    console.error('List clothing error:', err);
    return error('获取衣物列表失败', 500);
  }
}

// 创建衣物
export async function create(request) {
  try {
    const body = await request.json();
    
    // 验证输入数据
    const validation = ClothingCreateSchema.safeParse(body);
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const clothingModel = new ClothingModel(request.env.DB);
    const clothing = await clothingModel.createClothing(request.user.id, validation.data);

    return success(clothing, '衣物创建成功');

  } catch (err) {
    console.error('Create clothing error:', err);
    return error('创建衣物失败', 500);
  }
}

// 获取单个衣物
export async function get(request) {
  try {
    const { id } = request.params;
    
    const clothingModel = new ClothingModel(request.env.DB);
    const clothing = await clothingModel.findById(id, request.user.id);

    if (!clothing) {
      return notFound('衣物不存在');
    }

    return success(clothing);

  } catch (err) {
    console.error('Get clothing error:', err);
    return error('获取衣物失败', 500);
  }
}

// 更新衣物
export async function update(request) {
  try {
    const { id } = request.params;
    const body = await request.json();
    
    // 验证输入数据
    const validation = ClothingUpdateSchema.safeParse(body);
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const clothingModel = new ClothingModel(request.env.DB);
    
    // 检查衣物是否存在
    const existingClothing = await clothingModel.findById(id, request.user.id);
    if (!existingClothing) {
      return notFound('衣物不存在');
    }

    const updatedClothing = await clothingModel.updateClothing(id, request.user.id, validation.data);

    return success(updatedClothing, '衣物更新成功');

  } catch (err) {
    console.error('Update clothing error:', err);
    return error('更新衣物失败', 500);
  }
}

// 删除衣物
export async function deleteClothing(request) {
  try {
    const { id } = request.params;
    
    const clothingModel = new ClothingModel(request.env.DB);
    
    // 检查衣物是否存在
    const existingClothing = await clothingModel.findById(id, request.user.id);
    if (!existingClothing) {
      return notFound('衣物不存在');
    }

    // 删除衣物记录，同时获取需要清理的图片URLs
    const deleteResult = await clothingModel.deleteClothing(id, request.user.id);
    
    if (deleteResult.success) {
      // 异步清理R2存储中的文件，不阻塞响应
      if (deleteResult.imageUrls && deleteResult.imageUrls.length > 0) {
        // 在后台异步执行文件清理
        request.ctx.waitUntil(cleanupR2Files(request.env.STORAGE, deleteResult.imageUrls, request.user.id));
      }
      
      return success(null, '衣物删除成功');
    } else {
      return error('删除衣物失败', 500);
    }

  } catch (err) {
    console.error('Delete clothing error:', err);
    return error('删除衣物失败', 500);
  }
}

// 清理R2文件的辅助函数
async function cleanupR2Files(storage, imageUrls, userId) {
  if (!imageUrls || imageUrls.length === 0) return;
  
  for (const imageUrl of imageUrls) {
    try {
      // 从URL中提取R2文件key
      const key = extractR2KeyFromUrl(imageUrl, userId);
      if (key) {
        await storage.delete(key);
        console.log(`Successfully deleted R2 file: ${key}`);
      }
    } catch (error) {
      // 记录错误但不中断流程
      console.error(`Failed to delete R2 file for URL ${imageUrl}:`, error);
    }
  }
}

// 从图片URL中提取R2文件key
function extractR2KeyFromUrl(imageUrl, userId) {
  try {
    // 处理可能的URL格式:
    // https://pub-{account-id}.r2.dev/yigui-images/{userId}/{timestamp}-{uuid}.{ext}
    // https://images.yigui.app/{userId}/{timestamp}-{uuid}.{ext}
    
    if (!imageUrl) return null;
    
    // 提取路径部分
    const url = new URL(imageUrl);
    let pathname = url.pathname;
    
    // 如果是通过yigui-images bucket的路径，移除前缀
    if (pathname.startsWith('/yigui-images/')) {
      pathname = pathname.substring('/yigui-images/'.length);
    } else if (pathname.startsWith('/')) {
      pathname = pathname.substring(1);
    }
    
    // 验证文件是否属于当前用户
    if (pathname.startsWith(`${userId}/`)) {
      return pathname;
    }
    
    console.warn(`File key ${pathname} does not belong to user ${userId}`);
    return null;
  } catch (error) {
    console.error('Error extracting R2 key from URL:', error);
    return null;
  }
}