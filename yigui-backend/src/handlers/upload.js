// 文件上传处理器 - R2 存储
import { success, error } from '../utils/response';

// 获取上传签名 (预签名URL)
export async function getSignature(request) {
  try {
    const url = new URL(request.url);
    const fileName = url.searchParams.get('fileName');
    const fileType = url.searchParams.get('fileType');
    
    if (!fileName || !fileType) {
      return error('缺少必要参数: fileName 和 fileType', 400);
    }

    // 验证文件类型
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(fileType)) {
      return error('不支持的文件类型', 400);
    }

    // 生成唯一的文件键
    const fileExtension = fileType.split('/')[1];
    const key = `${request.user.id}/${Date.now()}-${crypto.randomUUID()}.${fileExtension}`;
    
    // 在开发环境中，我们可能无法使用真正的预签名URL，所以直接返回直接上传的信息
    if (request.env.ENVIRONMENT === 'development') {
      return success({
        uploadUrl: null, // 开发环境不使用预签名URL
        key: key,
        expires: new Date(Date.now() + 30 * 60 * 1000).toISOString(),
        fileUrl: `https://pub-${request.env.CLOUDFLARE_ACCOUNT_ID || 'your-account-id'}.r2.dev/yigui-images/${key}`,
        useDirectUpload: true // 指示前端使用直接上传
      });
    }
    
    // 生成预签名 URL (30分钟有效期)
    const expiryTime = new Date(Date.now() + 30 * 60 * 1000); // 30 minutes
    
    try {
      // R2 预签名URL可能在某些情况下不可用，我们提供回退方案
      let presignedUrl = null;
      
      try {
        presignedUrl = await request.env.STORAGE.presign(key, {
          method: 'PUT',
          expires: expiryTime,
          httpMetadata: {
            contentType: fileType
          }
        });
      } catch (presignError) {
        console.warn('R2 presign not available, using direct upload:', presignError.message);
        // 如果预签名失败，使用直接上传模式
        return success({
          uploadUrl: null,
          key: key,
          expires: expiryTime.toISOString(),
          fileUrl: `https://pub-${request.env.CLOUDFLARE_ACCOUNT_ID || 'your-account-id'}.r2.dev/yigui-images/${key}`,
          useDirectUpload: true
        });
      }

      return success({
        uploadUrl: presignedUrl,
        key: key,
        expires: expiryTime.toISOString(),
        fileUrl: `https://pub-${request.env.CLOUDFLARE_ACCOUNT_ID || 'your-account-id'}.r2.dev/yigui-images/${key}`,
        useDirectUpload: false
      });

    } catch (r2Error) {
      console.error('R2 operation error:', r2Error);
      return error('生成上传签名失败', 500);
    }

  } catch (err) {
    console.error('Get signature error:', err);
    return error('获取上传签名失败', 500);
  }
}

// 直接上传到 R2
export async function handleUpload(request) {
  try {
    const formData = await request.formData();
    const file = formData.get('file');
    
    if (!file) {
      return error('没有上传文件', 400);
    }

    // 验证文件类型和大小
    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
    if (!allowedTypes.includes(file.type)) {
      return error('不支持的文件类型', 400);
    }

    // 限制文件大小为 10MB
    const maxSize = 10 * 1024 * 1024; // 10MB
    if (file.size > maxSize) {
      return error('文件大小超过限制 (10MB)', 400);
    }

    // 生成唯一的文件键
    const fileExtension = file.type.split('/')[1];
    const key = `${request.user.id}/${Date.now()}-${crypto.randomUUID()}.${fileExtension}`;

    try {
      // 上传到 R2
      await request.env.STORAGE.put(key, file.stream(), {
        httpMetadata: {
          contentType: file.type,
          cacheControl: 'public, max-age=********' // 1年缓存
        },
        customMetadata: {
          uploadedBy: request.user.id,
          uploadedAt: new Date().toISOString(),
          originalName: file.name
        }
      });

      const fileUrl = `https://pub-${request.env.CLOUDFLARE_ACCOUNT_ID || 'your-account-id'}.r2.dev/yigui-images/${key}`; // 默认R2 URL 或自定义域名

      return success({ 
        url: fileUrl,
        key: key,
        size: file.size,
        type: file.type,
        originalName: file.name
      }, '文件上传成功');

    } catch (r2Error) {
      console.error('R2 upload error:', r2Error);
      return error('文件上传失败', 500);
    }

  } catch (err) {
    console.error('Upload error:', err);
    return error('文件上传失败', 500);
  }
}

// 删除 R2 中的文件
export async function deleteFile(request) {
  try {
    const { key } = request.params;
    
    if (!key) {
      return error('缺少文件键', 400);
    }

    // 验证文件是否属于当前用户
    if (!key.startsWith(`${request.user.id}/`)) {
      return error('无权限删除此文件', 403);
    }

    try {
      await request.env.STORAGE.delete(key);
      return success(null, '文件删除成功');
    } catch (r2Error) {
      console.error('R2 delete error:', r2Error);
      return error('文件删除失败', 500);
    }

  } catch (err) {
    console.error('Delete file error:', err);
    return error('删除文件失败', 500);
  }
}