// 收藏管理处理器
import { 
  FavoritesModel, 
  FavoriteToggleSchema, 
  FavoriteQuerySchema, 
  FavoriteBatchSchema,
  FavoriteStatusSchema 
} from '../models/favorites';
import { ShareService } from '../services/shareService.js';
import { success, error, validationError, notFound } from '../utils/response';

// 切换收藏状态
export async function toggle(request) {
  try {
    const body = await request.json();
    
    // 验证输入数据
    const validation = FavoriteToggleSchema.safeParse(body);
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const { itemType, itemId } = validation.data;
    const favoritesModel = new FavoritesModel(request.env.DB);
    
    // 验证项目是否存在
    const itemExists = await validateItemExists(request.env.DB, itemType, itemId, request.user.id);
    if (!itemExists) {
      return notFound('项目不存在或无权限访问');
    }

    const result = await favoritesModel.toggleFavorite(request.user.id, itemType, itemId);

    if (!result.success) {
      return error('收藏操作失败', 500);
    }

    return success({
      itemType,
      itemId,
      isFavorited: result.isFavorited,
      action: result.action
    }, result.action === 'added' ? '已添加到收藏' : '已从收藏中移除');

  } catch (err) {
    console.error('Toggle favorite error:', err);
    return error('收藏操作失败', 500);
  }
}

// 获取收藏列表
export async function list(request) {
  try {
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams);
    
    // 验证查询参数
    const validation = FavoriteQuerySchema.safeParse({
      ...queryParams,
      page: queryParams.page ? parseInt(queryParams.page) : 1,
      pageSize: queryParams.pageSize ? parseInt(queryParams.pageSize) : 20
    });
    
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const favoritesModel = new FavoritesModel(request.env.DB);
    const result = await favoritesModel.getFavorites(request.user.id, validation.data);

    return success(result);

  } catch (err) {
    console.error('List favorites error:', err);
    return error('获取收藏列表失败', 500);
  }
}

// 批量收藏操作
export async function batch(request) {
  try {
    const body = await request.json();
    
    // 验证输入数据
    const validation = FavoriteBatchSchema.safeParse(body);
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const { action, items } = validation.data;
    const favoritesModel = new FavoritesModel(request.env.DB);

    // 验证所有项目是否存在（可选，根据性能需求决定）
    // 这里为了性能考虑，暂时跳过验证，让数据库的外键约束来处理

    const result = await favoritesModel.batchFavorites(request.user.id, action, items);

    const message = action === 'add' 
      ? `成功添加 ${result.successCount}/${result.totalCount} 个项目到收藏`
      : `成功从收藏中移除 ${result.successCount}/${result.totalCount} 个项目`;

    return success(result, message);

  } catch (err) {
    console.error('Batch favorites error:', err);
    return error('批量收藏操作失败', 500);
  }
}

// 检查收藏状态
export async function status(request) {
  try {
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams);
    
    // 验证查询参数
    const validation = FavoriteStatusSchema.safeParse(queryParams);
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const { itemType, itemIds } = validation.data;
    const favoritesModel = new FavoritesModel(request.env.DB);
    
    const statusMap = await favoritesModel.checkFavoriteStatus(request.user.id, itemType, itemIds);

    return success({
      itemType,
      statusMap
    });

  } catch (err) {
    console.error('Check favorite status error:', err);
    return error('检查收藏状态失败', 500);
  }
}

// 搜索收藏
export async function search(request) {
  try {
    const url = new URL(request.url);
    const queryParams = Object.fromEntries(url.searchParams);
    
    // 验证查询参数
    const validation = FavoriteQuerySchema.safeParse({
      ...queryParams,
      page: queryParams.page ? parseInt(queryParams.page) : 1,
      pageSize: queryParams.pageSize ? parseInt(queryParams.pageSize) : 20
    });
    
    if (!validation.success) {
      return validationError(validation.error.issues);
    }

    const favoritesModel = new FavoritesModel(request.env.DB);
    const result = await favoritesModel.getFavorites(request.user.id, validation.data);

    // 添加搜索相关的元数据
    const response = {
      ...result,
      searchMetadata: {
        hasSearch: !!validation.data.search,
        searchQuery: validation.data.search || null,
        appliedFilters: Object.keys(validation.data)
          .filter(key => !['page', 'pageSize', 'sortBy', 'order'].includes(key) && validation.data[key])
          .reduce((acc, key) => {
            acc[key] = validation.data[key];
            return acc;
          }, {})
      }
    };

    return success(response);

  } catch (err) {
    console.error('Search favorites error:', err);
    return error('搜索收藏失败', 500);
  }
}

// 获取搜索建议
export async function suggestions(request) {
  try {
    const url = new URL(request.url);
    const query = url.searchParams.get('q') || '';
    const limit = parseInt(url.searchParams.get('limit')) || 10;

    if (query.length < 2) {
      return success([]);
    }

    const favoritesModel = new FavoritesModel(request.env.DB);
    const suggestions = await favoritesModel.getSearchSuggestions(request.user.id, query, limit);

    return success(suggestions);

  } catch (err) {
    console.error('Get search suggestions error:', err);
    return error('获取搜索建议失败', 500);
  }
}

// 获取热门搜索词
export async function popular(request) {
  try {
    const url = new URL(request.url);
    const limit = parseInt(url.searchParams.get('limit')) || 5;

    const favoritesModel = new FavoritesModel(request.env.DB);
    const popularTerms = await favoritesModel.getPopularSearchTerms(request.user.id, limit);

    return success(popularTerms);

  } catch (err) {
    console.error('Get popular search terms error:', err);
    return error('获取热门搜索词失败', 500);
  }
}

// 获取收藏统计
export async function stats(request) {
  try {
    const favoritesModel = new FavoritesModel(request.env.DB);
    const stats = await favoritesModel.getFavoriteStats(request.user.id);

    return success(stats);

  } catch (err) {
    console.error('Get favorite stats error:', err);
    return error('获取收藏统计失败', 500);
  }
}

// 分享收藏
export async function share(request) {
  try {
    const body = await request.json();
    const { itemType, itemId, expiresIn, allowPublic } = body;

    // 验证必需参数
    if (!itemType || !itemId) {
      return validationError([{ message: '分享类型和项目ID不能为空' }]);
    }

    // 验证分享类型
    const validItemTypes = ['clothing', 'outfit'];
    if (!validItemTypes.includes(itemType)) {
      return validationError([{ message: '不支持的分享类型' }]);
    }

    // 验证用户是否收藏了该项目
    const favoritesModel = new FavoritesModel(request.env.DB);
    const isFavorited = await favoritesModel.isFavorited(request.user.id, itemType, itemId);
    
    if (!isFavorited) {
      return notFound('只能分享已收藏的项目');
    }

    // 验证项目是否存在且属于用户
    const itemExists = await validateItemExists(request.env.DB, itemType, itemId, request.user.id);
    if (!itemExists) {
      return notFound('项目不存在或无权限分享');
    }

    // 创建分享
    const shareService = new ShareService(request.env.DB);
    const share = await shareService.createShare({
      userId: request.user.id,
      shareType: itemType,
      targetId: itemId,
      options: {
        expiresIn,
        allowPublic
      }
    });

    return success({
      share,
      message: '分享创建成功'
    });

  } catch (err) {
    console.error('Share favorite error:', err);
    return error(err.message || '创建分享失败', 500);
  }
}

// 批量分享收藏
export async function batchShare(request) {
  try {
    const body = await request.json();
    const { items, expiresIn, allowPublic } = body;

    if (!items || !Array.isArray(items) || items.length === 0) {
      return validationError([{ message: '分享项目列表不能为空' }]);
    }

    if (items.length > 20) {
      return validationError([{ message: '批量分享数量不能超过20个' }]);
    }

    const favoritesModel = new FavoritesModel(request.env.DB);
    const shareService = new ShareService(request.env.DB);
    const results = [];
    const errors = [];

    // 批量创建分享
    for (const item of items) {
      try {
        const { itemType, itemId } = item;
        
        if (!itemType || !itemId) {
          errors.push({
            item,
            error: '分享类型和项目ID不能为空'
          });
          continue;
        }

        // 验证是否已收藏
        const isFavorited = await favoritesModel.isFavorited(request.user.id, itemType, itemId);
        if (!isFavorited) {
          errors.push({
            item,
            error: '只能分享已收藏的项目'
          });
          continue;
        }

        // 验证项目是否存在
        const itemExists = await validateItemExists(request.env.DB, itemType, itemId, request.user.id);
        if (!itemExists) {
          errors.push({
            item,
            error: '项目不存在或无权限分享'
          });
          continue;
        }

        const share = await shareService.createShare({
          userId: request.user.id,
          shareType: itemType,
          targetId: itemId,
          options: {
            expiresIn,
            allowPublic
          }
        });

        results.push({
          item,
          share
        });

      } catch (error) {
        errors.push({
          item,
          error: error.message
        });
      }
    }

    return success({
      shares: results,
      errors,
      summary: {
        total: items.length,
        success: results.length,
        failed: errors.length
      }
    }, `成功创建 ${results.length}/${items.length} 个分享`);

  } catch (err) {
    console.error('Batch share favorites error:', err);
    return error('批量分享失败', 500);
  }
}

// 获取收藏的分享列表
export async function getShares(request) {
  try {
    const url = new URL(request.url);
    const page = parseInt(url.searchParams.get('page')) || 1;
    const pageSize = Math.min(parseInt(url.searchParams.get('pageSize')) || 20, 100);
    const itemType = url.searchParams.get('itemType');

    const shareService = new ShareService(request.env.DB);
    const result = await shareService.getUserShares(request.user.id, {
      page,
      pageSize,
      shareType: itemType
    });

    // 过滤出收藏相关的分享
    const favoriteShares = {
      ...result,
      shares: result.shares.filter(share => 
        share.shareType === 'clothing' || share.shareType === 'outfit'
      )
    };

    return success(favoriteShares);

  } catch (err) {
    console.error('Get favorite shares error:', err);
    return error('获取分享列表失败', 500);
  }
}

// 验证项目是否存在的辅助函数
async function validateItemExists(db, itemType, itemId, userId) {
  try {
    if (itemType === 'clothing') {
      const result = await db.prepare(`
        SELECT id FROM clothing WHERE id = ? AND user_id = ?
      `).bind(itemId, userId).first();
      return !!result;
    } else if (itemType === 'outfit') {
      const result = await db.prepare(`
        SELECT id FROM outfits WHERE id = ? AND user_id = ?
      `).bind(itemId, userId).first();
      return !!result;
    }
    return false;
  } catch (error) {
    console.error('Validate item exists error:', error);
    return false;
  }
}