// JWT 工具函数
import { sign, verify } from '@tsndr/cloudflare-worker-jwt';

export async function generateToken(payload, secret, expiresIn = '7d') {
  // 计算过期时间
  const expirationTime = Math.floor(Date.now() / 1000) + getExpirationSeconds(expiresIn);
  
  const tokenPayload = {
    ...payload,
    iat: Math.floor(Date.now() / 1000), // 签发时间
    exp: expirationTime // 过期时间
  };
  
  return await sign(tokenPayload, secret);
}

export async function generateRefreshToken(payload, secret) {
  // 刷新token有效期为30天
  return await generateToken(payload, secret, '30d');
}

export async function verifyToken(token, secret) {
  try {
    const isValid = await verify(token, secret);
    if (!isValid) {
      return { valid: false, error: 'Invalid token' };
    }
    
    // 解析payload
    const payload = JSON.parse(atob(token.split('.')[1]));
    
    // 检查是否过期
    if (payload.exp && payload.exp < Math.floor(Date.now() / 1000)) {
      return { valid: false, error: 'Token expired' };
    }
    
    return { valid: true, payload };
  } catch (error) {
    return { valid: false, error: error.message };
  }
}

export function decodeToken(token) {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    return payload;
  } catch (error) {
    throw new Error('Invalid token format');
  }
}

function getExpirationSeconds(duration) {
  const units = {
    's': 1,
    'm': 60,
    'h': 3600,
    'd': 86400,
    'w': 604800
  };
  
  const match = duration.match(/^(\d+)([smhdw])$/);
  if (!match) {
    throw new Error('Invalid duration format. Use format like "7d", "2h", "30m"');
  }
  
  const [, value, unit] = match;
  return parseInt(value) * units[unit];
}