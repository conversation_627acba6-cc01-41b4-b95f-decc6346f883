/**
 * ID生成工具
 */

import { randomBytes } from 'crypto';

/**
 * 生成唯一ID
 * @returns {string} 唯一ID
 */
export function generateId() {
  return randomBytes(16).toString('hex');
}

/**
 * 生成分享令牌
 * @param {number} length - 令牌长度（字节数）
 * @returns {string} 分享令牌
 */
export function generateShareToken(length = 32) {
  return randomBytes(length).toString('base64url');
}

/**
 * 生成短分享码（用于短链接）
 * @param {number} length - 码长度
 * @returns {string} 短分享码
 */
export function generateShortCode(length = 8) {
  const chars = 'ABCDEFGHJKMNPQRSTUVWXYZabcdefghjkmnpqrstuvwxyz23456789';
  let result = '';
  
  for (let i = 0; i < length; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  
  return result;
}