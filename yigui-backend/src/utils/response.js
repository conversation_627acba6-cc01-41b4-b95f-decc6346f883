// 响应工具函数
import { corsHeaders } from '../middleware/cors';

export function json(data, status = 200, additionalHeaders = {}) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      ...corsHeaders,
      ...additionalHeaders
    }
  });
}

export function success(data, message = 'Success') {
  return json({
    success: true,
    message,
    data
  });
}

export function error(message, status = 400, code = null) {
  return json({
    success: false,
    error: message,
    code
  }, status);
}

export function badRequest(message = 'Bad Request') {
  return error(message, 400, 'BAD_REQUEST');
}

export function unauthorized(message = 'Unauthorized') {
  return error(message, 401, 'UNAUTHORIZED');
}

export function forbidden(message = 'Forbidden') {
  return error(message, 403, 'FORBIDDEN');
}

export function notFound(message = 'Not Found') {
  return error(message, 404, 'NOT_FOUND');
}

export function validationError(errors) {
  return json({
    success: false,
    error: 'Validation failed',
    code: 'VALIDATION_ERROR',
    details: errors
  }, 400);
}

export function serverError(message = 'Internal Server Error') {
  return error(message, 500, 'SERVER_ERROR');
}