/**
 * 分享服务
 * 处理分享相关的业务逻辑
 */

import { ShareModel } from '../models/share.js';
import { FavoritesModel } from '../models/favorites.js';
import { generateShareToken, generateShortCode } from '../utils/id.js';

export class ShareService {
  constructor(db) {
    this.shareModel = new ShareModel(db);
    this.favoritesModel = new FavoritesModel(db);
  }

  /**
   * 创建分享链接
   * @param {Object} params - 分享参数
   * @param {string} params.userId - 用户ID
   * @param {string} params.shareType - 分享类型
   * @param {string} params.targetId - 目标ID
   * @param {Object} params.options - 分享选项
   * @param {number} params.options.expiresIn - 过期时间（秒）
   * @param {boolean} params.options.allowPublic - 是否允许公开访问
   * @returns {Promise<Object>} 分享信息
   */
  async createShare(params) {
    const { userId, shareType, targetId, options = {} } = params;
    const { expiresIn, allowPublic = false } = options;

    // 验证分享类型和目标是否存在
    await this.validateShareTarget(shareType, targetId, userId);

    // 生成分享令牌
    const shareToken = generateShareToken();
    const shortCode = generateShortCode();

    // 计算过期时间
    let expiresAt = null;
    if (expiresIn) {
      expiresAt = new Date(Date.now() + expiresIn * 1000).toISOString();
    }

    // 创建分享记录
    const share = await this.shareModel.createShare({
      userId,
      shareType,
      targetId,
      shareToken,
      expiresAt
    });

    // 生成分享链接
    const shareUrl = this.generateShareUrl(shareToken, shareType);
    const shortUrl = this.generateShortUrl(shortCode);

    return {
      ...share,
      shareUrl,
      shortUrl,
      shortCode,
      allowPublic,
      isExpired: false
    };
  }

  /**
   * 获取分享内容
   * @param {string} shareToken - 分享令牌
   * @returns {Promise<Object>} 分享内容
   */
  async getShareContent(shareToken) {
    // 获取分享记录
    const share = await this.shareModel.getShareByToken(shareToken);
    
    if (!share) {
      throw new Error('分享链接不存在或已过期');
    }

    // 增加访问次数
    await this.shareModel.incrementViewCount(shareToken);

    // 根据分享类型获取内容
    let content;
    switch (share.shareType) {
      case 'clothing':
        content = await this.getClothingShareContent(share.targetId);
        break;
      case 'outfit':
        content = await this.getOutfitShareContent(share.targetId);
        break;
      case 'collection':
        content = await this.getCollectionShareContent(share.targetId);
        break;
      default:
        throw new Error('不支持的分享类型');
    }

    return {
      share: {
        id: share.id,
        shareType: share.shareType,
        viewCount: share.viewCount + 1,
        createdAt: share.createdAt
      },
      content
    };
  }

  /**
   * 获取用户的分享列表
   * @param {string} userId - 用户ID
   * @param {Object} options - 查询选项
   * @returns {Promise<Object>} 分享列表
   */
  async getUserShares(userId, options = {}) {
    const result = await this.shareModel.getUserShares(userId, options);
    
    // 为每个分享添加URL信息
    const sharesWithUrls = result.shares.map(share => ({
      ...share,
      shareUrl: this.generateShareUrl(share.shareToken, share.shareType),
      isExpired: share.expiresAt ? new Date(share.expiresAt) < new Date() : false
    }));

    return {
      ...result,
      shares: sharesWithUrls
    };
  }

  /**
   * 删除分享
   * @param {string} shareId - 分享ID
   * @param {string} userId - 用户ID
   * @returns {Promise<boolean>} 是否成功
   */
  async deleteShare(shareId, userId) {
    return await this.shareModel.deleteShare(shareId, userId);
  }

  /**
   * 验证分享目标是否存在且用户有权限
   * @private
   */
  async validateShareTarget(shareType, targetId, userId) {
    switch (shareType) {
      case 'clothing':
        // 验证衣物是否存在且属于用户
        const clothing = await this.getClothingById(targetId);
        if (!clothing || clothing.userId !== userId) {
          throw new Error('衣物不存在或无权限分享');
        }
        break;
      case 'outfit':
        // 验证搭配是否存在且属于用户
        const outfit = await this.getOutfitById(targetId);
        if (!outfit || outfit.userId !== userId) {
          throw new Error('搭配不存在或无权限分享');
        }
        break;
      case 'collection':
        // 验证收藏集合是否存在且属于用户
        const collection = await this.getCollectionById(targetId);
        if (!collection || collection.userId !== userId) {
          throw new Error('收藏集合不存在或无权限分享');
        }
        break;
      default:
        throw new Error('不支持的分享类型');
    }
  }

  /**
   * 获取衣物分享内容
   * @private
   */
  async getClothingShareContent(clothingId) {
    // 这里需要调用衣物服务获取详细信息
    // 暂时返回基础结构，实际实现需要集成衣物服务
    return {
      type: 'clothing',
      id: clothingId,
      // 实际实现中需要获取完整的衣物信息
      title: '分享的衣物',
      description: '来看看这件衣物',
      images: [],
      metadata: {}
    };
  }

  /**
   * 获取搭配分享内容
   * @private
   */
  async getOutfitShareContent(outfitId) {
    // 这里需要调用搭配服务获取详细信息
    return {
      type: 'outfit',
      id: outfitId,
      title: '分享的搭配',
      description: '来看看这个搭配',
      images: [],
      items: [],
      metadata: {}
    };
  }

  /**
   * 获取收藏集合分享内容
   * @private
   */
  async getCollectionShareContent(collectionId) {
    // 这里需要调用收藏服务获取详细信息
    return {
      type: 'collection',
      id: collectionId,
      title: '分享的收藏集合',
      description: '来看看这个收藏集合',
      items: [],
      metadata: {}
    };
  }

  /**
   * 生成分享URL
   * @private
   */
  generateShareUrl(shareToken, shareType) {
    // 实际实现中应该从环境变量获取域名
    const baseUrl = 'https://yigui.app';
    return `${baseUrl}/share/${shareType}/${shareToken}`;
  }

  /**
   * 生成短链接URL
   * @private
   */
  generateShortUrl(shortCode) {
    const baseUrl = 'https://yigui.app';
    return `${baseUrl}/s/${shortCode}`;
  }

  /**
   * 清理过期分享
   * @returns {Promise<number>} 清理的记录数
   */
  async cleanupExpiredShares() {
    return await this.shareModel.deleteExpiredShares();
  }

  // 以下方法需要在实际实现中集成相应的服务
  async getClothingById(id) {
    // 使用数据库查询衣物信息
    const result = await this.shareModel.db.prepare(`
      SELECT id, user_id FROM clothing WHERE id = ?
    `).bind(id).first();
    
    return result ? { id: result.id, userId: result.user_id } : null;
  }

  async getOutfitById(id) {
    // 使用数据库查询搭配信息
    const result = await this.shareModel.db.prepare(`
      SELECT id, user_id FROM outfits WHERE id = ?
    `).bind(id).first();
    
    return result ? { id: result.id, userId: result.user_id } : null;
  }

  async getCollectionById(id) {
    // 使用数据库查询收藏集合信息
    const result = await this.shareModel.db.prepare(`
      SELECT id, user_id FROM collections WHERE id = ?
    `).bind(id).first();
    
    return result ? { id: result.id, userId: result.user_id } : null;
  }
}