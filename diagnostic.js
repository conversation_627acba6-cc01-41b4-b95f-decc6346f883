#!/usr/bin/env node

/**
 * Diagnostic script to check project setup
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Running diagnostic checks...\n');

// Check 1: node_modules exists
const nodeModulesExists = fs.existsSync('./node_modules');
console.log(`📦 node_modules: ${nodeModulesExists ? '✅ EXISTS' : '❌ MISSING'}`);

// Check 2: Key files exist
const keyFiles = [
  'package.json',
  'vite.config.js',
  'src/main.js',
  'src/stores/collectionStore.js',
  'src/components/collection/VirtualCollectionGrid.vue',
  'src/pages/main/myCollections/MyCollectionsPage.vue'
];

console.log('\n📁 Key files:');
keyFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`   ${file}: ${exists ? '✅' : '❌'}`);
});

// Check 3: Dependencies in package.json
console.log('\n📋 Key dependencies:');
try {
  const packageJson = JSON.parse(fs.readFileSync('./package.json', 'utf8'));
  const keyDeps = [
    '@dcloudio/uni-app',
    '@dcloudio/uni-ui',
    'vue',
    'pinia',
    'vite'
  ];
  
  keyDeps.forEach(dep => {
    const hasInDeps = packageJson.dependencies?.[dep];
    const hasInDevDeps = packageJson.devDependencies?.[dep];
    const version = hasInDeps || hasInDevDeps;
    console.log(`   ${dep}: ${version ? `✅ ${version}` : '❌ MISSING'}`);
  });
} catch (error) {
  console.log('   ❌ Error reading package.json');
}

// Check 4: Import paths
console.log('\n🔗 Import validation:');
try {
  const myCollectionsContent = fs.readFileSync('./src/pages/main/myCollections/MyCollectionsPage.vue', 'utf8');
  const imports = [
    { name: 'collectionStore', path: './src/stores/collectionStore.js' },
    { name: 'VirtualCollectionGrid', path: './src/components/collection/VirtualCollectionGrid.vue' },
    { name: 'VirtualScroll', path: './src/components/common/VirtualScroll.vue' }
  ];
  
  imports.forEach(({ name, path: filePath }) => {
    const fileExists = fs.existsSync(filePath);
    const isImported = myCollectionsContent.includes(name);
    console.log(`   ${name}: ${fileExists && isImported ? '✅' : '❌'} (file: ${fileExists ? '✅' : '❌'}, imported: ${isImported ? '✅' : '❌'})`);
  });
} catch (error) {
  console.log('   ❌ Error validating imports');
}

console.log('\n🎯 Recommendations:');
if (!nodeModulesExists) {
  console.log('   1. Run: npm install');
}
console.log('   2. Run: npm run dev:h5');
console.log('   3. Open: http://localhost:5174');
console.log('   4. Navigate to MyCollections page');

console.log('\n✨ Diagnostic complete!');
