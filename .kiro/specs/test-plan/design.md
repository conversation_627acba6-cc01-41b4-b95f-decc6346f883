# 测试计划设计文档

## 1. 概述

本设计文档旨在为项目建立一个全面、分层的测试策略。该策略将利用现有工具，并引入必要的实践来确保代码质量。测试将分为三个主要层次：单元测试、集成测试和端到端（E2E）测试。

## 2. 架构

测试架构将基于 `Vitest` 测试框架，并结合 `@vue/test-utils` 和 `uni-app` 的原生测试工具。

```mermaid
graph TD
    A[开发流程] --> B{代码提交};
    B --> C[CI/CD 管道];
    C --> D[单元测试 (Vitest)];
    C --> E[集成测试 (Vitest)];
    C --> F[E2E 测试 (uni-automator)];
    D --> G{测试通过?};
    E --> G;
    F --> G;
    G -- 是 --> H[部署];
    G -- 否 --> I[通知开发人员];
```

## 3. 组件和接口

### 3.1. 单元测试

*   **工具:** `Vitest` + `@vue/test-utils` + `happy-dom`
*   **目标:**
    *   **Vue 组件:** 测试单个 Vue 组件的渲染、用户交互和 props/events 行为。测试将集中在 `src/components` 和 `src/pages` 中的组件。
    *   **Stores (Pinia):** 测试 `src/stores` 中每个 store 的 state 变化、actions 和 getters 的正确性。
    *   **工具函数 (Utils):** 测试 `src/utils` 中的纯函数的输入和输出。
*   **测试文件位置:** 与被测试文件相邻的 `__tests__` 目录中，或使用 `.test.js` / `.spec.js` 后缀。例如 `src/components/__tests__/MyComponent.test.js`。

### 3.2. 集成测试

*   **工具:** `Vitest`
*   **目标:**
    *   **API 集成:** 模拟 API 请求（使用 `msw` 或类似的库），测试前端服务层（例如 `src/api`）在接收到模拟数据或错误时是否能正确处理。
    *   **多组件交互:** 测试需要多个组件协同工作的复杂用户流程（例如，一个表单和其提交按钮）。
*   **测试文件位置:** `tests/integration/` 目录。

### 3.3. 端到端 (E2E) 测试

*   **工具:** `@dcloudio/uni-automator`
*   **目标:** 模拟真实用户在 `uni-app` 环境中的操作，覆盖关键业务流程。
    *   用户认证流程（注册、登录、忘记密码）。
    *   衣物管理流程（添加、查看、编辑衣物）。
    *   穿搭创建流程。
*   **测试文件位置:** `tests/e2e/` 目录。

## 4. 数据模型

测试数据将通过以下方式管理：
*   **单元/集成测试:** 在测试文件中使用固定的 mock 数据或工厂函数生成。
*   **E2E 测试:** 依赖一个独立的测试数据库或通过 API 在测试执行前设置和清理数据。

## 5. 错误处理

*   测试用例将明确覆盖错误场景，例如：
    *   API 返回错误码。
    *   用户输入无效数据。
    *   组件在缺少必要 props 的情况下渲染。
*   `errorHandler.js` 等工具函数将有专门的单元测试来验证其行为。

## 6. 测试策略

1.  **识别关键路径:** 首先为应用的核心功能（如认证、核心 CRUD 操作）编写 E2E 测试。
2.  **自下而上:**
    *   为所有新的 `utils` 和 `stores` 编写单元测试。
    *   为所有新的 UI 组件编写单元测试。
    *   在功能开发中，为前端和后端的交互点编写集成测试。
3.  **持续集成 (CI):**
    *   在代码合并到主分支前，CI 管道（例如 GitHub Actions）将自动运行所有测试。
    *   测试覆盖率报告将由 `vitest run --coverage` 生成，以监控测试覆盖范围。

## 7. 测试文件清理

*   在所有测试阶段完成并确认应用质量后，将执行一个脚本来删除所有测试相关的文件和配置。
*   **要删除的文件/目录:**
    *   `src/**/__tests__/`
    *   `src/**/*.test.js`
    *   `src/**/*.spec.js`
    *   `tests/`
    *   `vitest.config.js`
    *   `package.json` 中的测试相关脚本和依赖项。