# 实施计划

- [x] 1. 搭建测试基础设施
  - [x] 1.1. 确认 `Vitest` 和 `@vue/test-utils` 已正确安装并配置。
    - 检查 `package.json` 和 `vitest.config.js`。
    - _需求: 4.1_
  - [x] 1.2. 创建 `tests/integration` 和 `tests/e2e` 目录。
    - _需求: 2.2, 3.1_
  - [x] 1.3. (可选) 安装并配置 `msw` 用于 API mocking。
    - `npm install msw --save-dev`
    - 创建 `src/mocks` 目录来存放 mock handlers。
    - _需求: 2.1, 2.2_

- [x] 2. 编写单元测试
  - [x] 2.1. 为 `src/utils` 中的核心工具函数编写单元测试。
    - 创建 `src/utils/__tests__/errorHandler.test.js` 等文件。
    - _需求: 1.1, 1.3_
  - [x] 2.2. 为 `src/stores` 中的 Pinia stores 编写单元测试。
    - 重点测试 `userStore`, `clothingStore`, `outfitStore`。
    - 创建 `src/stores/__tests__/userStore.test.js` 等文件。
    - _需求: 1.1, 1.3_
  - [x] 2.3. 为 `src/components/common` 中的通用组件编写单元测试。
    - 例如 `ImageUploader.vue`。
    - 创建 `src/components/common/__tests__/ImageUploader.test.js`。
    - _需求: 1.1, 1.3_

- [x] 3. 编写集成测试
  - [x] 3.1. 为用户认证 API (`src/api/auth.js`) 编写集成测试。
    - 使用 `msw` 模拟登录、注册的成功和失败场景。
    - 创建 `tests/integration/auth.test.js`。
    - _需求: 2.1, 2.2_
  - [x] 3.2. 为衣物管理 API (`src/api/clothing.js`) 编写集成测试。
    - 模拟获取、添加、删除衣物的 API 交互。
    - 创建 `tests/integration/clothing.test.js`。
    - _需求: 2.1, 2.2_

- [x] 4. 编写端到端 (E2E) 测试
  - [x] 4.1. 配置 `@dcloudio/uni-automator`。
    - 确保 `launchApp` 等方法可以正常启动应用进行测试。
    - _需求: 4.1_
  - [x] 4.2. 编写用户登录流程的 E2E 测试。
    - 创建 `tests/e2e/login.test.js`。
    - 自动化输入用户名、密码并验证登录结果。
    - _需求: 3.1, 3.2_
  - [x] 4.3. 编写添加衣物流程的 E2E 测试。
    - 创建 `tests/e2e/add-clothing.test.js`。
    - 自动化导航到添加页面、填写表单、上传图片并验证结果。
    - _需求: 3.1, 3.2_

- [x] 5. 配置持续集成 (CI)
  - [x] 5.1. 创建一个 CI 配置文件 (例如 `.github/workflows/ci.yml`)。
    - 配置在 `push` 或 `pull_request` 事件时触发。
    - _需求: 4.1_
  - [x] 5.2. 在 CI 脚本中添加运行所有测试的命令。
    - `npm test` 或 `vitest run`。
    - _需求: 4.1, 4.2_
  - [x] 5.3. 配置测试覆盖率报告的生成和上传。
    - `vitest run --coverage`。
    - _需求: 4.1_

- [ ] 6. 执行测试并清理
  - [x] 6.1. 手动或通过 CI 运行所有测试，确保全部通过。
    - _需求: 1.3, 2.2, 3.2_
  - [x] 6.2. 创建一个清理脚本 `scripts/cleanup-tests.js`。
    - 该脚本将删除所有测试文件、目录和 `package.json` 中的相关依赖。
    - _需求: 5.1_
  - [x] 6.3. 执行清理脚本，并手动验证所有测试文件已被移除。
    - _需求: 5.1_