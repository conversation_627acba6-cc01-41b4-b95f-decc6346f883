# 我的收藏功能完善设计文档

## 概述

本设计文档基于需求文档，为"我的收藏"功能的完善提供详细的技术设计方案。系统将实现完整的收藏功能，包括衣物收藏、搭配收藏、数据管理、统计分析、分享功能等。

设计采用前后端分离架构，前端使用Vue 3 + Pinia进行状态管理，后端基于Cloudflare Workers + D1数据库，确保高性能和可扩展性。

## 架构设计

### 整体架构

```mermaid
graph TB
    A[前端 Vue3 + UniApp] --> B[API Gateway]
    B --> C[收藏服务]
    B --> D[衣物服务]
    B --> E[搭配服务]
    B --> F[分享服务]
    
    C --> G[D1 数据库]
    D --> G
    E --> G
    F --> H[R2 存储]
    
    I[状态管理 Pinia] --> A
    J[本地缓存] --> A
```

### 数据流设计

1. **收藏操作流程**：用户操作 → 本地状态更新 → API调用 → 数据库更新 → 状态同步
2. **数据同步策略**：乐观更新 + 错误回滚机制
3. **缓存策略**：本地缓存 + 服务端缓存，支持离线操作

## 组件和接口设计

### 前端组件架构

#### 1. 收藏页面组件 (MyCollectionsPage.vue)
- **功能**：主收藏页面，包含标签切换和内容展示
- **子组件**：
  - `CollectionTabs`: 标签切换器
  - `ClothingCollectionView`: 衣物收藏视图
  - `OutfitCollectionView`: 搭配收藏视图
  - `CollectionSearchBar`: 搜索栏
  - `CollectionFilters`: 筛选器

#### 2. 收藏项组件
- **ClothingCollectionCard**: 收藏衣物卡片
- **OutfitCollectionCard**: 收藏搭配卡片
- **CollectionItemActions**: 收藏项操作菜单

#### 3. 收藏管理组件
- **BatchOperationBar**: 批量操作工具栏
- **CollectionStatsView**: 收藏统计视图
- **ShareCollectionModal**: 分享收藏弹窗

### 状态管理设计 (Pinia Store)

#### CollectionStore 增强

```javascript
// 状态结构
state: {
  // 衣物收藏
  favoriteClothings: [],
  clothingFilters: {
    category: 'all',
    color: null,
    season: null,
    sortBy: 'created_at'
  },
  
  // 搭配收藏
  favoriteOutfits: [],
  outfitFilters: {
    occasion: 'all',
    sortBy: 'created_at'
  },
  
  // 收藏状态缓存
  favoriteStatusMap: new Map(),
  
  // 收藏集合
  collections: [],
  
  // 统计数据
  stats: {
    totalClothings: 0,
    totalOutfits: 0,
    categoryDistribution: {},
    recentActivity: []
  },
  
  // UI状态
  isLoading: false,
  selectedItems: [],
  batchMode: false,
  searchQuery: '',
  
  // 分页状态
  pagination: {
    clothing: { page: 1, hasMore: true },
    outfit: { page: 1, hasMore: true }
  }
}
```

### API接口设计

#### 1. 收藏管理接口

```javascript
// 切换收藏状态
POST /api/favorites/toggle
{
  "itemType": "clothing|outfit",
  "itemId": "uuid"
}

// 获取收藏列表
GET /api/favorites?type=clothing&page=1&pageSize=20&category=tops&sortBy=created_at

// 批量收藏操作
POST /api/favorites/batch
{
  "action": "add|remove",
  "items": [
    { "itemType": "clothing", "itemId": "uuid" }
  ]
}

// 检查收藏状态
GET /api/favorites/status?itemType=clothing&itemIds=uuid1,uuid2

// 获取收藏统计
GET /api/favorites/stats
```

#### 2. 收藏集合接口

```javascript
// 创建收藏集合
POST /api/collections
{
  "name": "我的夏日搭配",
  "description": "夏季收藏集合",
  "isPublic": false
}

// 添加项目到集合
POST /api/collections/:id/items
{
  "items": [
    { "itemType": "clothing", "itemId": "uuid" }
  ]
}

// 获取集合列表
GET /api/collections?page=1&pageSize=20

// 分享集合
POST /api/collections/:id/share
{
  "shareType": "link|image",
  "expiresIn": 7200
}
```

## 数据模型设计

### 数据库表结构扩展

#### 1. 收藏表 (favorites) - 已存在，需要优化

```sql
-- 优化现有收藏表
CREATE TABLE favorites (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    item_type TEXT NOT NULL, -- 'clothing' 或 'outfit'
    item_id TEXT NOT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE,
    UNIQUE(user_id, item_type, item_id)
);

-- 添加索引优化查询性能
CREATE INDEX idx_favorites_user_type ON favorites(user_id, item_type);
CREATE INDEX idx_favorites_created_at ON favorites(created_at);
```

#### 2. 收藏集合表 (collections)

```sql
CREATE TABLE collections (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    name TEXT NOT NULL,
    description TEXT,
    is_public BOOLEAN DEFAULT FALSE,
    cover_image_url TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_collections_user ON collections(user_id);
CREATE INDEX idx_collections_public ON collections(is_public);
```

#### 3. 收藏集合项目表 (collection_items)

```sql
CREATE TABLE collection_items (
    collection_id TEXT NOT NULL,
    item_type TEXT NOT NULL, -- 'clothing' 或 'outfit'
    item_id TEXT NOT NULL,
    added_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (collection_id, item_type, item_id),
    FOREIGN KEY(collection_id) REFERENCES collections(id) ON DELETE CASCADE
);

CREATE INDEX idx_collection_items_collection ON collection_items(collection_id);
```

#### 4. 分享记录表 (shares)

```sql
CREATE TABLE shares (
    id TEXT PRIMARY KEY,
    user_id TEXT NOT NULL,
    share_type TEXT NOT NULL, -- 'collection', 'clothing', 'outfit'
    target_id TEXT NOT NULL,
    share_token TEXT UNIQUE NOT NULL,
    expires_at DATETIME,
    view_count INTEGER DEFAULT 0,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY(user_id) REFERENCES users(id) ON DELETE CASCADE
);

CREATE INDEX idx_shares_token ON shares(share_token);
CREATE INDEX idx_shares_user ON shares(user_id);
```

## 错误处理设计

### 错误分类和处理策略

#### 1. 网络错误处理
- **连接超时**：显示重试按钮，支持自动重试
- **网络中断**：启用离线模式，缓存操作到本地
- **服务器错误**：显示友好错误信息，提供反馈渠道

#### 2. 数据一致性处理
- **乐观锁机制**：防止并发修改冲突
- **状态回滚**：操作失败时自动回滚本地状态
- **数据同步**：定期同步本地缓存与服务器数据

#### 3. 用户体验优化
- **加载状态**：显示骨架屏和加载动画
- **错误提示**：使用Toast和Modal提供清晰的错误信息
- **操作反馈**：即时的视觉反馈和操作确认

## 测试策略

### 单元测试
- **Store测试**：测试状态管理逻辑和数据流
- **组件测试**：测试组件渲染和用户交互
- **API测试**：测试接口调用和数据处理

### 集成测试
- **端到端测试**：测试完整的收藏流程
- **性能测试**：测试大量数据下的性能表现
- **兼容性测试**：测试不同设备和网络环境

### 测试用例设计
1. **收藏操作测试**：添加、移除、批量操作
2. **数据同步测试**：离线操作、网络恢复同步
3. **分享功能测试**：生成分享链接、访问权限控制
4. **性能测试**：大量收藏数据的加载和渲染性能

## 性能优化设计

### 前端优化
1. **虚拟滚动**：处理大量收藏列表的渲染性能
2. **图片懒加载**：优化图片加载和内存使用
3. **状态缓存**：智能缓存策略减少不必要的API调用
4. **组件复用**：复用收藏卡片组件提高渲染效率

### 后端优化
1. **数据库索引**：优化查询性能
2. **分页查询**：避免一次性加载大量数据
3. **缓存策略**：使用Redis缓存热点数据
4. **批量操作**：支持批量收藏操作减少网络请求

### 网络优化
1. **请求合并**：合并相似的API请求
2. **数据压缩**：压缩API响应数据
3. **CDN加速**：使用CDN加速图片和静态资源
4. **离线支持**：支持离线查看收藏内容

## 安全设计

### 数据安全
1. **权限控制**：用户只能访问自己的收藏数据
2. **数据验证**：严格验证输入数据防止注入攻击
3. **敏感信息保护**：不在前端暴露敏感的用户信息

### 分享安全
1. **访问令牌**：使用临时令牌控制分享访问
2. **权限级别**：支持不同级别的分享权限
3. **过期机制**：分享链接支持自动过期
4. **访问日志**：记录分享内容的访问情况

## 扩展性设计

### 功能扩展
1. **收藏标签**：支持为收藏添加自定义标签
2. **收藏笔记**：支持为收藏添加个人笔记
3. **收藏推荐**：基于收藏历史推荐相似内容
4. **社交功能**：支持关注其他用户的公开收藏

### 技术扩展
1. **多端同步**：支持Web、移动端数据同步
2. **导入导出**：支持收藏数据的导入导出
3. **API开放**：提供开放API供第三方应用使用
4. **插件系统**：支持第三方插件扩展收藏功能