# 设计文档：项目全面优化计划

## 1. 概述

本设计文档旨在将已批准的需求转化为一个全面的技术实现蓝图。我们将通过引入现代化的设计系统、重构核心组件、优化数据模型和增强关键功能流程，系统性地提升“衣柜”应用的整体质量和用户体验。

## 2. 架构

### 2.1. 总体架构
我们将维持现有的 `uni-app` + `Cloudflare Workers` 的前后端分离架构。优化的重点将放在前端的UI/UX层和前后端的数据交互层。

### 2.2. 设计系统 (Design System)
为了实现UI/UX的全面革新（**需求 1**），我们将引入一个统一的设计系统。

*   **颜色**: 定义一套新的品牌色、辅助色、中性色和功能色（成功、警告、错误）。
    *   主色调: `#FDA085` (现有选中色) 可作为品牌色保留，但需搭配更柔和、更高级的灰色系和白色背景。
    *   背景色: `#F7F8FA` (浅灰)，取代当前的 `#F8F8F8`，提供更细腻的质感。
    *   文本色: 主文本 `#333333`，次要文本 `#888888`。
*   **字体**: 统一使用系统默认的无衬线字体，定义清晰的字号层级（如 18px 用于标题，16px 用于正文，14px 用于辅助信息）。
*   **间距与圆角**: 制定标准的间距单位（如 8px 的倍数）和统一的圆角规范（如卡片 12px，按钮 8px），应用于所有组件。
*   **图标**: 评估并可能替换现有图标库，或对现有图标进行风格统一，确保所有图标线性、粗细一致。

### 2.3. 组件化策略
我们将采用原子化设计的思想，重构和新建UI组件。

*   **基础组件 (Atoms)**: 封装如 `StyledButton`, `StyledInput`, `StyledIcon` 等基础元素，它们将严格遵循设计系统规范。
*   **复合组件 (Molecules)**: 组合基础组件形成更复杂的单元，例如 `FilterPanel`, `EnhancedClothingCard`。
*   **模板 (Templates)**: 页面级的布局结构，如 `TwoColumnLayout`, `WaterfallFlowLayout`。

## 3. 组件与接口设计

### 3.1. UI/UX 核心组件重构

*   **EnhancedClothingCard (`ClothingCard.vue` 重构)**
    *   **视觉**: 采用新的设计系统，增加内边距，使用更柔和的阴影。图片加载时使用骨架屏（Shimmer Effect）代替简单的图标。
    *   **交互**: 点击收藏按钮时，提供更细腻的动画反馈（如跳动效果）。
    *   **功能**: 卡片上直接显示更多关键信息，如品牌（如果存在）。

*   **FilterPanel (新建组件)**
    *   **功能**: 一个可从侧边滑出的抽屉或模态框，用于衣柜页面的高级筛选（**需求 3.2**）。
    *   **内容**: 包含按分类、颜色、季节、品牌等多维度的筛选器。支持多选和一键重置。
    *   **接口**: `v-model` 绑定筛选参数对象，`@change` 事件在参数变更时触发。

### 3.2. 核心流程优化 (需求 2)

我们将通过优化页面跳转逻辑和增加上下文操作来改善流程。

*   **从“衣物详情”到“创建穿搭”**:
    *   在 `ClothingDetailPage.vue` 中增加一个“加入穿搭”的浮动操作按钮 (FAB)。
    *   点击后，弹出一个选择器，让用户选择“加入现有穿搭”或“创建新穿搭”。
    *   选择“创建新穿搭”时，直接跳转到 `CreateOutfitPage.vue`，并将当前衣物自动添加进去。

*   **创建穿搭后的反馈流程**:
    ```mermaid
    graph TD
        A[用户在 CreateOutfitPage 点击保存] --> B{调用 outfitStore.createOutfit};
        B -- 成功 --> C[API 返回新穿搭数据];
        C --> D[outfitStore 更新列表];
        D --> E[uni.redirectTo 跳转到 OutfitDetailPage];
        E --> F[页面加载新穿搭的详细信息];
    ```

### 3.3. 图片处理流程 (需求 4)

*   **上传流程**:
    1.  **前端**: 用户选择图片后，使用 `uni.compressImage` 进行初步压缩，限制尺寸和大小。
    2.  **后端 (新建 `image-handler` Worker)**:
        *   接收到图片后，进行二次压缩，生成不同尺寸的缩略图（如 `thumb_200x200`, `detail_800x800`）。
        *   **[可选/高级功能]** 调用第三方API（如 Cloudflare Images, หรือ remove.bg API）进行背景移除。该功能可在设置中开启/关闭。
        *   将处理后的图片URL（原图、缩略图、去底图）存入数据库。
*   **展示**: 客户端根据场景请求不同尺寸的图片，如列表页用缩略图，详情页用大图，从而优化加载性能。

## 4. 数据模型

为了支持新功能，需要对后端的数据库 `schema` 和数据模型进行扩展。

### 4.1. `clothing` 表/模型扩展 (需求 3.1)

在 `yigui-backend/src/models/clothing.js` 和对应的数据库表中增加以下字段：

| 字段名              | 类型         | 描述         | 示例值        |
| ------------------- | ------------ | ------------ | ------------- |
| `material`          | `TEXT`       | 材质         | 'cotton', 'silk' |
| `thickness`         | `TEXT`       | 厚薄程度     | 'thin', 'medium', 'thick' |
| `care_instructions` | `TEXT`       | 保养说明     | '手洗, 不可漂白' |

### 4.2. `outfits` 表/模型扩展 (需求 3.3)

在 `yigui-backend/src/models/outfit.js` 和对应的数据库表中增加 `tags` 字段。考虑到查询效率，我们将使用一个关联表。

*   **新建 `outfit_tags` 表**:
    *   `id`: 主键
    *   `outfit_id`: 穿搭ID (外键)
    *   `tag_name`: 标签名 (TEXT)

## 5. 错误处理

*   **前端**: 在 `utils/errorHandler.js` 中增加一个统一的 `showToast` 函数，用于向用户显示格式统一、体验良好的错误提示，而不是简单的 `console.error`。
*   **后端**: API返回的错误信息应更具体，并提供错误码，便于前端进行针对性处理。例如，图片背景移除失败时，返回 `{ success: false, error: { code: 'IMAGE_PROCESSING_FAILED', message: '...' } }`。

## 6. 测试策略

*   **单元测试**:
    *   为 `clothingStore` 和 `outfitStore` 中新增的 `actions` 和 `getters` 编写单元测试。
    *   为后端新增的数据模型验证逻辑（如 `material`, `thickness` 字段）编写测试。
*   **组件测试**:
    *   为新建的 `FilterPanel` 组件编写测试，验证其交互和事件触发。
    *   为重构后的 `EnhancedClothingCard` 编写快照测试，确保UI一致性。
*   **端到端测试**:
    *   编写一个完整的测试用例，覆盖“上传衣物 -> 增强衣物信息 -> 创建穿搭 -> 为穿搭添加标签 -> 在列表中查看”的完整流程。

### 2.4. 实现高级感UI的核心原则

为了系统性地达成“高级感”，我们将遵循以下设计与实现原则：

*   **克制与留白 (Less is More)**: 界面设计的核心是信息传达，而非元素堆砌。我们将通过增加组件间距、页面边距和卡片内边距，创造呼吸感，引导用户视线聚焦于核心内容。所有页面都将避免内容直接触及屏幕边缘。

*   **严格的样式规范**: 在 `src/uni.scss` 中建立一套完整的视觉语言变量，包括色板、字体层级、间距单位、圆角和阴影。所有组件的样式都必须引用这些全局变量，杜绝硬编码，确保全局视觉一致性。

*   **原子化的基础组件**: 将所有可复用的UI元素（如按钮、卡片、输入框）封装成遵循设计规范的基础组件，存放于 `src/components/base` 目录。这能从根本上保证应用风格的统一，并提升开发效率。

*   **精炼的排版与视觉层级**: 严格控制字号和字重的种类。通过字号大小、字重粗细和文字颜色的深浅来构建清晰的视觉层级，帮助用户快速区分信息的主次。

*   **有意义的微动效**: 动画应服务于交互反馈，而非炫技。
    *   **状态过渡**: 为所有可交互元素添加平滑的过渡动画 (`transition`)。
    *   **加载动画**: 优先使用骨架屏 (Skeleton) 代替传统的加载指示器 (Spinner)，以优化等待体验。
    *   **出现效果**: 新加载的内容采用淡入和轻微位移的动画，使其出现得更自然。