# 实施任务清单：项目全面优化

这份清单将指导编码LLM以测试驱动的方式，逐步完成项目的全面优化。

- [ ] 1. **后端基础：扩展数据模型与API**
    - 目的：为功能增强提供数据层支持。
    - [ ] 1.1. **更新数据库 Schema**
        - 在 `yigui-backend/schema.sql` (或类似的迁移文件) 中，修改 `clothing` 表，添加 `material` (TEXT), `thickness` (TEXT), `care_instructions` (TEXT) 字段。
        - 创建新表 `outfit_tags` (id, outfit_id, tag_name)。
        - _需求: 3.1, 3.3_
    - [ ] 1.2. **扩展后端 Clothing Model**
        - 修改 `yigui-backend/src/models/clothing.js` 中的 `ClothingCreateSchema` 和 `ClothingUpdateSchema`，加入对 `material`, `thickness`, `care_instructions` 的验证。
        - 更新 `createClothing` 和 `updateClothing` 方法以处理这些新字段。
        - _需求: 3.1_
    - [ ] 1.3. **实现后端 Outfit Tagging Model**
        - 在 `yigui-backend/src/models/outfit.js` 中，实现 `addOutfitTags`, `findTagsByOutfitId`, `updateOutfitTags` 等方法。
        - 更新 `createOutfit` 和 `updateOutfit` 方法，使其能够处理 `tags` 数组。
        - _需求: 3.3_
    - [ ] 1.4. **更新 API Endpoints**
        - 修改 `yigui-backend/src/handlers/clothing.js` 和 `yigui-backend/src/handlers/outfits.js`，确保 `POST` 和 `PUT` 请求能够接收和处理新的数据字段（衣物详情和穿搭标签）。
        - 确保 `GET` 请求能返回这些新字段。
        - _需求: 3.1, 3.3_

- [ ] 2. **前端状态管理：同步数据模型**
    - 目的：使前端 Pinia store 能够管理和同步新的数据结构。
    - [ ] 2.1. **更新 Clothing Store**
        - 修改 `src/stores/clothingStore.js`。
        - 在 `addClothing` 和 `updateClothing` action 中，添加对 `material`, `thickness`, `care_instructions` 等新属性的支持。
        - 确保 `fetchClothingDetail` 能够正确处理返回的新数据。
        - _需求: 3.1_
    - [ ] 2.2. **更新 Outfit Store**
        - 修改 `src/stores/outfitStore.js`。
        - 在 `createOutfit` 和 `updateOutfit` action 中，添加对 `tags` 数组的支持。
        - _需求: 3.3_

- [ ] 3. **UI/UX 基础：构建设计系统**
    - 目的：奠定统一、现代化的视觉基础。
    - [ ] 3.1. **创建全局样式变量**
        - 在 `src/uni.scss` 中，定义全局 SCSS 变量，包括新的色板、字体层级、标准间距和圆角值。
        - _需求: 1.1_
    - [ ] 3.2. **创建基础原子组件**
        - 创建 `src/components/base/StyledButton.vue`，该按钮组件的样式（颜色、圆角、内边距）完全由全局样式变量控制。
        - 创建 `src/components/base/StyledInput.vue`，统一样式。
        - _需求: 1.1, 1.4_

- [ ] 4. **核心组件重构与实现**
    - 目的：将设计稿中的关键组件转化为实际代码。
    - [ ] 4.1. **重构 ClothingCard 组件**
        - 修改 `src/pages/main/wardrobe/components/ClothingCard.vue`。
        - 应用新的设计系统样式。实现骨架屏加载效果，替换现有占位符。
        - 添加更平滑的交互动画。
        - _需求: 1.1, 1.2, 1.4, 4.3, 4.4_
    - [ ] 4.2. **创建 FilterPanel 组件**
        - 创建 `src/components/FilterPanel.vue`。
        - 实现一个可从侧边滑出的抽屉，包含按分类、颜色、季节、品牌等多维度筛选器。
        - 编写组件测试，验证其交互和事件触发。
        - _需求: 3.2_
    - [ ] 4.3. **集成 FilterPanel 到衣柜页面**
        - 修改 `src/pages/wardrobe/WardrobePage.vue`，添加一个筛选触发按钮，并集成 `FilterPanel` 组件。
        - 将筛选参数与 `clothingStore` 的 `fetchClothingList` action 关联起来。
        - _需求: 3.2_

- [ ] 5. **功能流程增强**
    - 目的：打通核心功能之间的壁垒，优化用户操作路径。
    - [ ] 5.1. **实现“添加衣物”页面的完整字段**
        - 修改 `src/pages/main/addItem/AddItemPage.vue`，添加用于输入品牌、购买日期、价格、材质、厚薄、保养说明的表单控件。
        - _需求: 3.1_
    - [ ] 5.2. **实现“创建穿搭”页面的标签功能**
        - 修改 `src/pages/main/createOutfit/CreateOutfitPage.vue`，添加一个标签输入区域，允许用户为穿搭添加自定义标签。
        - _需求: 3.3_
    - [ ] 5.3. **实现从衣物详情到创建穿搭的流畅跳转**
        - 在 `src/pages/main/clothingDetail/ClothingDetailPage.vue` 中添加“加入穿搭”按钮。
        - 实现点击后跳转到 `CreateOutfitPage.vue` 并将当前衣物自动选中的逻辑。
        - _需求: 2.1_

- [ ] 6. **图片处理流程优化**
    - 目的：提升图片展示质量和加载性能。
    - [ ] 6.1. **实现前端图片压缩**
        - 在 `ImageUploader.vue` 或相关上传逻辑中，调用 `uni.compressImage` 对用户选择的图片进行预处理。
        - _需求: 4.1_
    - [ ] 6.2. **[高级] 实现后端图片背景移除功能**
        - 创建一个新的 Cloudflare Worker `image-handler`。
        - 编写逻辑，在接收到图片后，调用第三方服务（如 `remove.bg` API）进行处理。
        - 将处理后的图片URL保存到 `clothing_images` 表中一个新字段 `processed_image_url`。
        - _需求: 4.2_