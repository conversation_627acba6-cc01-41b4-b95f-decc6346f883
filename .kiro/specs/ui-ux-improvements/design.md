# Design Document

## Overview

本设计文档旨在解决当前应用中的关键UI/UX问题，通过系统性的改进提升用户体验。主要包括：收藏功能的完善、图片展示优化、数据实时更新以及创建搭配时的视觉反馈改进。

## Architecture

### 整体架构设计

```mermaid
graph TB
    A[用户界面层] --> B[组件层]
    B --> C[状态管理层]
    C --> D[API服务层]
    D --> E[后端服务]
    
    B --> F[收藏组件]
    B --> G[图片优化组件]
    B --> H[实时更新组件]
    B --> I[搭配选择组件]
    
    C --> J[收藏状态管理]
    C --> K[UI状态管理]
    C --> L[数据同步管理]
```

### 数据流设计

```mermaid
sequenceDiagram
    participant U as 用户
    participant UI as 界面组件
    participant S as Store状态
    participant API as API服务
    participant BE as 后端
    
    U->>UI: 操作触发
    UI->>S: 更新本地状态
    UI->>U: 立即反馈
    S->>API: 发送请求
    API->>BE: 后端处理
    BE->>API: 返回结果
    API->>S: 更新状态
    S->>UI: 同步界面
```

## Components and Interfaces

### 1. 收藏功能组件

#### FavoriteButton 组件增强
- **位置**: `src/components/common/FavoriteButton.vue`
- **功能**: 已存在，需要集成到更多页面
- **接口**:
  ```typescript
  interface FavoriteButtonProps {
    itemId: string;
    itemType: 'clothing' | 'outfit';
    size: 'small' | 'medium' | 'large';
    variant: 'default' | 'minimal' | 'filled';
  }
  ```

#### 收藏页面组件
- **位置**: `src/pages/favorites/FavoritesPage.vue` (新建)
- **功能**: 展示用户收藏的衣物和搭配
- **接口**:
  ```typescript
  interface FavoritesPageData {
    favorites: FavoriteItem[];
    filters: FilterOptions;
    loading: boolean;
  }
  ```

### 2. 图片优化组件

#### OptimizedImageGrid 组件
- **位置**: `src/components/common/OptimizedImageGrid.vue` (新建)
- **功能**: 响应式图片网格布局
- **特性**:
  - 自适应列数 (移动端2-3列，平板4列)
  - 统一宽高比 (1:1.2)
  - 懒加载支持
  - 加载占位符

#### CompactCard 组件
- **位置**: `src/components/common/CompactCard.vue` (新建)
- **功能**: 紧凑型卡片布局
- **尺寸规范**:
  - 卡片高度: 280rpx (原来400rpx+)
  - 图片高度: 180rpx (原来250rpx+)
  - 信息区域: 100rpx

### 3. 实时更新组件

#### DataSyncManager 组件
- **位置**: `src/components/common/DataSyncManager.vue` (新建)
- **功能**: 管理数据同步和更新
- **特性**:
  - 自动刷新机制
  - 错误重试
  - 离线缓存

### 4. 搭配选择增强组件

#### ClothingSelector 组件增强
- **位置**: `src/pages/main/createOutfit/components/ClothingSelector.vue`
- **改进**:
  - 添加图片预览
  - 网格布局展示
  - 选中状态视觉反馈

#### SelectedItemsPreview 组件
- **位置**: `src/components/outfit/SelectedItemsPreview.vue` (新建)
- **功能**: 显示已选择衣物的预览
- **布局**: 横向滚动卡片列表

## Data Models

### 收藏数据模型
```typescript
interface FavoriteItem {
  id: string;
  itemType: 'clothing' | 'outfit';
  itemId: string;
  favoritedAt: string;
  details: ClothingItem | OutfitItem;
}

interface FavoriteFilters {
  type?: 'clothing' | 'outfit';
  category?: string;
  search?: string;
  sortBy: 'created_at' | 'name' | 'relevance';
}
```

### UI状态模型
```typescript
interface UIState {
  imageGridColumns: number;
  cardSize: 'compact' | 'normal' | 'large';
  loadingStates: Map<string, boolean>;
  errorStates: Map<string, string>;
}
```

### 搭配选择状态模型
```typescript
interface OutfitCreationState {
  selectedItems: ClothingItem[];
  previewMode: 'grid' | 'list';
  showImages: boolean;
  validationErrors: string[];
}
```

## Error Handling

### 错误分类和处理策略

#### 1. 网络错误
- **策略**: 自动重试 + 离线缓存
- **用户反馈**: Toast提示 + 重试按钮
- **实现**: 指数退避重试机制

#### 2. 数据加载错误
- **策略**: 显示占位符 + 手动刷新
- **用户反馈**: 错误状态页面
- **实现**: 错误边界组件

#### 3. 操作失败错误
- **策略**: 回滚状态 + 错误提示
- **用户反馈**: 具体错误信息
- **实现**: 乐观更新 + 失败回滚

### 错误处理组件
```typescript
interface ErrorBoundary {
  fallback: Component;
  onError: (error: Error) => void;
  retry: () => void;
}
```

## Testing Strategy

### 1. 单元测试
- **组件测试**: 每个新组件的功能测试
- **工具函数测试**: 图片处理、数据同步逻辑
- **状态管理测试**: Store的状态变更逻辑

### 2. 集成测试
- **页面流程测试**: 收藏操作完整流程
- **数据同步测试**: 添加衣物后的页面更新
- **错误处理测试**: 网络异常情况处理

### 3. 视觉回归测试
- **布局测试**: 不同屏幕尺寸下的布局
- **图片加载测试**: 各种网络条件下的图片显示
- **交互测试**: 用户操作的视觉反馈

### 4. 性能测试
- **图片加载性能**: 大量图片的加载时间
- **滚动性能**: 长列表的滚动流畅度
- **内存使用**: 图片缓存的内存占用

## Implementation Details

### 1. 图片优化实现

#### 响应式网格布局
```scss
.image-grid {
  display: grid;
  gap: 20rpx;
  
  // 移动端
  grid-template-columns: repeat(2, 1fr);
  
  // 平板
  @media (min-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }
  
  // 大屏
  @media (min-width: 1024px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

.compact-card {
  height: 280rpx;
  border-radius: 12rpx;
  overflow: hidden;
  
  .image-container {
    height: 180rpx;
    position: relative;
    
    .lazy-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
    
    .favorite-button {
      position: absolute;
      top: 8rpx;
      right: 8rpx;
    }
  }
  
  .info-section {
    height: 100rpx;
    padding: 12rpx;
    
    .title {
      font-size: 26rpx;
      font-weight: 500;
      line-height: 1.2;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    
    .subtitle {
      font-size: 22rpx;
      color: #666;
      margin-top: 4rpx;
    }
  }
}
```

#### 图片懒加载实现
```javascript
// 使用Intersection Observer API
const useImageLazyLoad = () => {
  const imageRef = ref(null);
  const isLoaded = ref(false);
  const isError = ref(false);
  
  onMounted(() => {
    const observer = new IntersectionObserver((entries) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          loadImage();
          observer.unobserve(entry.target);
        }
      });
    });
    
    if (imageRef.value) {
      observer.observe(imageRef.value);
    }
  });
  
  const loadImage = () => {
    // 图片加载逻辑
  };
  
  return { imageRef, isLoaded, isError };
};
```

### 2. 数据同步实现

#### 实时更新机制
```javascript
// Store中的数据同步逻辑
const useDataSync = () => {
  const syncData = async (type, data) => {
    // 1. 立即更新本地状态
    updateLocalState(type, data);
    
    // 2. 通知相关页面更新
    notifyPageUpdate(type);
    
    // 3. 后台同步到服务器
    try {
      await syncToServer(type, data);
    } catch (error) {
      // 同步失败处理
      handleSyncError(error);
    }
  };
  
  return { syncData };
};
```

### 3. 搭配选择界面实现

#### 衣物选择器增强
```vue
<template>
  <view class="clothing-selector">
    <view class="selected-preview" v-if="selectedItems.length > 0">
      <scroll-view scroll-x class="selected-scroll">
        <view class="selected-item" 
              v-for="item in selectedItems" 
              :key="item.id"
              @click="removeItem(item.id)">
          <image :src="item.imageUrls[0]" class="selected-image" />
          <text class="selected-name">{{ item.name }}</text>
          <view class="remove-btn">×</view>
        </view>
      </scroll-view>
    </view>
    
    <view class="available-items">
      <view class="item-grid">
        <view class="item-card" 
              v-for="item in availableItems" 
              :key="item.id"
              :class="{ selected: isSelected(item.id) }"
              @click="toggleItem(item)">
          <image :src="item.imageUrls[0]" class="item-image" />
          <view class="item-info">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-category">{{ item.categoryDisplay }}</text>
          </view>
          <view class="selection-indicator" v-if="isSelected(item.id)">
            <uni-icons type="checkmarkempty" color="#fff" size="16" />
          </view>
        </view>
      </view>
    </view>
  </view>
</template>
```

## Performance Considerations

### 1. 图片加载优化
- **懒加载**: 只加载可视区域的图片
- **图片压缩**: 根据显示尺寸提供合适分辨率
- **缓存策略**: 本地缓存常用图片
- **预加载**: 预加载下一屏的图片

### 2. 列表渲染优化
- **虚拟滚动**: 长列表使用虚拟滚动
- **分页加载**: 避免一次性加载大量数据
- **防抖搜索**: 搜索输入防抖处理

### 3. 状态管理优化
- **局部更新**: 只更新变化的部分
- **状态缓存**: 缓存计算结果
- **异步操作**: 非阻塞的后台同步

## Accessibility

### 1. 键盘导航
- 所有交互元素支持键盘访问
- 合理的Tab顺序
- 快捷键支持

### 2. 屏幕阅读器支持
- 语义化HTML结构
- 适当的ARIA标签
- 图片alt文本

### 3. 视觉辅助
- 高对比度模式支持
- 字体大小调节
- 色盲友好的颜色方案

## Security Considerations

### 1. 图片安全
- 图片URL验证
- 防止XSS攻击
- 图片内容过滤

### 2. 数据安全
- 用户数据加密传输
- 敏感信息本地加密存储
- API访问权限控制

### 3. 操作安全
- 防止重复提交
- 操作权限验证
- 恶意操作检测