<!DOCTYPE html>
<html>
<head>
    <title>API Test</title>
</head>
<body>
    <h1>API Test Page</h1>
    <button onclick="testCollectionAPI()">Test Collection API</button>
    <div id="result"></div>

    <script>
        // Mock uni object for testing
        window.uni = {
            request: function(config) {
                console.log('Mock uni.request called with:', config);
                
                // Simulate a 404 response for collections endpoint
                if (config.url.includes('/api/collections')) {
                    return Promise.resolve({
                        statusCode: 404,
                        data: 'Not Found'
                    });
                }
                
                return Promise.resolve({
                    statusCode: 200,
                    data: { success: true }
                });
            },
            getStorageSync: function(key) {
                return null;
            }
        };

        // Mock process.env for development detection
        window.process = {
            env: {
                NODE_ENV: 'development'
            }
        };

        async function testCollectionAPI() {
            try {
                // Import the http module
                const { http } = await import('./api/http.js');
                
                console.log('Testing collection API...');
                const result = await http.get('/collections', { page: 1, pageSize: 10 });
                
                document.getElementById('result').innerHTML = 
                    '<h2>Success!</h2><pre>' + JSON.stringify(result, null, 2) + '</pre>';
                    
                console.log('API test result:', result);
            } catch (error) {
                console.error('API test failed:', error);
                document.getElementById('result').innerHTML = 
                    '<h2>Error!</h2><pre>' + error.message + '</pre>';
            }
        }
    </script>
</body>
</html>
