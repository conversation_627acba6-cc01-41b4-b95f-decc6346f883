<template>
  <view class="profile-container">
    <view class="profile-card">
      <!-- Avatar -->
      <view class="avatar-section">
        <image
          class="avatar-image"
          src="/static/logo.png"
          mode="aspectFill"
        ></image>
      </view>

      <!-- User Info -->
      <uni-list class="info-section" :border="true">
        <uni-list-item title="用户名" :show-arrow="false">
          <template #footer>
            <input
              v-if="isEditing"
              v-model="userData.username"
              type="text"
              placeholder="输入用户名"
              class="editable-input"
            />
            <text v-else>{{ userData.username }}</text>
          </template>
        </uni-list-item>
        <uni-list-item title="邮箱" :show-arrow="false">
          <template #footer>
            <input
              v-if="isEditing"
              v-model="userData.email"
              type="email"
              placeholder="输入邮箱"
              class="editable-input"
            />
            <text v-else>{{ userData.email }}</text>
          </template>
        </uni-list-item>
        <!-- Add more fields as needed, e.g. -->
        <!-- <uni-list-item title="会员等级" rightText="黄金会员" /> -->
      </uni-list>

      <!-- Action Buttons -->
      <view class="actions-section">
        <button class="action-button edit-button" @click="handleEditProfile">
          {{ isEditing ? '保存资料' : '编辑资料' }}
        </button>
        <button class="action-button logout-button" @click="handleLogout">
          退出登录
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useUserStore } from '@/stores/userStore';
// uni-list and uni-list-item are auto-imported via easycom

const userStore = useUserStore();
const userData = reactive({
  username: '',
  email: '',
  avatarUrl: ''
});

const isEditing = ref(false);
const isLoading = ref(false);

const handleEditProfile = () => {
  if (isEditing.value) {
    uni.showLoading({ title: '正在保存...' });
    userStore
      .updateUserProfile(userData)
      .then(() => {
        uni.hideLoading();
        isEditing.value = false;
        uni.showToast({
          title: '资料已保存',
          icon: 'success',
          duration: 1500
        });
      })
      .catch(error => {
        uni.showToast({
          title: error.message || '保存失败',
          icon: 'none'
        });
      });
  } else {
    isEditing.value = true;
  }
};

const handleLogout = () => {
  isLoading.value = true;
  uni.showLoading({ title: '正在退出...' });
  userStore
    .logout()
    .then(() => {
      isLoading.value = false;
      uni.hideLoading();
      uni.showToast({
        title: '已退出登录',
        icon: 'success',
        duration: 1500
      });
      // userStore.logout() 已经处理了导航逻辑
    })
    .catch(error => {
      uni.showToast({
        title: error.message || '退出失败',
        icon: 'none'
      });
      isLoading.value = false;
    });
};

onLoad(() => {
  userStore
    .getUserProfile()
    .then(profile => {
      Object.assign(userData, profile);
    })
    .catch(error => {
      uni.showToast({
        title: error.message || '获取用户信息失败',
        icon: 'none'
      });
    });
});
</script>

<style scoped lang="scss">
.profile-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40rpx;
  box-sizing: border-box;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.profile-card {
  width: 100%;
  max-width: 700rpx;
  background-color: #ffffff;
  border-radius: 20rpx;
  box-shadow: 0 8rpx 16rpx rgba(0, 0, 0, 0.05);
  padding: 40rpx;
  box-sizing: border-box;
}

.avatar-section {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40rpx;
}

.avatar-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 50%;
  border: 2px solid #eee;
}

.info-section {
  // Styles for uni-list wrapper
  margin-bottom: 50rpx;
  // uni-list has its own border styling if :border="true"
  // Custom styling for uni-list-item can be done using ::v-deep if needed
  // For example, to change the title color or rightText color:
  // ::v-deep .uni-list-item__content-title { color: #yourcolor; }
  // ::v-deep .uni-list-item__extra-text { color: #yourcolor; }
}

/* Removed old .info-item, .info-label, .info-value styles as they are not used with uni-list-item's props approach
  .info-item {
    display: flex;
    justify-content: space-between;
    padding: 20rpx 0;
    border-bottom: 1px solid #f0f0f0;
    &:last-child {
      border-bottom: none;
    }
  }
  
  .info-label {
    font-size: 30rpx;
    color: #666;
  }
  
  .info-value {
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
  }
  */

.actions-section {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
  margin-top: 30rpx; // Added some margin-top for spacing from the list
}

.action-button {
  width: 100%;
  border-radius: 10rpx;
  font-size: 30rpx;
  padding: 20rpx 0; // Keep this for button height
  line-height: normal; // Ensure text vertical centering if padding is affecting it
  text-align: center;
}

.edit-button {
  background-color: #fda085; // Theme color
  color: white;
  border: none;
}

.logout-button {
  background-color: #e6e6e6;
  color: #333;
  border: 1px solid #ccc; // Keep border for visual distinction
}

.editable-input {
  text-align: right; /* Align text to the right like original rightText */
  font-size: inherit; /* Inherit font size from parent */
  color: inherit; /* Inherit color from parent */
  background-color: transparent; /* Make background transparent */
  border: none; /* Remove border */
  padding: 0; /* Remove padding */
  width: 100%; /* Take full width of the slot */
}
</style>
