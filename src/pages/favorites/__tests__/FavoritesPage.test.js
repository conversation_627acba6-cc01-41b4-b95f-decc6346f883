import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { setActivePinia, createPinia } from 'pinia';
import FavoritesPage from '../FavoritesPage.vue';
import { useCollectionStore } from '@/stores/collectionStore';

// Mock uni-app components
vi.mock('uni-icons', () => ({
  default: {
    name: 'uni-icons',
    template: '<div class="uni-icons-mock" :data-type="type"></div>',
    props: ['type', 'size', 'color']
  }
}));

vi.mock('uni-load-more', () => ({
  default: {
    name: 'uni-load-more',
    template: '<div class="uni-load-more-mock" :data-status="status"></div>',
    props: ['status']
  }
}));

// Mock OptimizedImageGrid component
vi.mock('@/components/common/OptimizedImageGrid.vue', () => ({
  default: {
    name: 'OptimizedImageGrid',
    template: '<div class="optimized-image-grid-mock"><slot /></div>',
    props: ['items', 'itemType', 'showFavoriteButton', 'emptyText'],
    emits: ['item-click']
  }
}));

// Mock uni API
global.uni = {
  navigateTo: vi.fn(),
  showModal: vi.fn(),
  showToast: vi.fn(),
  setNavigationBarTitle: vi.fn()
};

describe('FavoritesPage', () => {
  let wrapper;
  let store;

  const mockClothingItems = [
    {
      id: '1',
      name: '白色T恤',
      categoryDisplay: '上装',
      imageUrls: ['https://example.com/image1.jpg'],
      favoritedAt: '2024-01-01T00:00:00Z'
    },
    {
      id: '2',
      name: '牛仔裤',
      categoryDisplay: '下装',
      imageUrls: ['https://example.com/image2.jpg'],
      favoritedAt: '2024-01-02T00:00:00Z'
    }
  ];

  const mockOutfitItems = [
    {
      id: '3',
      name: '休闲搭配',
      occasion: '日常',
      imageUrl: 'https://example.com/outfit1.jpg',
      favoritedAt: '2024-01-03T00:00:00Z'
    }
  ];

  beforeEach(() => {
    setActivePinia(createPinia());
    store = useCollectionStore();
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('渲染和初始状态', () => {
    it('应该正确渲染页面结构', () => {
      wrapper = mount(FavoritesPage);

      expect(wrapper.find('.favorites-container').exists()).toBe(true);
      expect(wrapper.find('.page-header').exists()).toBe(true);
      expect(wrapper.find('.page-title').text()).toBe('我的收藏');
      expect(wrapper.find('.category-tabs').exists()).toBe(true);
    });

    it('应该显示正确的分类标签', () => {
      store.favoriteClothings = mockClothingItems;
      store.favoriteOutfits = mockOutfitItems;

      wrapper = mount(FavoritesPage);

      const tabs = wrapper.findAll('.tab-item');
      expect(tabs).toHaveLength(3);
      
      expect(tabs[0].find('.tab-text').text()).toBe('全部');
      expect(tabs[1].find('.tab-text').text()).toBe('衣物');
      expect(tabs[2].find('.tab-text').text()).toBe('搭配');
    });

    it('应该显示正确的数量统计', async () => {
      store.favoriteClothings = mockClothingItems;
      store.favoriteOutfits = mockOutfitItems;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      const tabs = wrapper.findAll('.tab-item');
      expect(tabs[0].find('.tab-count').text()).toBe('(3)'); // 全部
      expect(tabs[1].find('.tab-count').text()).toBe('(2)'); // 衣物
      expect(tabs[2].find('.tab-count').text()).toBe('(1)'); // 搭配
    });

    it('应该设置正确的导航栏标题', () => {
      wrapper = mount(FavoritesPage);
      expect(uni.setNavigationBarTitle).toHaveBeenCalledWith({
        title: '我的收藏'
      });
    });
  });

  describe('加载状态', () => {
    it('应该在加载时显示加载指示器', async () => {
      store.isLoading = true;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.loading-container').exists()).toBe(true);
      expect(wrapper.find('.uni-load-more-mock').exists()).toBe(true);
    });

    it('应该在加载完成后隐藏加载指示器', async () => {
      store.isLoading = false;
      store.favoriteClothings = mockClothingItems;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.loading-container').exists()).toBe(false);
    });
  });

  describe('空状态', () => {
    it('应该在没有收藏内容时显示空状态', async () => {
      store.favoriteClothings = [];
      store.favoriteOutfits = [];
      store.isLoading = false;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.empty-state').exists()).toBe(true);
      expect(wrapper.find('.empty-title').text()).toBe('还没有收藏任何内容');
      expect(wrapper.find('.empty-desc').text()).toBe('快去收藏你喜欢的衣物和搭配吧');
    });

    it('应该在点击"去逛逛"按钮时跳转到衣橱页面', async () => {
      store.favoriteClothings = [];
      store.favoriteOutfits = [];
      store.isLoading = false;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      await wrapper.find('.explore-button').trigger('click');

      expect(uni.navigateTo).toHaveBeenCalledWith({
        url: '/pages/wardrobe/WardrobePage'
      });
    });
  });

  describe('标签切换', () => {
    beforeEach(() => {
      store.favoriteClothings = mockClothingItems;
      store.favoriteOutfits = mockOutfitItems;
      store.isLoading = false;
    });

    it('应该正确切换到衣物标签', async () => {
      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      const clothingTab = wrapper.findAll('.tab-item')[1];
      await clothingTab.trigger('click');

      expect(wrapper.vm.activeTab).toBe('clothing');
      expect(clothingTab.classes()).toContain('active');
    });

    it('应该正确切换到搭配标签', async () => {
      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      const outfitTab = wrapper.findAll('.tab-item')[2];
      await outfitTab.trigger('click');

      expect(wrapper.vm.activeTab).toBe('outfit');
      expect(outfitTab.classes()).toContain('active');
    });

    it('应该在切换标签时更新显示的内容', async () => {
      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      // 切换到衣物标签
      const clothingTab = wrapper.findAll('.tab-item')[1];
      await clothingTab.trigger('click');
      await wrapper.vm.$nextTick();

      expect(wrapper.vm.favoriteItems).toEqual(mockClothingItems);

      // 切换到搭配标签
      const outfitTab = wrapper.findAll('.tab-item')[2];
      await outfitTab.trigger('click');
      await wrapper.vm.$nextTick();

      expect(wrapper.vm.favoriteItems).toEqual(mockOutfitItems);
    });
  });

  describe('筛选功能', () => {
    it('应该在点击筛选按钮时显示筛选模态框', async () => {
      wrapper = mount(FavoritesPage);

      await wrapper.find('.filter-button').trigger('click');

      expect(wrapper.vm.showFilter).toBe(true);
    });

    it('应该根据搜索关键词筛选内容', async () => {
      store.favoriteClothings = mockClothingItems;
      store.isLoading = false;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      // 设置搜索关键词
      wrapper.vm.searchKeyword = '白色';
      await wrapper.vm.$nextTick();

      const filteredItems = wrapper.vm.favoriteItems;
      expect(filteredItems).toHaveLength(1);
      expect(filteredItems[0].name).toBe('白色T恤');
    });

    it('应该根据分类筛选内容', async () => {
      store.favoriteClothings = mockClothingItems;
      store.isLoading = false;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      // 设置分类筛选
      wrapper.vm.selectedCategory = '上装';
      await wrapper.vm.$nextTick();

      const filteredItems = wrapper.vm.favoriteItems;
      expect(filteredItems).toHaveLength(1);
      expect(filteredItems[0].categoryDisplay).toBe('上装');
    });

    it('应该根据排序方式排序内容', async () => {
      store.favoriteClothings = mockClothingItems;
      store.isLoading = false;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      // 按名称排序
      wrapper.vm.sortBy = 'name';
      await wrapper.vm.$nextTick();

      const sortedItems = wrapper.vm.favoriteItems;
      expect(sortedItems[0].name).toBe('白色T恤');
      expect(sortedItems[1].name).toBe('牛仔裤');
    });
  });

  describe('项目交互', () => {
    beforeEach(() => {
      store.favoriteClothings = mockClothingItems;
      store.isLoading = false;
    });

    it('应该在点击项目时跳转到详情页面', async () => {
      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      const grid = wrapper.findComponent({ name: 'OptimizedImageGrid' });
      await grid.vm.$emit('item-click', { item: mockClothingItems[0], index: 0 });

      expect(uni.navigateTo).toHaveBeenCalledWith({
        url: `/pages/main/clothingDetail/ClothingDetailPage?id=${mockClothingItems[0].id}`
      });
    });

    it('应该正确处理搭配项目点击', async () => {
      store.favoriteOutfits = mockOutfitItems;
      store.isLoading = false;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      // 切换到搭配标签
      wrapper.vm.activeTab = 'outfit';
      await wrapper.vm.$nextTick();

      const grid = wrapper.findComponent({ name: 'OptimizedImageGrid' });
      await grid.vm.$emit('item-click', { item: mockOutfitItems[0], index: 0 });

      expect(uni.navigateTo).toHaveBeenCalledWith({
        url: `/pages/outfits/OutfitDetailPage?id=${mockOutfitItems[0].id}`
      });
    });
  });

  describe('数据刷新', () => {
    it('应该在页面显示时刷新数据', async () => {
      store.loadFavoriteClothings = vi.fn();
      store.loadFavoriteOutfits = vi.fn();

      wrapper = mount(FavoritesPage);

      // 模拟页面显示
      wrapper.vm.onShow();

      expect(store.loadFavoriteClothings).toHaveBeenCalled();
      expect(store.loadFavoriteOutfits).toHaveBeenCalled();
    });

    it('应该在下拉刷新时重新加载数据', async () => {
      store.loadFavoriteClothings = vi.fn();
      store.loadFavoriteOutfits = vi.fn();

      wrapper = mount(FavoritesPage);

      await wrapper.vm.onPullDownRefresh();

      expect(store.loadFavoriteClothings).toHaveBeenCalled();
      expect(store.loadFavoriteOutfits).toHaveBeenCalled();
    });
  });

  describe('错误处理', () => {
    it('应该处理数据加载错误', async () => {
      const error = new Error('加载失败');
      store.loadFavoriteClothings = vi.fn().mockRejectedValue(error);
      store.loadFavoriteOutfits = vi.fn().mockRejectedValue(error);

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      expect(uni.showToast).toHaveBeenCalledWith({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });

    it('应该在网络错误时显示重试选项', async () => {
      store.isLoading = false;
      store.favoriteClothings = [];
      store.favoriteOutfits = [];
      store.error = '网络连接失败';

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.error-state').exists()).toBe(true);
      expect(wrapper.find('.retry-button').exists()).toBe(true);
    });
  });

  describe('计算属性', () => {
    beforeEach(() => {
      store.favoriteClothings = mockClothingItems;
      store.favoriteOutfits = mockOutfitItems;
    });

    it('应该正确计算分类标签数据', async () => {
      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      const tabs = wrapper.vm.categoryTabs;
      expect(tabs).toHaveLength(3);
      expect(tabs[0]).toMatchObject({
        key: 'all',
        label: '全部',
        count: 3
      });
      expect(tabs[1]).toMatchObject({
        key: 'clothing',
        label: '衣物',
        count: 2
      });
      expect(tabs[2]).toMatchObject({
        key: 'outfit',
        label: '搭配',
        count: 1
      });
    });

    it('应该根据当前标签返回正确的收藏项目', async () => {
      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      // 全部标签
      wrapper.vm.activeTab = 'all';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.favoriteItems).toHaveLength(3);

      // 衣物标签
      wrapper.vm.activeTab = 'clothing';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.favoriteItems).toEqual(mockClothingItems);

      // 搭配标签
      wrapper.vm.activeTab = 'outfit';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.favoriteItems).toEqual(mockOutfitItems);
    });
  });

  describe('生命周期', () => {
    it('应该在挂载时加载数据', () => {
      store.loadFavoriteClothings = vi.fn();
      store.loadFavoriteOutfits = vi.fn();

      wrapper = mount(FavoritesPage);

      expect(store.loadFavoriteClothings).toHaveBeenCalled();
      expect(store.loadFavoriteOutfits).toHaveBeenCalled();
    });

    it('应该在卸载时清理资源', () => {
      wrapper = mount(FavoritesPage);
      const cleanupSpy = vi.spyOn(wrapper.vm, 'cleanup');

      wrapper.unmount();

      expect(cleanupSpy).toHaveBeenCalled();
    });
  });

  describe('性能优化', () => {
    it('应该使用虚拟滚动处理大量数据', async () => {
      // 创建大量测试数据
      const largeDataSet = Array.from({ length: 1000 }, (_, index) => ({
        id: `item-${index}`,
        name: `测试项目 ${index}`,
        categoryDisplay: '测试分类',
        imageUrls: [`https://example.com/image${index}.jpg`],
        favoritedAt: new Date().toISOString()
      }));

      store.favoriteClothings = largeDataSet;
      store.isLoading = false;

      wrapper = mount(FavoritesPage);
      await wrapper.vm.$nextTick();

      const grid = wrapper.findComponent({ name: 'OptimizedImageGrid' });
      expect(grid.exists()).toBe(true);
      expect(grid.props('items')).toHaveLength(1000);
    });

    it('应该防抖搜索输入', async () => {
      wrapper = mount(FavoritesPage);

      const searchInput = wrapper.find('.search-input');
      if (searchInput.exists()) {
        // 快速输入多次
        await searchInput.setValue('a');
        await searchInput.setValue('ab');
        await searchInput.setValue('abc');

        // 验证防抖效果
        expect(wrapper.vm.searchKeyword).toBe('abc');
      }
    });
  });
});