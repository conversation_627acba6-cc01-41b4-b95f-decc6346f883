<template>
  <view class="outfit-calendar-container">
    <view class="header">
      <text class="title">穿搭日历</text>
    </view>
    <view class="calendar-main-content">
      <uni-calendar
        :insert="true"
        :show-month="true"
        :selected="calendarExtraInfo"
        :date="currentDate"
        @change="handleDateSelect"
        @month-switch="handleMonthSwitched"
      />
      <view v-if="selectedDateInfo" class="selected-outfit-details">
        <text class="selected-date-text">日期: {{ selectedDateInfo.fullDate }}</text>
        <view v-if="assignedOutfitDetails" class="outfit-card">
          <text class="outfit-name-title">当日穿搭:</text>
          <image
            v-if="
              assignedOutfitDetails.clothingItems &&
                assignedOutfitDetails.clothingItems.length > 0 &&
                assignedOutfitDetails.clothingItems[0].imageUrl
            "
            :src="assignedOutfitDetails.clothingItems[0].imageUrl"
            class="outfit-image-preview"
            mode="aspectFit"
          />
          <text class="outfit-name">{{ assignedOutfitDetails.name }}</text>
          <button
            class="action-button unassign-button"
            @click="handleUnassignOutfit"
          >
            {{ BUTTON_TEXTS.UNASSIGN_OUTFIT || '取消安排' }}
          </button>
          <button
            class="action-button change-button"
            @click="openOutfitSelector"
          >
            {{ BUTTON_TEXTS.CHANGE_OUTFIT || '更换穿搭' }}
          </button>
        </view>
        <view v-else class="no-outfit-assigned">
          <text>{{
            MISC_TEXT.NO_OUTFIT_ASSIGNED_TODAY || '当日无穿搭安排。'
          }}</text>
          <button
            class="action-button assign-button"
            @click="openOutfitSelector"
          >
            {{ BUTTON_TEXTS.ASSIGN_OUTFIT || '安排穿搭' }}
          </button>
        </view>
      </view>
    </view>

    <!-- Outfit Selector Section -->
    <view v-if="showOutfitSelector" class="outfit-selector-overlay">
      <view class="outfit-selector-modal" @click.stop>
        <text class="selector-title">{{
          MISC_TEXT.SELECT_OUTFIT_TITLE || '选择一套穿搭'
        }}</text>
        <scroll-view scroll-y class="outfits-list">
          <view
            v-if="availableOutfits.length === 0"
            class="no-outfits-available"
          >
            <text>{{
              MISC_TEXT.NO_OUTFITS_CREATED_YET || '您还没有创建任何穿搭。'
            }}</text>
          </view>
          <view
            v-for="outfit in availableOutfits"
            :key="outfit.id"
            class="outfit-list-item"
            hover-class="outfit-list-item-hover"
            @tap="handleAssignOutfit(outfit.id)"
          >
            <image
              v-if="
                outfit.clothingItems &&
                  outfit.clothingItems.length > 0 &&
                  outfit.clothingItems[0].imageUrl
              "
              :src="outfit.clothingItems[0].imageUrl"
              class="outfit-item-image-preview"
              mode="aspectFill"
            />
            <text class="outfit-item-name">{{ outfit.name }}</text>
          </view>
        </scroll-view>
        <button
          class="action-button cancel-button"
          @tap="showOutfitSelector = false"
        >
          {{ BUTTON_TEXTS.CANCEL || '取消' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import uniCalendar from '@dcloudio/uni-ui/lib/uni-calendar/uni-calendar.vue';
import { useCalendarStore } from '@/stores/calendarStore.js';
import { useOutfitStore } from '@/stores/outfitStore.js'; // To fetch outfit details for display
import { BUTTON_TEXTS, MISC_TEXT } from '@/constants/uiTexts.js';

const calendarStore = useCalendarStore();
const outfitStore = useOutfitStore(); // If you plan to show outfit names directly on calendar

const currentDate = ref(new Date().toISOString().slice(0, 10)); // YYYY-MM-DD
const selectedDateInfo = ref(null); // To store info about the selected date
const assignedOutfitDetails = ref(null); // To store details of outfit for selectedDateInfo
const showOutfitSelector = ref(false); // To toggle visibility of outfit list
const availableOutfits = ref([]); // To store outfits from outfitStore

// Data for uni-calendar's extraInfo prop
// Example: [{date: '2024-07-28', info: '夏季穿搭', data: { outfitId: 'outfit1' }}]
const calendarExtraInfo = ref([]);

// Function to format date to YYYY-MM-DD
const formatDate = dateObj => {
  const year = dateObj.getFullYear();
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0');
  const day = dateObj.getDate().toString().padStart(2, '0');
  return `${year}-${month}-${day}`;
};

// Function to load assignments for the currently displayed month
const loadAssignmentsForMonth = async (year, month) => {
  const monthString = `${year}-${month.toString().padStart(2, '0')}`;
  // This getter returns { 'YYYY-MM-DD': 'outfitId', ... }
  const assignments = calendarStore.getAssignmentsForMonth(monthString);

  const extraInfoArray = [];
  for (const dateKey in assignments) {
    const outfitId = assignments[dateKey];
    // Optionally, fetch outfit name for 'info' if outfitStore is populated
    // For now, a generic marker:
    let outfitName = '已安排'; // Default text
    const outfit = outfitStore.getOutfitById(outfitId); // Assumes outfits are loaded in store
    if (outfit && outfit.name) {
      outfitName = outfit.name.substring(0, 10); // Show first 10 chars
    }

    extraInfoArray.push({
      date: dateKey,
      info: outfitName, // Display outfit name or a generic marker
      data: { outfitId } // Store outfitId for later use
    });
  }
  calendarExtraInfo.value = extraInfoArray;
};

// Handle calendar date change (month change)
const handleMonthSwitched = event => {
  currentDate.value = `${event.year}-${event.month.toString().padStart(2, '0')}-01`;
  loadAssignmentsForMonth(event.year, event.month);
};

// Handle date selection
const handleDateSelect = async event => {
  if (
    event.extraInfo &&
    event.extraInfo.data &&
    event.extraInfo.data.outfitId
  ) {
    selectedDateInfo.value = event;
    // Fetch and display outfit details
    const outfit = await calendarStore.fetchOutfitForDate(event.fullDate);
    if (outfit) {
      assignedOutfitDetails.value = outfit;
    } else {
      assignedOutfitDetails.value = null;
      uni.showToast({
        title: '获取穿搭详情失败',
        icon: 'none'
      }); // Used LOAD_FAILED_GENERAL
    }
  } else {
    selectedDateInfo.value = event; // Date selected, but no outfit assigned
    assignedOutfitDetails.value = null;
    // Directly open outfit selector when a date with no outfit is selected
    // Directly open outfit selector when a date with no outfit is selected
    openOutfitSelector(); // Directly call openOutfitSelector
  }
};

const loadInitialData = async () => {
  try {
    // 获取用户的穿搭列表
    await outfitStore.fetchUserOutfits();

    // 获取当月的穿搭日历安排
    const today = new Date();
    await calendarStore.fetchMonthAssignments(
      today.getFullYear(),
      today.getMonth() + 1
    );
  } catch (error) {
    console.error('加载初始数据失败:', error);
    uni.showToast({
      title: '加载数据失败',
      icon: 'none'
    });
  }
};

onMounted(async () => {
  await loadInitialData();
  const today = new Date();
  loadAssignmentsForMonth(today.getFullYear(), today.getMonth() + 1);
});

const openOutfitSelector = async () => {
  await outfitStore.fetchUserOutfits();
  availableOutfits.value = outfitStore.outfits;
  showOutfitSelector.value = true;
};

const handleAssignOutfit = outfitId => {
  if (!selectedDateInfo.value || !selectedDateInfo.value.fullDate) {
    // Should not happen if button is only shown when a date is selected
    console.error('No date selected for assignment');
    return;
  }
  const dateStr = selectedDateInfo.value.fullDate;
  calendarStore.assignOutfitToDate(dateStr, outfitId);
  showOutfitSelector.value = false; // Close the selector

  // Refresh calendar display for the current month
  const currentDateObj = new Date(dateStr);
  loadAssignmentsForMonth(
    currentDateObj.getFullYear(),
    currentDateObj.getMonth() + 1
  );

  // Also update the details for the currently selected date
  handleDateSelect({
    fullDate: dateStr,
    extraInfo: { data: { outfitId } }
  });
  uni.showToast({
    title: MISC_TEXT.OUTFIT_ASSIGNED_SUCCESS || '穿搭已成功安排！',
    icon: 'success'
  });
};

const handleUnassignOutfit = () => {
  if (
    !selectedDateInfo.value ||
    !selectedDateInfo.value.fullDate ||
    !assignedOutfitDetails.value
  ) {
    uni.showToast({
      title: MISC_TEXT.NO_OUTFIT_TO_UNASSIGN || '当前日期没有穿搭可取消',
      icon: 'none'
    });
    return;
  }
  const dateStr = selectedDateInfo.value.fullDate;
  calendarStore.removeOutfitFromDate(dateStr);

  // Refresh calendar display
  const currentDateObj = new Date(dateStr);
  loadAssignmentsForMonth(
    currentDateObj.getFullYear(),
    currentDateObj.getMonth() + 1
  );

  // Clear details for the currently selected date
  assignedOutfitDetails.value = null;
  // We need to call handleDateSelect again to show "no outfit" state,
  // but ensure extraInfo is cleared or it will re-fetch.
  // A simpler way for now is just to clear details and rely on user re-selecting or month switch.
  // For a better UX, we'd refetch or pass a null outfitId to handleDateSelect's logic.
  // For now, just clear the details and show toast.
  uni.showToast({
    title: MISC_TEXT.OUTFIT_UNASSIGNED_SUCCESS || '穿搭已取消安排',
    icon: 'success'
  });
  // Force re-evaluation of selectedDateInfo to update UI (or parts of it)
  const tempDateInfo = selectedDateInfo.value;
  selectedDateInfo.value = null; // Trigger reactivity
  setTimeout(() => {
    selectedDateInfo.value = tempDateInfo;
  }, 0); // Restore to re-render the section, now showing no outfit
};
</script>

<style scoped lang="scss">
.outfit-calendar-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 30rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);

  .title {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
  }
}

.calendar-content {
  flex: 1;
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  // justify-content: center; // Uncomment if you want to center the placeholder text
  // text-align: center; // No longer needed if children manage their own alignment
}

// Keep existing styles for .outfit-calendar-container and .header
// Add new styles:
.calendar-main-content {
  flex: 1;
  width: 100%;
  padding: 0 20rpx; // Add some horizontal padding
  box-sizing: border-box;
}

.selected-outfit-details {
  margin-top: 20rpx;
  padding: 20rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  width: 100%;
  box-sizing: border-box;

  .selected-date-text {
    font-size: 30rpx;
    font-weight: 600;
    margin-bottom: 15rpx;
    display: block;
    color: #333;
  }

  .outfit-card {
    display: flex;
    flex-direction: column;
    align-items: center;
  }

  .outfit-name-title {
    font-size: 28rpx;
    color: #555;
    margin-bottom: 10rpx;
  }

  .outfit-image-preview {
    width: 200rpx;
    height: 200rpx;
    border-radius: 8rpx;
    margin-bottom: 10rpx;
    background-color: #eee; // Placeholder bg
  }

  .outfit-name {
    font-size: 28rpx;
    font-weight: 500;
    color: #fda085; // Theme color
  }

  .no-outfit-assigned {
    font-size: 28rpx;
    color: #777;
    text-align: center;
    padding: 20rpx 0;
  }
}

.assign-outfit-section {
  // Styles for this section will be added later
  padding: 20rpx;
}

.action-button {
  font-size: 26rpx;
  padding: 10rpx 30rpx;
  margin-top: 20rpx;
  border-radius: 30rpx;
  background-color: #f0f0f0;
  color: #333;
  &:active {
    opacity: 0.8;
  }
}

.assign-button {
  background-color: #fda085;
  color: white;
}

.unassign-button {
  background-color: #ff4d4f;
  color: white;
  margin-right: 10rpx; // If placing next to change button
}
.change-button {
  background-color: #409eff; // Example color
  color: white;
}

.outfit-selector-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.outfit-selector-modal {
  background-color: white;
  padding: 30rpx;
  border-radius: 16rpx;
  width: 80%;
  max-height: 70vh;
  display: flex;
  flex-direction: column;

  .selector-title {
    font-size: 32rpx;
    font-weight: 600;
    text-align: center;
    margin-bottom: 20rpx;
    color: #333;
  }

  .outfits-list {
    flex-grow: 1;
    min-height: 200rpx;
    max-height: calc(70vh - 150rpx);
  }

  .no-outfits-available {
    text-align: center;
    color: #777;
    padding: 40rpx 0;
  }

  .outfit-list-item {
    display: flex;
    align-items: center;
    padding: 20rpx;
    border-bottom: 1rpx solid #eee;
    background-color: #fff;

    &:last-child {
      border-bottom: none;
    }

    &.outfit-list-item-hover {
      background-color: #f5f5f5;
    }

    .outfit-item-image-preview {
      width: 80rpx;
      height: 80rpx;
      border-radius: 8rpx;
      margin-right: 20rpx;
      background-color: #f0f0f0;
    }

    .outfit-item-name {
      font-size: 28rpx;
      color: #333;
      flex: 1;
    }
  }

  .cancel-button {
    background-color: #ddd;
    color: #333;
    margin-top: 20rpx;
    width: 100%;
    height: 80rpx;
    line-height: 80rpx;
    border-radius: 8rpx;
    font-size: 28rpx;

    &:active {
      opacity: 0.8;
    }
  }
}
</style>
