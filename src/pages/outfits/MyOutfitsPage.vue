<template>
  <view class="outfits-container">
    <view class="header">
      <view class="title-container">
        <text class="title">我的穿搭</text>
      </view>
      <view class="filter-section">
        <view class="filter-button" @click="showFilterOptions">
          <uni-icons type="filter" size="24" color="#777"></uni-icons>
          <text class="filter-text">筛选</text>
        </view>
      </view>
    </view>

    <scroll-view
      scroll-y
      class="outfits-content"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @scrolltolower="loadMore"
      @refresherrefresh="handleRefresh"
    >
      <view v-if="isLoading && !isLoadingMore" class="loading-container">
        <uni-load-more
          status="loading"
          :content-text="loadMoreText"
        ></uni-load-more>
      </view>

      <view v-else-if="outfits.length === 0" class="empty-container">
        <image
          src="/static/empty-outfit.png"
          mode="aspectFit"
          class="empty-image"
        ></image>
        <text class="empty-text">您还未创建任何穿搭</text>
        <text class="empty-subtext">创建穿搭，让您的衣物搭配更加便捷</text>
        <button class="create-button" @click="navigateToCreateOutfit">
          创建穿搭
        </button>
      </view>

      <view v-else class="outfits-grid">
        <view
          v-for="(outfit, index) in outfits"
          :key="index"
          class="outfit-card"
          @click="viewOutfitDetail(outfit.id)"
        >
          <view class="outfit-image-container">
            <image
              :src="outfit.coverImage"
              mode="aspectFill"
              class="outfit-cover"
            ></image>
            <view class="outfit-item-count"
              >{{ outfit.items.length }}件单品</view
            >
          </view>
          <view class="outfit-info">
            <text class="outfit-name">{{ outfit.name }}</text>
            <view
              v-if="Array.isArray(outfit.tags) && outfit.tags.length > 0"
              class="outfit-tags"
            >
              <text
                v-for="(tag, tagIndex) in outfit.tags.slice(0, 2)"
                :key="tagIndex"
                class="outfit-tag"
              >
                {{ tag }}
              </text>
              <text
v-if="outfit.tags.length > 2" class="more-tags"
                >+{{ outfit.tags.length - 2 }}</text
              >
            </view>
          </view>
        </view>
      </view>

      <uni-load-more
        v-if="outfits.length > 0"
        :status="loadMoreStatus"
        :content-text="loadMoreText"
      ></uni-load-more>
    </scroll-view>

    <view class="floating-button" @click="navigateToCreateOutfit">
      <uni-icons type="plusempty" size="30" color="#FFFFFF"></uni-icons>
    </view>
  </view>
</template>

<script setup>
// src/pages/outfits/MyOutfitsPage.vue
import { ref, onMounted, reactive } from 'vue';
import uniLoadMore from '@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.vue';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
import { useOutfitStore } from '@/stores/outfitStore';

const outfitStore = useOutfitStore();
const outfits = ref([]);
const isLoading = ref(true);
const isLoadingMore = ref(false);
const isRefreshing = ref(false);
const hasMore = ref(true);
const currentPage = ref(1);

const loadMoreStatus = ref('more');
const loadMoreText = reactive({
  contentdown: '上拉显示更多',
  contentrefresh: '加载中...',
  contentnomore: '没有更多了'
});

const fetchOutfits = async (page = 1) => {
  try {
    const response = await outfitStore.fetchUserOutfits({ page, pageSize: 10 });
    hasMore.value = response.hasMore;
    return response.outfits;
  } catch (error) {
    console.error('获取穿搭列表失败', error);
    uni.showToast({
      title: '获取穿搭列表失败',
      icon: 'none'
    });
    return [];
  }
};

// 初始加载数据
const loadInitialData = async () => {
  isLoading.value = true;
  try {
    const items = await fetchOutfits(1);
    outfits.value = items;
    currentPage.value = 1;
    hasMore.value = items.length > 0;
    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore';
  } catch (error) {
    console.error('加载初始数据失败', error);
  } finally {
    isLoading.value = false;
  }
};

// 加载更多数据
const loadMore = async () => {
  if (!hasMore.value || isLoadingMore.value) return;

  isLoadingMore.value = true;
  loadMoreStatus.value = 'loading';

  try {
    const nextPage = currentPage.value + 1;
    const newItems = await fetchOutfits(nextPage);

    if (newItems.length > 0) {
      outfits.value = [...outfits.value, ...newItems];
      currentPage.value = nextPage;
    } else {
      hasMore.value = false;
    }

    loadMoreStatus.value = hasMore.value ? 'more' : 'noMore';
  } catch (error) {
    console.error('加载更多数据失败', error);
    loadMoreStatus.value = 'more'; // 失败后允许重试
  } finally {
    isLoadingMore.value = false;
  }
};

// 刷新数据
const handleRefresh = async () => {
  isRefreshing.value = true;
  try {
    await loadInitialData();
  } finally {
    isRefreshing.value = false;
  }
};

// 页面加载时获取数据
onMounted(() => {
  loadInitialData();
});

// 导航到创建穿搭页面
const navigateToCreateOutfit = () => {
  uni.navigateTo({
    url: '/pages/main/createOutfit/CreateOutfitPage'
  });
};

// 查看穿搭详情 (待实现)
const viewOutfitDetail = id => {
  uni.showToast({
    title: '穿搭详情页开发中...',
    icon: 'none'
  });
};

// 显示筛选选项 (待实现)
const showFilterOptions = () => {
  uni.showToast({
    title: '筛选功能开发中...',
    icon: 'none'
  });
};
</script>

<style lang="scss" scoped>
.outfits-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f8f8f8;
  position: relative;
}

.header {
  padding: 30rpx 30rpx 20rpx;
  background-color: #fff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.title-container {
  flex: 1;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.filter-section {
  display: flex;
  align-items: center;
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
}

.filter-text {
  font-size: 26rpx;
  color: #777;
  margin-left: 8rpx;
}

.outfits-content {
  flex: 1;
  width: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-image {
  width: 250rpx;
  height: 250rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.create-button {
  background: linear-gradient(to right, #f6d365, #fda085);
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  padding: 0 60rpx;
  height: 80rpx;
  line-height: 80rpx;
}

.outfits-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.outfit-card {
  width: calc(50% - 20rpx);
  margin: 10rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.outfit-image-container {
  position: relative;
  width: 100%;
  height: 240rpx;
}

.outfit-cover {
  width: 100%;
  height: 100%;
}

.outfit-item-count {
  position: absolute;
  bottom: 10rpx;
  right: 10rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: #fff;
  font-size: 22rpx;
  border-radius: 20rpx;
}

.outfit-info {
  padding: 16rpx;
}

.outfit-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 10rpx;
  display: block;
}

.outfit-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8rpx;
}

.outfit-tag {
  padding: 2rpx 12rpx;
  background-color: rgba(253, 160, 133, 0.1);
  color: #fda085;
  font-size: 20rpx;
  border-radius: 20rpx;
}

.more-tags {
  font-size: 20rpx;
  color: #999;
  margin-left: 8rpx;
}

.floating-button {
  position: fixed;
  right: 40rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(to right, #f6d365, #fda085);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(253, 160, 133, 0.4);
  z-index: 100;
}
</style>
