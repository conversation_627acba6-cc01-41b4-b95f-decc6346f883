<template>
  <view class="select-items-container">
    <view class="header">
      <view class="title-section">
        <text class="title">{{ MISC_TEXT.SELECT_ITEMS_TITLE }}</text>
      </view>
    </view>

    <scroll-view scroll-y class="items-content" @scrolltolower="loadMore">
      <view class="filter-section">
        <view class="search-box">
          <uni-icons type="search" size="20" color="#999"></uni-icons>
          <input
            v-model="searchKeyword"
            type="text"
            :placeholder="PLACEHOLDERS.SEARCH_ITEMS"
            class="search-input"
          />
        </view>
        <view class="category-filter">
          <scroll-view scroll-x class="category-scroll">
            <view
              v-for="(category, index) in categoryFilterOptions"
              :key="index"
              class="category-item"
              :class="{ active: selectedCategory === category.value }"
              @click="selectCategory(category.value)"
            >
              {{ category.label }}
            </view>
          </scroll-view>
        </view>
      </view>

      <!-- Loading State -->
      <view v-if="isLoading" class="loading-container">
        <uni-load-more status="loading" :show-text="false"></uni-load-more>
      </view>

      <!-- Empty State -->
      <view
        v-else-if="!isLoading && filteredItems.length === 0"
        class="empty-state-container"
      >
        <text v-if="wardrobeStore.wardrobeItems.length === 0">
          {{ MISC_TEXT.NO_ITEMS_IN_WARDROBE || '您的衣橱里还没有任何衣物。' }}
          <!-- Optional: <button @click="navigateToAddItem">去添加</button> -->
        </text>
        <text v-else>
          {{
            MISC_TEXT.NO_ITEMS_MATCH_FILTERS || '没有找到符合筛选条件的衣物。'
          }}
        </text>
      </view>

      <!-- Items Grid -->
      <view v-else class="items-grid">
        <view
          v-for="item in filteredItems"
          :key="item.id"
          class="item-card"
          :class="{ selected: selectedItems.includes(item.id) }"
          @click="toggleSelectItem(item)"
        >
          <image
            :src="
              item.imageUrls && item.imageUrls.length > 0
                ? item.imageUrls[0]
                : '/static/placeholder-image.png'
            "
            mode="aspectFill"
            class="item-image"
          ></image>
          <view class="item-info">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-category">{{
              item.categoryDisplay || item.category
            }}</text>
          </view>
          <view class="select-indicator">
            <uni-icons
              :type="
                selectedItems.includes(item.id) ? 'checkbox-filled' : 'circle'
              "
              size="24"
              :color="selectedItems.includes(item.id) ? '#fda085' : '#ddd'"
            ></uni-icons>
          </view>
        </view>
      </view>

      <!-- Load More (conditionally shown based on if there are items and if not loading initial set) -->
      <uni-load-more
        v-if="!isLoading && filteredItems.length > 0"
        :status="loadMoreStatus"
      ></uni-load-more>
    </scroll-view>

    <view class="bottom-actions">
      <view class="selected-count"
        >已选择 {{ selectedItems.length }}
        {{ MISC_TEXT.SELECTED_COUNT_SUFFIX }}</view
      >
      <button
        class="confirm-button"
        :disabled="selectedItems.length === 0"
        @click="confirmSelection"
      >
        {{ BUTTON_TEXTS.CONFIRM_SELECTION }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'; // Add onMounted
import { useWardrobeStore } from '@/stores/wardrobeStore.js';
import { useOutfitStore } from '@/stores/outfitStore.js';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
import uniLoadMore from '@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.vue';
import { BUTTON_TEXTS, PLACEHOLDERS, MISC_TEXT } from '@/constants/strings.js';

const wardrobeStore = useWardrobeStore();
const outfitStore = useOutfitStore();

// 状态
const searchKeyword = ref('');
const selectedCategory = ref(''); // This will be a category *key* like 'tops'
const selectedItems = ref([]); // Stores IDs of selected items
const loadMoreStatus = ref('more');
const isLoading = ref(false); // Added for loading state during fetch

// 检查裤子数量并提醒
const checkPantsCount = selectedItemsData => {
  const pantsCount = selectedItemsData.filter(
    item =>
      item.category === 'bottoms' &&
      wardrobeStore.categorySystem.bottoms.subcategories.pants.types.includes(
        item.type
      )
  ).length;

  if (pantsCount >= 2) {
    uni.showToast({
      title: '您已选择了两条裤子，这可能不太合理哦',
      icon: 'none',
      duration: 2000
    });
  }
};

// const categories = ref([ ... hardcoded options ... ]); // REMOVE THIS

const categoryFilterOptions = computed(() => {
  const options = [{ value: '', label: '全部' }]; // "All" option
  if (wardrobeStore.categorySystem) {
    for (const key in wardrobeStore.categorySystem) {
      options.push({
        value: key,
        label: wardrobeStore.categorySystem[key].name
      });
    }
  }
  return options;
});

// 过滤后的衣物列表
const filteredItems = computed(() => {
  return wardrobeStore.wardrobeItems.filter(item => {
    const matchesSearch = item.name
      .toLowerCase()
      .includes(searchKeyword.value.toLowerCase());
    // Assuming selectedCategory will store the *key* (e.g., 'tops')
    // And item.category is the *key* from the store data
    const matchesCategory =
      !selectedCategory.value || item.category === selectedCategory.value;
    return matchesSearch && matchesCategory;
  });
});

// 选择分类
const selectCategory = category => {
  selectedCategory.value = category;
};

// 切换选择衣物
const toggleSelectItem = item => {
  const index = selectedItems.value.indexOf(item.id);
  if (index === -1) {
    // 添加选中项
    selectedItems.value.push(item.id);
    // 检查是否需要显示提醒
    const selectedItemsData = wardrobeStore.wardrobeItems.filter(i =>
      selectedItems.value.includes(i.id)
    );
    checkPantsCount(selectedItemsData);
  } else {
    selectedItems.value.splice(index, 1);
  }
};

// 确认选择
const confirmSelection = () => {
  const selectedItemsData = wardrobeStore.wardrobeItems.filter(item =>
    selectedItems.value.includes(item.id)
  );

  // 使用 outfitStore 更新选中的衣物
  outfitStore.updateOutfitAction({
    clothingItems: selectedItemsData
  });

  // 返回上一页
  uni.navigateBack();
};

// 加载更多
const loadMore = () => {
  // With current mock store, all items are loaded at once.
  // If store supported pagination, this would call an action in the store.
  loadMoreStatus.value = 'noMore';
};

onMounted(async () => {
  isLoading.value = true;
  if (wardrobeStore.wardrobeItems.length === 0) {
    try {
      await wardrobeStore.fetchWardrobeItemsAction();
    } catch (error) {
      console.error('Failed to fetch wardrobe items:', error);
      // Optionally show a toast to the user
    }
  }
  isLoading.value = false;
  // Logic to dynamically populate `categories` from wardrobeStore.categorySystem will be in next step.
});
</script>

<style lang="scss" scoped>
.select-items-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 30rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.items-content {
  flex: 1;
  padding-bottom: 120rpx;
}

.filter-section {
  padding: 20rpx;
  background-color: #fff;
  margin-bottom: 20rpx;
}

.search-box {
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  padding: 10rpx 20rpx;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.search-input {
  flex: 1;
  height: 60rpx;
  margin-left: 10rpx;
  font-size: 28rpx;
}

.category-scroll {
  white-space: nowrap;
}

.category-item {
  display: inline-block;
  padding: 10rpx 30rpx;
  margin-right: 20rpx;
  font-size: 28rpx;
  color: #666;
  background-color: #f5f5f5;
  border-radius: 30rpx;

  &.active {
    background-color: #fda085;
    color: #fff;
  }
}

.items-grid {
  padding: 20rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 50rpx 0;
  width: 100%;
}

.empty-state-container {
  display: flex;
  justify-content: center;
  align-items: center;
  text-align: center;
  padding: 50rpx 20rpx;
  font-size: 28rpx;
  color: #777;
  width: 100%;
  min-height: 200rpx; // Give it some visible height
}

.item-card {
  width: calc(50% - 10rpx);
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;

  &.selected {
    border: 2rpx solid #fda085;
  }
}

.item-image {
  width: 100%;
  height: 200rpx;
  background-color: #f5f5f5;
}

.item-info {
  padding: 16rpx;
}

.item-name {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 4rpx;
}

.item-category {
  font-size: 24rpx;
  color: #999;
}

.select-indicator {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  border-top: 1rpx solid #eee;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.selected-count {
  font-size: 28rpx;
  color: #666;
}

.confirm-button {
  width: 240rpx;
  height: 80rpx;
  line-height: 80rpx;
  background: linear-gradient(to right, #f6d365, #fda085);
  color: #fff;
  font-size: 30rpx;
  border-radius: 12rpx;

  &[disabled] {
    opacity: 0.7;
  }
}
</style>
