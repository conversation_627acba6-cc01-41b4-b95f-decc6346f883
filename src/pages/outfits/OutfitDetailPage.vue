<template>
  <view class="outfit-detail-container">
    <!-- 顶部图片区域 -->
    <swiper class="image-swiper" :indicator-dots="true" :autoplay="false">
      <swiper-item v-for="(image, index) in outfitImages" :key="index">
        <image :src="image" mode="aspectFill" class="outfit-image"></image>
      </swiper-item>
    </swiper>

    <!-- 收藏按钮 -->
    <FavoriteButton
      :item-id="outfitData?.id"
      item-type="outfit"
      size="large"
      variant="default"
      class="favorite-button"
    />

    <!-- 穿搭信息区域 -->
    <view class="info-section">
      <view class="basic-info">
        <text class="outfit-name">{{ outfitData.name }}</text>
        <text class="outfit-occasion">{{ outfitData.occasion }}</text>
      </view>

      <view class="description-section">
        <text class="section-title">穿搭描述</text>
        <text class="description-text">{{ outfitData.description }}</text>
      </view>

      <!-- 包含的衣物 -->
      <view class="included-items-section">
        <text class="section-title">包含的衣物</text>
        <view class="items-grid">
          <view
            v-for="(item, index) in outfitData.items"
            :key="index"
            class="item-card"
            @click="viewItemDetail(item)"
          >
            <image
              :src="item.imageUrl"
              mode="aspectFill"
              class="item-image"
            ></image>
            <view class="item-info">
              <text class="item-name">{{ item.name }}</text>
              <text class="item-category">{{ item.category }}</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 标签 -->
      <view v-if="outfitData.tags.length > 0" class="tags-section">
        <text class="section-title">标签</text>
        <view class="tags-container">
          <view
            v-for="(tag, index) in outfitData.tags"
            :key="index"
            class="tag"
          >
            {{ tag }}
          </view>
        </view>
      </view>

      <!-- 创建时间 -->
      <view class="creation-info">
        <text class="creation-date">创建于 {{ outfitData.createdAt }}</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
import { useOutfitStore } from '@/stores/outfitStore';
import FavoriteButton from '@/components/common/FavoriteButton.vue';

const outfitStore = useOutfitStore();

// 状态
const outfitImages = ref([]);
const outfitData = ref(null);

// Note: toggleFavorite functionality is now handled by FavoriteButton component

// 查看衣物详情
const viewItemDetail = item => {
  uni.navigateTo({
    url: `/pages/wardrobe/ItemDetailPage?id=${item.id}`
  });
};

// 页面加载时
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const id = currentPage.options?.id;

  if (id) {
    outfitStore
      .getOutfitById(id)
      .then(outfit => {
        outfitData.value = outfit;
        outfitImages.value = outfit.imageUrls;
      })
      .catch(error => {
        uni.showToast({
          title: error.message || '获取穿搭详情失败',
          icon: 'none'
        });
      });
  }
});
</script>

<style lang="scss" scoped>
.outfit-detail-container {
  min-height: 100vh;
  background-color: #f8f8f8;
}

.image-swiper {
  width: 100%;
  height: 500rpx;

  .outfit-image {
    width: 100%;
    height: 100%;
  }
}

.favorite-button {
  position: absolute;
  top: 40rpx;
  right: 40rpx;
  width: 80rpx;
  height: 80rpx;
  background-color: rgba(255, 255, 255, 0.9);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
}

.info-section {
  margin-top: -40rpx;
  padding: 40rpx 30rpx;
  background-color: #fff;
  border-radius: 40rpx 40rpx 0 0;
  position: relative;
  z-index: 1;
}

.basic-info {
  margin-bottom: 30rpx;

  .outfit-name {
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 10rpx;
    display: block;
  }

  .outfit-occasion {
    font-size: 28rpx;
    color: #666;
  }
}

.description-section {
  margin-bottom: 30rpx;

  .section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }

  .description-text {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
  }
}

.included-items-section {
  margin-bottom: 30rpx;

  .section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 20rpx;
    display: block;
  }
}

.items-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;
}

.item-card {
  background-color: #f8f8f8;
  border-radius: 12rpx;
  overflow: hidden;

  .item-image {
    width: 100%;
    height: 200rpx;
  }

  .item-info {
    padding: 16rpx;

    .item-name {
      font-size: 26rpx;
      color: #333;
      margin-bottom: 4rpx;
      display: block;
    }

    .item-category {
      font-size: 24rpx;
      color: #999;
    }
  }
}

.tags-section {
  margin-bottom: 30rpx;

  .section-title {
    font-size: 30rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    display: block;
  }
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;

  .tag {
    padding: 8rpx 20rpx;
    background-color: rgba(253, 160, 133, 0.1);
    color: #fda085;
    border-radius: 30rpx;
    font-size: 24rpx;
  }
}

.creation-info {
  text-align: center;
  padding-top: 30rpx;
  border-top: 2rpx solid #f0f0f0;

  .creation-date {
    font-size: 24rpx;
    color: #999;
  }
}
</style>
