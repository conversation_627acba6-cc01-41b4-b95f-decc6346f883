<template>
  <view class="main-container">
    <!-- 数据同步管理器 -->
    <DataSyncManager
      ref="dataSyncManager"
      :data-sources="dataSources"
      :auto-refresh-interval="30000"
      :enable-auto-refresh="true"
      :show-sync-indicator="true"
      @data-updated="handleDataUpdated"
    />
    
    <!-- 主内容区域 -->
    <view class="main-content">
      <!-- 欢迎与概览区 -->
      <view class="welcome-banner-section">
        <text class="greeting-title">
          早上好, {{ userName || '时尚达人' }}!
        </text>
        <text class="greeting-subtitle">
          今天想穿什么，或记录新的时尚单品？
        </text>
        <view class="weather-info">
          今日天气：晴转多云, 25°C - 适合轻薄外套
        </view>
      </view>

      <!-- 快捷操作区 -->
      <view class="section-container">
        <text class="section-title">快捷入口</text>
        <view class="quick-actions-section">
          <view class="action-card" @click="handleQuickAction('addItem')">
            <uni-icons type="plusempty" size="30" color="#fda085"></uni-icons>
            <text class="action-text">添加衣物</text>
          </view>
          <view class="action-card" @click="handleQuickAction('favorites')">
            <uni-icons type="star" size="30" color="#fda085"></uni-icons>
            <text class="action-text">我的收藏</text>
          </view>
          <view class="action-card" @click="handleQuickAction('createOutfit')">
            <uni-icons type="paperplane" size="30" color="#fda085"></uni-icons>
            <text class="action-text">创建搭配</text>
          </view>
          <view class="action-card" @click="handleQuickAction('calendar')">
            <uni-icons type="calendar" size="30" color="#fda085"></uni-icons>
            <text class="action-text">穿搭日历</text>
          </view>
        </view>
      </view>

      <!-- 最近添加区域 -->
      <view class="section-container">
        <view class="section-title-container">
          <view class="section-title-line"></view>
          <text class="section-title">最近添加</text>
        </view>
        <view class="recent-items-grid">
          <CompactCard
            v-for="(item, index) in recentItems"
            :key="index"
            :item="item"
            type="clothing"
            @click="handleItemClick"
          />
        </view>
      </view>

      <!-- 搭配推荐区域 -->
      <view class="section-container">
        <view class="section-title-container">
          <view class="section-title-line"></view>
          <text class="section-title">搭配推荐</text>
        </view>
        <view class="outfit-suggestions-grid">
          <CompactCard
            v-for="(outfit, index) in outfitSuggestions"
            :key="index"
            :item="outfit"
            type="outfit"
            @click="handleOutfitClick"
          />
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
import { onLoad } from '@dcloudio/uni-app';
import { useWardrobeStore } from '@/stores/wardrobeStore';
import { useOutfitStore } from '@/stores/outfitStore';
import { useUserStore } from '@/stores/userStore';
import CompactCard from '@/components/common/CompactCard.vue';
import DataSyncManager from '@/components/common/DataSyncManager.vue';

const userStore = useUserStore();
const wardrobeStore = useWardrobeStore();
const outfitStore = useOutfitStore();

const recentItems = ref([]);
const outfitSuggestions = ref([]);
const userName = ref(userStore.username);
const dataSyncManager = ref(null);

// 数据源配置
const dataSources = ref([
  {
    key: 'wardrobe',
    fetchFn: () => wardrobeStore.fetchItems(),
    cacheKey: 'main_page_wardrobe',
    updateStore: (data) => {
      // 更新最近添加的衣物
      updateRecentItems();
    }
  },
  {
    key: 'outfits',
    fetchFn: () => outfitStore.fetchOutfitList(),
    cacheKey: 'main_page_outfits',
    updateStore: (data) => {
      // 更新搭配推荐
      updateOutfitSuggestions();
    }
  }
]);

// 实时更新监听器
let wardrobeUpdateListener = null;
let outfitUpdateListener = null;

// 快捷操作处理方法
const handleQuickAction = action => {
  switch (action) {
    case 'addItem':
      uni.navigateTo({ url: '/pages/main/addItem/AddItemPage' });
      break;
    case 'favorites':
      uni.navigateTo({ url: '/pages/main/myCollections/MyCollectionsPage' });
      break;
    case 'createOutfit':
      uni.navigateTo({ url: '/pages/main/createOutfit/CreateOutfitPage' });
      break;
    case 'calendar':
      uni.navigateTo({ url: '/pages/calendar/OutfitCalendarPage' });
      break;
  }
};

// 处理衣物点击事件
const handleItemClick = item => {
  uni.navigateTo({
    url: `/pages/main/clothingDetail/ClothingDetailPage?id=${item.id}`
  });
};

// 处理搭配点击事件
const handleOutfitClick = outfit => {
  uni.navigateTo({
    url: `/pages/outfits/OutfitDetailPage?id=${outfit.id}`
  });
};

// 更新最近添加的衣物
const updateRecentItems = () => {
  recentItems.value = wardrobeStore.wardrobeItems
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, 6)
    .map(item => ({
      id: item.id,
      name: item.name,
      categoryDisplay: item.categoryDisplay,
      imageUrls: item.imageUrls || [],
      imageUrl: item.imageUrls?.[0] || '/static/images/empty-collection.png'
    }));
};

// 更新搭配推荐
const updateOutfitSuggestions = () => {
  outfitSuggestions.value = outfitStore.outfitList
    .slice(0, 4)
    .map(outfit => ({
      id: outfit.id,
      name: outfit.name,
      occasion: outfit.occasion,
      imageUrl: outfit.imageUrl || '/static/images/empty-collection.png'
    }));
};

// 处理数据更新事件
const handleDataUpdated = () => {
  console.log('主页数据已更新');
  // 数据源的 updateStore 回调已经处理了具体的更新逻辑
};

// 设置实时更新监听器
const setupUpdateListeners = () => {
  // 监听衣橱更新
  wardrobeUpdateListener = wardrobeStore.addUpdateListener((update) => {
    console.log('收到衣橱更新通知:', update);
    
    switch (update.type) {
      case 'item_added':
      case 'item_updated':
      case 'item_deleted':
        updateRecentItems();
        break;
    }
  });

  // 监听搭配更新
  outfitUpdateListener = outfitStore.addUpdateListener((update) => {
    console.log('收到搭配更新通知:', update);
    
    switch (update.type) {
      case 'outfit_created':
      case 'outfit_updated':
      case 'outfit_deleted':
        updateOutfitSuggestions();
        break;
    }
  });

  // 监听全局事件
  const wardrobeGlobalListener = wardrobeStore.onGlobalEvent('wardrobe:update', (data) => {
    console.log('收到衣橱全局更新事件:', data);
    updateRecentItems();
  });

  const outfitGlobalListener = outfitStore.onGlobalEvent('outfit:update', (data) => {
    console.log('收到搭配全局更新事件:', data);
    updateOutfitSuggestions();
  });

  // 返回清理函数
  return () => {
    if (wardrobeUpdateListener) wardrobeUpdateListener();
    if (outfitUpdateListener) outfitUpdateListener();
    wardrobeGlobalListener();
    outfitGlobalListener();
  };
};

// 清理监听器
const cleanupListeners = () => {
  if (wardrobeUpdateListener) {
    wardrobeUpdateListener();
    wardrobeUpdateListener = null;
  }
  if (outfitUpdateListener) {
    outfitUpdateListener();
    outfitUpdateListener = null;
  }
};

// 页面加载时
onLoad(async () => {
  // 获取用户信息
  try {
    await userStore.fetchUserInfo();
    userName.value = userStore.username;
  } catch (error) {
    console.warn('获取用户信息失败:', error);
  }

  // 设置实时更新监听器
  setupUpdateListeners();

  // 初始数据加载（DataSyncManager 会自动处理）
  // 但我们需要确保初始显示
  try {
    await wardrobeStore.fetchItems();
    updateRecentItems();
  } catch (error) {
    console.warn('获取衣橱数据失败:', error);
  }

  try {
    await outfitStore.fetchOutfitList();
    updateOutfitSuggestions();
  } catch (error) {
    console.warn('获取搭配数据失败:', error);
  }
});

// 组件挂载时
onMounted(() => {
  // 处理待更新的数据
  wardrobeStore.processPendingUpdates();
  outfitStore.processPendingUpdates();
});

// 组件卸载时清理监听器
onUnmounted(() => {
  cleanupListeners();
});
</script>

<style scoped lang="scss">
// 颜色变量定义
$theme-primary-color: #fda085;
$theme-secondary-color: #f6d365;
$text-dark-color: #333333;
$text-light-color: #777777;
$card-bg-color: #ffffff;
$page-bg-color: #f9f9f9;
$border-color: #e0e0e0;
$shadow-color: rgba(0, 0, 0, 0.1);
$border-radius-base: 15rpx;

.main-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: $page-bg-color;
}

.main-content {
  flex-grow: 1;
  padding: 30rpx;
  box-sizing: border-box;
}

// 欢迎区域样式
.welcome-banner-section {
  background-color: $card-bg-color;
  border-radius: $border-radius-base;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 8rpx 25rpx $shadow-color;
  display: flex;
  flex-direction: column;
  align-items: center;

  .greeting-title {
    font-size: 44rpx;
    font-weight: 700;
    color: $text-dark-color;
    margin-bottom: 15rpx;
    text-align: center;
  }

  .greeting-subtitle {
    font-size: 28rpx;
    color: $text-light-color;
    margin-bottom: 20rpx;
    text-align: center;
  }

  .weather-info {
    font-size: 28rpx;
    color: $theme-primary-color;
    font-weight: 600;
    text-align: center;
  }
}

// 区块容器通用样式
.section-container {
  margin-bottom: 40rpx;
}

// 标题样式
.section-title-container {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.section-title-line {
  width: 8rpx;
  height: 36rpx;
  background-color: $theme-primary-color;
  margin-right: 15rpx;
}

.section-title {
  font-size: 34rpx;
  font-weight: 700;
  color: $text-dark-color;
  margin-bottom: 20rpx;
}

// 快捷操作区样式
.quick-actions-section {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20rpx;

  .action-card {
    background-color: $card-bg-color;
    border-radius: $border-radius-base;
    padding: 30rpx;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    box-shadow: 0 8rpx 15rpx $shadow-color;

    .action-text {
      font-size: 28rpx;
      font-weight: 600;
      color: $text-dark-color;
      margin-top: 15rpx;
    }
  }
}

// 最近添加区域样式 - 使用响应式网格布局
.recent-items-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;

  // 平板及以上显示3列
  @media (min-width: 481px) and (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  // 大屏显示4列
  @media (min-width: 769px) {
    grid-template-columns: repeat(4, 1fr);
  }
}

// 搭配推荐区域样式 - 使用响应式网格布局
.outfit-suggestions-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16rpx;

  // 平板及以上显示3列
  @media (min-width: 481px) and (max-width: 768px) {
    grid-template-columns: repeat(3, 1fr);
  }

  // 大屏显示4列
  @media (min-width: 769px) {
    grid-template-columns: repeat(4, 1fr);
  }
}
</style>
