<template>
  <view class="detail-container">
    <!-- Loading State -->
    <view v-if="isLoading" class="loading-container">
      <uni-load-more
        status="loading"
        :content-text="loadingText"
      ></uni-load-more>
    </view>

    <template v-else>
      <!-- Image Display Section -->
      <view class="image-section">
        <swiper
          v-if="clothingItem?.imageUrls?.length > 0"
          class="image-swiper"
          :indicator-dots="clothingItem.imageUrls.length > 1"
          :autoplay="false"
          circular
        >
          <swiper-item
            v-for="(imageUrl, index) in clothingItem.imageUrls"
            :key="index"
          >
            <image
              class="item-image"
              :src="imageUrl"
              mode="aspectFill"
              @click="previewFullImage(index)"
            ></image>
          </swiper-item>
        </swiper>
        <view v-else class="no-image">
          <text>暂无图片</text>
        </view>
      </view>

      <!-- Core Information Section -->
      <view class="core-info-section section-card">
        <view class="item-name-row">
          <text class="item-name">{{
            clothingItem?.name || '未命名衣物'
          }}</text>
        </view>
        <view class="item-brand-row">
          <text class="label">品牌：</text>
          <text class="value">{{ clothingItem?.brand || '未填写' }}</text>
        </view>
        <view class="item-category-row">
          <text class="label">分类：</text>
          <text class="value">{{ formattedCategoryPath }}</text>
        </view>
      </view>

      <!-- Tags & Attributes Section -->
      <view class="tags-attributes-section section-card">
        <view v-if="clothingItem?.fitTags?.length" class="tag-group">
          <text class="group-label">版型：</text>
          <view class="tags-wrapper">
            <text
              v-for="(tag, index) in clothingItem.fitTags"
              :key="index"
              class="tag-item"
              >{{ getTagText(tag, fitOptions) }}</text
            >
          </view>
        </view>

        <view v-if="clothingItem?.styleTags?.length" class="tag-group">
          <text class="group-label">风格：</text>
          <view class="tags-wrapper">
            <text
              v-for="(tag, index) in clothingItem.styleTags"
              :key="index"
              class="tag-item"
              >{{ getTagText(tag, styleOptions) }}</text
            >
          </view>
        </view>

        <view v-if="clothingItem?.colorTags?.length" class="tag-group">
          <text class="group-label">颜色：</text>
          <view class="tags-wrapper">
            <view
              v-for="(tag, index) in clothingItem.colorTags"
              :key="index"
              class="tag-item color-tag"
            >
              <view
                class="color-dot"
                :style="{ backgroundColor: getColorHex(tag) }"
              ></view>
              <text>{{ getTagText(tag, colorOptions) }}</text>
            </view>
          </view>
        </view>

        <view v-if="clothingItem?.materialTags?.length" class="tag-group">
          <text class="group-label">材质：</text>
          <view class="tags-wrapper">
            <text
              v-for="(tag, index) in clothingItem.materialTags"
              :key="index"
              class="tag-item"
              >{{ getTagText(tag, materialOptions) }}</text
            >
          </view>
        </view>

        <view v-if="clothingItem?.seasonTags?.length" class="tag-group">
          <text class="group-label">季节：</text>
          <view class="tags-wrapper">
            <text
              v-for="(tag, index) in clothingItem.seasonTags"
              :key="index"
              class="tag-item"
              >{{ getTagText(tag, seasonOptions) }}</text
            >
          </view>
        </view>
      </view>

      <!-- Purchase & Other Info Section -->
      <view class="other-info-section section-card">
        <view class="info-row">
          <text class="label">购买日期：</text>
          <text class="value">{{
            formatDate(clothingItem?.purchaseDate)
          }}</text>
        </view>
        <view class="info-row">
          <text class="label">价格：</text>
          <text class="value">{{ formatPrice(clothingItem?.price) }}</text>
        </view>
        <view class="info-row notes-row">
          <text class="label">备注：</text>
          <text class="value notes-value">{{
            clothingItem?.notes || '无'
          }}</text>
        </view>
      </view>

      <!-- Action Buttons -->
      <view class="action-bar">
        <FavoriteButton
          :item-id="itemId"
          item-type="clothing"
          size="large"
          variant="filled"
          :show-text="true"
          class="favorite-action-button"
        />
        <button class="action-button edit-button" @click="navigateToEdit">
          编辑
        </button>
        <button class="action-button delete-button" @click="confirmDeleteItem">
          删除
        </button>
      </view>
    </template>
  </view>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useWardrobeStore } from '@/stores/wardrobeStore';
import FavoriteButton from '@/components/common/FavoriteButton.vue';
import {
  fitOptions,
  styleOptions,
  colorOptions,
  materialOptions,
  seasonOptions
} from '@/constants/options';

// Store 实例
const wardrobeStore = useWardrobeStore();

// 页面参数
const itemId = ref(null);
const isLoading = ref(true);
const loadingText = {
  contentdown: '加载中...',
  contentrefresh: '加载中...',
  contentnomore: '没有更多'
};

// 生命周期
onMounted(() => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  const options = currentPage.$page?.options;

  if (options?.id) {
    itemId.value = options.id;
    fetchClothingItemDetail();
  } else {
    console.error('未获取到衣物ID');
    uni.showToast({
      title: '参数错误',
      icon: 'none'
    });
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  }
});

// 衣物数据
const clothingItem = ref(null);

// Options data imported from @/constants/options.js

// 计算属性
const formattedCategoryPath = computed(() => {
  if (!clothingItem.value?.categoryTree?.length) return '未分类';
  return clothingItem.value.categoryTree.join(' > ');
});

// 工具方法
const getTagText = (value, options) => {
  const option = options.find(opt => opt.value === value);
  return option ? option.text : value;
};

const getColorHex = value => {
  const option = colorOptions.find(opt => opt.value === value);
  return option ? option.hex : '#CCCCCC';
};

const formatDate = date => {
  if (!date) return '未记录';
  return new Date(date).toLocaleDateString('zh-CN');
};

const formatPrice = price => {
  if (!price) return '未记录';
  return `¥${price}`;
};

// 图片预览
const previewFullImage = index => {
  if (!clothingItem.value?.imageUrls?.length) return;
  uni.previewImage({
    urls: clothingItem.value.imageUrls,
    current: index
  });
};

// 编辑导航
const navigateToEdit = () => {
  if (!itemId.value) {
    uni.showToast({
      title: '无法获取衣物ID',
      icon: 'none'
    });
    return;
  }
  uni.navigateTo({
    url: `/pages/main/addItem/AddItemPage?id=${itemId.value}&mode=edit`
  });
};

// 删除确认
const confirmDeleteItem = () => {
  uni.showModal({
    title: '确认删除',
    content: '确定要删除这件衣物吗？此操作不可撤销。',
    success: async res => {
      if (res.confirm) {
        try {
          await wardrobeStore.deleteItem(itemId.value);
          uni.showToast({
            title: '删除成功',
            icon: 'success'
          });

          setTimeout(() => {
            uni.navigateBack();
          }, 1500);
        } catch (error) {
          console.error('删除失败:', error);
          uni.showToast({
            title: '删除失败',
            icon: 'none'
          });
        }
      }
    }
  });
};

// 获取衣物详情
const fetchClothingItemDetail = async () => {
  isLoading.value = true;
  try {
    const item = await wardrobeStore.getItemByIdAsync(itemId.value);
    if (item) {
      clothingItem.value = item;
    } else {
      throw new Error('未找到衣物');
    }
  } catch (error) {
    console.error('获取衣物详情失败:', error);
    uni.showToast({
      title: '获取数据失败',
      icon: 'none'
    });
  } finally {
    isLoading.value = false;
  }
};
</script>

<style lang="scss" scoped>
.detail-container {
  min-height: 100vh;
  background-color: #f8f8f8;
  padding-bottom: calc(120rpx + env(safe-area-inset-bottom));
}

.loading-container {
  padding: 40rpx 0;
}

.image-section {
  width: 100%;
  height: 70vw;
  background-color: #fff;

  .image-swiper {
    width: 100%;
    height: 100%;

    .item-image {
      width: 100%;
      height: 100%;
    }
  }

  .no-image {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #999;
  }
}

.section-card {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #fff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.05);
}

.core-info-section {
  .item-name-row {
    margin-bottom: 20rpx;

    .item-name {
      font-size: 36rpx;
      font-weight: bold;
      color: #333;
    }
  }

  .item-brand-row,
  .item-category-row {
    margin-top: 16rpx;
    display: flex;
    align-items: center;

    .label {
      color: #666;
      width: 120rpx;
    }

    .value {
      color: #333;
      flex: 1;
    }
  }
}

.tags-attributes-section {
  .tag-group {
    margin-bottom: 24rpx;

    &:last-child {
      margin-bottom: 0;
    }

    .group-label {
      color: #666;
      margin-bottom: 16rpx;
      display: block;
    }

    .tags-wrapper {
      display: flex;
      flex-wrap: wrap;
      gap: 16rpx;

      .tag-item {
        padding: 8rpx 20rpx;
        background-color: #f5f5f5;
        border-radius: 100rpx;
        font-size: 24rpx;
        color: #666;

        &.color-tag {
          display: flex;
          align-items: center;

          .color-dot {
            width: 24rpx;
            height: 24rpx;
            border-radius: 50%;
            margin-right: 8rpx;
            border: 1rpx solid rgba(0, 0, 0, 0.1);
          }
        }
      }
    }
  }
}

.other-info-section {
  .info-row {
    margin-bottom: 16rpx;
    display: flex;

    &:last-child {
      margin-bottom: 0;
    }

    .label {
      color: #666;
      width: 120rpx;
    }

    .value {
      color: #333;
      flex: 1;
    }

    &.notes-row {
      flex-direction: column;

      .label {
        margin-bottom: 8rpx;
      }

      .notes-value {
        padding: 16rpx;
        background-color: #f5f5f5;
        border-radius: 8rpx;
        width: 100%;
        box-sizing: border-box;
        min-height: 80rpx;
      }
    }
  }
}

.action-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  padding: 20rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  display: flex;
  gap: 20rpx;
  box-sizing: border-box;
  padding-bottom: calc(20rpx + env(safe-area-inset-bottom));

  .favorite-action-button {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 28rpx;
  }

  .action-button {
    flex: 1;
    height: 80rpx;
    line-height: 80rpx;
    text-align: center;
    border-radius: 40rpx;
    font-size: 28rpx;

    &.edit-button {
      background-color: #fff;
      border: 2rpx solid #fda085;
      color: #fda085;
    }

    &.delete-button {
      background-color: #ff4d4f;
      color: #fff;
    }
  }
}
</style>
