<template>
  <view class="create-outfit-container">
    <uni-forms
      ref="outfitForm"
      :model-value="outfitData"
      :rules="outfitFormRules"
      label-position="top"
    >
      <view class="form-section">
        <uni-forms-item label="穿搭名称" name="name" required>
          <uni-easyinput
            v-model="outfitData.name"
            type="text"
            placeholder="例如：休闲周末风"
          />
        </uni-forms-item>
      </view>

      <view class="form-section">
        <uni-forms-item label="选择衣物" name="itemIds" required>
          <!-- 加载状态 -->
          <view v-if="wardrobeStore.isLoading" class="loading-notice">
            <uni-load-more
              status="loading"
              content-text="正在加载衣物..."
            ></uni-load-more>
          </view>
          <!-- 空状态 -->
          <view v-else-if="availableItems.length === 0" class="no-items-notice">
            您的衣橱里还没有衣物，请先去
            <text class="link" @click="goToAddItemPage">添加衣物</text>。
          </view>
          <!-- 已选择衣物预览 -->
          <view v-else class="outfit-selection-container">
            <SelectedItemsPreview
              :selected-items="selectedClothingItems"
              @remove-item="handleRemoveItem"
              @clear-all="handleClearAll"
              @item-click="handleItemClick"
            />
            <!-- 衣物选择器 -->
            <ClothingSelector
              v-model="selectedClothingItems"
              @change="handleSelectionChange"
            />
          </view>
        </uni-forms-item>
      </view>

      <view class="form-section">
        <uni-forms-item label="场合 (可选)" name="occasion">
          <uni-easyinput
            v-model="outfitData.occasion"
            type="text"
            placeholder="例如：工作、约会、运动"
          />
        </uni-forms-item>
      </view>

      <view class="form-section">
        <uni-forms-item label="备注 (可选)" name="notes">
          <uni-easyinput
            v-model="outfitData.notes"
            type="textarea"
            placeholder="关于这套穿搭的说明..."
          />
        </uni-forms-item>
      </view>

      <!-- 搭配预览确认 -->
      <view v-if="selectedClothingItems.length > 0" class="outfit-preview-section">
        <view class="preview-header">
          <text class="preview-title">搭配预览</text>
          <text class="preview-count">{{ selectedClothingItems.length }} 件衣物</text>
        </view>
        <view class="outfit-preview-grid">
          <view 
            v-for="item in selectedClothingItems" 
            :key="item.id" 
            class="preview-outfit-item"
          >
            <image 
              class="preview-outfit-image" 
              :src="item.imageUrls?.[0] || '/static/images/empty-collection.png'" 
              mode="aspectFill"
            />
            <text class="preview-outfit-name">{{ item.name }}</text>
          </view>
        </view>
      </view>

      <view class="form-actions">
        <button
          class="submit-button"
          type="primary"
          :loading="isSaving"
          :disabled="isSubmitDisabled"
          @click="handleSubmitOutfit"
        >
          {{ isEditMode ? '更新穿搭' : '保存穿搭' }}
        </button>
      </view>
    </uni-forms>
  </view>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import { useWardrobeStore } from '@/stores/wardrobeStore.js';
import { useOutfitStore } from '@/stores/outfitStore.js';
import { handleAuthError } from '@/utils/navigation';
import SelectedItemsPreview from '@/components/outfit/SelectedItemsPreview.vue';
import ClothingSelector from './components/ClothingSelector.vue';

const wardrobeStore = useWardrobeStore();
const outfitStore = useOutfitStore();

const outfitForm = ref(null);
const isEditMode = ref(false);
const editingOutfitId = ref(null);
const isSaving = ref(false);

const outfitData = reactive({
  name: '',
  itemIds: [], // Array of selected clothing item IDs
  occasion: '',
  notes: ''
});

// 选中的衣物对象数组
const selectedClothingItems = ref([]);

const outfitFormRules = {
  name: { rules: [{ required: true, errorMessage: '请输入穿搭名称' }] },
  itemIds: {
    rules: [
      {
        type: 'array',
        min: 1,
        errorMessage: '请至少选择一件衣物',
        trigger: 'change'
      }
    ]
  }
};

// Get available items directly from store (reactive)
const availableItems = computed(() => wardrobeStore.wardrobeItems);

// 添加用于禁用按钮的计算属性
const isSubmitDisabled = computed(() => {
  return !(outfitData.name.trim() && selectedClothingItems.value.length > 0);
});

// 处理衣物选择变化
const handleSelectionChange = (items) => {
  selectedClothingItems.value = items;
  outfitData.itemIds = items.map(item => item.id);
};

// 处理移除单个衣物
const handleRemoveItem = (item) => {
  const newItems = selectedClothingItems.value.filter(i => i.id !== item.id);
  handleSelectionChange(newItems);
};

// 处理清空所有衣物
const handleClearAll = () => {
  handleSelectionChange([]);
};

// 处理点击衣物预览
const handleItemClick = (item) => {
  // 可以在这里添加额外的逻辑，比如显示详情等
  console.log('Item clicked:', item);
};

const goToAddItemPage = () => {
  uni.navigateTo({ url: '/pages/main/addItem/AddItemPage' });
};

onLoad(async options => {
  try {
    // Fetch all available clothing items from the wardrobe if not already loaded
    if (wardrobeStore.wardrobeItems.length === 0) {
      await wardrobeStore.fetchItems();
    }

    if (options.id) {
      // Edit mode
      isEditMode.value = true;
      editingOutfitId.value = parseInt(options.id);
      const outfitToEdit = outfitStore.getOutfitById(editingOutfitId.value);
      if (outfitToEdit) {
        outfitData.name = outfitToEdit.name;
        outfitData.itemIds = [...outfitToEdit.itemIds]; // Ensure reactivity
        outfitData.occasion = outfitToEdit.occasion;
        outfitData.notes = outfitToEdit.notes;
        
        // 根据itemIds找到对应的衣物对象
        const selectedItems = availableItems.value.filter(item => 
          outfitData.itemIds.includes(item.id)
        );
        selectedClothingItems.value = selectedItems;
        
        uni.setNavigationBarTitle({ title: '编辑穿搭' });
      } else {
        uni.showToast({ title: '无法加载穿搭信息', icon: 'none' });
        isEditMode.value = false;
        uni.setNavigationBarTitle({ title: '创建新穿搭' });
      }
    } else {
      // Create mode
      uni.setNavigationBarTitle({ title: '创建新穿搭' });
    }
  } catch (error) {
    console.error('Failed to load clothing items:', error);

    // Check if it's an authentication error
    if (error.statusCode === 401) {
      uni.showModal({
        title: '需要登录',
        content: '请先登录后再创建穿搭',
        showCancel: false,
        success: () => {
          handleAuthError('请先登录后再创建穿搭');
        }
      });
    } else {
      uni.showToast({
        title: '加载衣物失败，请重试',
        icon: 'none'
      });
    }
  }
});

const handleSubmitOutfit = async () => {
  try {
    await outfitForm.value.validate();

    isSaving.value = true;
    const dataToSave = JSON.parse(JSON.stringify(outfitData));

    let success = false;
    let errorMessage = '';

    try {
      if (isEditMode.value) {
        success = await outfitStore.updateOutfit(
          editingOutfitId.value,
          dataToSave
        );
      } else {
        success = await outfitStore.addOutfit(dataToSave);
      }
    } catch (error) {
      console.error('Outfit save error:', error);
      success = false;

      // Handle specific error types
      if (error.statusCode === 401) {
        errorMessage = '登录已过期，请重新登录';
        handleAuthError(errorMessage);
        return; // 不继续执行后续逻辑
      } else if (error.statusCode === 400) {
        errorMessage = error.message || '数据验证失败，请检查输入';
      } else if (error.statusCode === 403) {
        errorMessage = '权限不足，无法执行此操作';
      } else {
        errorMessage = error.message || '网络错误，请检查网络连接后重试';
      }
    }

    isSaving.value = false;

    if (success) {
      uni.showToast({
        title: isEditMode.value ? '穿搭更新成功！' : '穿搭保存成功！',
        icon: 'success'
      });
      
      // 触发数据同步通知（实时更新机制会自动处理）
      // outfitStore 的 createOutfit 和 updateOutfit 已经包含了通知机制
      
      // Navigate to MyOutfitsPage (to be created) or back
      // For now, navigate back as MyOutfitsPage doesn't exist yet.
      setTimeout(() => {
        uni.navigateBack();
        // Later: uni.redirectTo({ url: '/pages/outfits/MyOutfitsPage' });
      }, 1500);
    } else {
      uni.showToast({
        title: errorMessage || '操作失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  } catch (validationError) {
    // Form validation failed - user will see the validation errors
    console.log('Form validation failed:', validationError);
    isSaving.value = false;
  }
};

// Watch for changes in selected items
watch(
  () => selectedClothingItems.value,
  (newItems, oldItems) => {
    // Check for multiple pants
    const selectedPants = newItems.filter(
      item =>
        item.categoryTree &&
        item.categoryTree[0] === '裤子'
    );

    if (selectedPants.length >= 2) {
      uni.showToast({
        title: '您已选择了多条裤子，请确认是否需要',
        icon: 'none', // 使用 none 使其成为非阻塞式
        duration: 3000 // 提醒显示 3 秒
      });
    }

    // Check for multiple shoes
    const selectedShoes = newItems.filter(
      item =>
        item.categoryTree &&
        item.categoryTree[0] === '鞋子'
    );

    if (selectedShoes.length >= 2) {
      uni.showToast({
        title: '您已选择了多双鞋子，请确认是否需要',
        icon: 'none', // 使用 none 使其成为非阻塞式
        duration: 3000 // 提醒显示 3 秒
      });
    }

    // Add other smart reminder logic here in the future
    // Example: Check for multiple tops, or incompatible items
  },
  { deep: true }
);
</script>

<style scoped lang="scss">
.create-outfit-container {
  padding: 30rpx;
  background-color: #f9f9f9;
  min-height: 100vh;
}

.form-section {
  margin-bottom: 40rpx; // Consistent with AddItemPage.vue
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx; // Consistent with AddItemPage.vue
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

:deep(.uni-forms-item__label) {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-top: 15rpx; // Add margin to top of label
}

.outfit-selection-container {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.outfit-preview-section {
  background-color: #ffffff;
  border-radius: 12rpx;
  padding: 20rpx 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 20rpx;

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20rpx;
    padding-bottom: 16rpx;
    border-bottom: 1px solid #f0f0f0;

    .preview-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }

    .preview-count {
      font-size: 24rpx;
      color: #fda085;
      background-color: rgba(253, 160, 133, 0.1);
      padding: 6rpx 12rpx;
      border-radius: 12rpx;
    }
  }

  .outfit-preview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120rpx, 1fr));
    gap: 16rpx;

    .preview-outfit-item {
      text-align: center;

      .preview-outfit-image {
        width: 100%;
        height: 120rpx;
        border-radius: 8rpx;
        background-color: #f5f5f5;
        margin-bottom: 8rpx;
      }

      .preview-outfit-name {
        font-size: 22rpx;
        color: #666;
        display: block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        line-height: 1.3;
      }
    }
  }
}

.loading-notice {
  padding: 40rpx 0;
  text-align: center;
}

.no-items-notice {
  font-size: 28rpx;
  color: #777;
  padding: 20rpx 0;
  .link {
    color: #fda085;
    text-decoration: underline;
    cursor: pointer;
  }
}

.form-actions {
  margin-top: 40rpx;
  .submit-button {
    height: 90rpx;
    line-height: 90rpx;
    border-radius: 45rpx;
    font-size: 32rpx;
    font-weight: 500;
    background: linear-gradient(to right, #fda085, #f6d365);
    color: #fff;
    border: none;
    box-shadow: 0 10rpx 20rpx rgba(253, 160, 133, 0.3); // Consistent with AddItemPage.vue
  }
}

// Styling for list mode checkbox items (if needed for text part)
:deep(.uni-list-item__content-title) {
  font-size: 28rpx !important;
}
</style>
