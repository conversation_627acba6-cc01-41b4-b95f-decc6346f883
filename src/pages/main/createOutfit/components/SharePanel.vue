<!-- src/pages/main/createOutfit/components/SharePanel.vue -->
<template>
  <uni-popup ref="sharePopup" type="bottom" @change="handlePopupChange">
    <view class="share-panel">
      <view class="panel-header">
        <text class="title">分享搭配</text>
        <text class="close" @click="handleClose">✕</text>
      </view>

      <view class="share-options">
        <view class="share-item" @click="handleShare('friend')">
          <image
            class="share-icon"
            src="/static/icons/wechat.png"
            mode="aspectFit"
          />
          <text class="share-text">微信好友</text>
        </view>

        <view class="share-item" @click="handleShare('timeline')">
          <image
            class="share-icon"
            src="/static/icons/moments.png"
            mode="aspectFit"
          />
          <text class="share-text">朋友圈</text>
        </view>

        <view class="share-item" @click="handleShare('link')">
          <image
            class="share-icon"
            src="/static/icons/link.png"
            mode="aspectFit"
          />
          <text class="share-text">复制链接</text>
        </view>

        <view class="share-item" @click="handleShare('qrcode')">
          <image
            class="share-icon"
            src="/static/icons/qrcode.png"
            mode="aspectFit"
          />
          <text class="share-text">二维码</text>
        </view>
      </view>

      <button class="cancel-button" @click="handleClose">取消</button>
    </view>
  </uni-popup>
</template>

<script setup>
import { ref } from 'vue';

// Props
const props = defineProps({
  outfitData: {
    type: Object,
    required: true
  }
});

// Emits
const emit = defineEmits(['close']);

// Refs
const sharePopup = ref(null);

// Methods
const handleClose = () => {
  sharePopup.value?.close();
};

const handlePopupChange = e => {
  if (!e.show) {
    emit('close');
  }
};

const handleShare = async type => {
  try {
    switch (type) {
      case 'friend':
        // #ifdef MP-WEIXIN
        uni.showShareMenu({
          withShareTicket: true,
          menus: ['shareAppMessage']
        });
        // #endif
        break;

      case 'timeline':
        // #ifdef MP-WEIXIN
        uni.showShareMenu({
          withShareTicket: true,
          menus: ['shareTimeline']
        });
        // #endif
        break;

      case 'link':
        // 生成分享链接
        const shareLink = `https://your-domain.com/outfit/${props.outfitData.id}`;
        await uni.setClipboardData({
          data: shareLink
        });
        uni.showToast({
          title: '链接已复制',
          icon: 'success'
        });
        break;

      case 'qrcode':
        // TODO: 生成分享二维码
        uni.showToast({
          title: '二维码生成中...',
          icon: 'none'
        });
        break;
    }

    handleClose();
  } catch (err) {
    uni.showToast({
      title: '分享失败',
      icon: 'error'
    });
  }
};

// 暴露方法给父组件
defineExpose({
  open: () => sharePopup.value?.open()
});
</script>

<style lang="scss">
.share-panel {
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  padding: 30rpx;

  .panel-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 40rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
    }

    .close {
      font-size: 40rpx;
      color: #999;
      padding: 10rpx;
    }
  }

  .share-options {
    display: flex;
    flex-wrap: wrap;
    padding: 20rpx 0;

    .share-item {
      width: 25%;
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-bottom: 30rpx;

      .share-icon {
        width: 100rpx;
        height: 100rpx;
        margin-bottom: 16rpx;
      }

      .share-text {
        font-size: 24rpx;
        color: #666;
      }
    }
  }

  .cancel-button {
    width: 100%;
    height: 88rpx;
    line-height: 88rpx;
    background: #f5f5f5;
    color: #666;
    border-radius: 44rpx;
    margin-top: 20rpx;
    font-size: 32rpx;
  }
}
</style>
