<!-- src/pages/main/createOutfit/components/ClothingSelector.vue -->
<template>
  <view class="clothing-selector">
    <!-- 选择器弹出层 -->
    <uni-popup ref="selectorPopup" type="bottom" @change="handlePopupChange">
      <view class="selector-content">
        <view class="selector-header">
          <text class="selector-title">选择衣物</text>
          <text class="selector-close" @click="handleClose">✕</text>
        </view>

        <!-- 分类筛选 -->
        <scroll-view scroll-x class="category-filter" show-scrollbar="false">
          <view class="filter-tabs">
            <view
              v-for="(category, key) in wardrobeStore.categorySystem"
              :key="key"
              class="filter-tab"
              :class="{ active: currentCategory === key }"
              @click="handleCategoryChange(key)"
            >
              {{ category.name }}
            </view>
          </view>
        </scroll-view>

        <!-- 子分类筛选 -->
        <scroll-view
          v-if="currentSubcategories.length"
          scroll-x
          class="subcategory-filter"
          show-scrollbar="false"
        >
          <view class="filter-tabs">
            <view
              v-for="subcategory in currentSubcategories"
              :key="subcategory.key"
              class="filter-tab"
              :class="{ active: currentSubcategory === subcategory.key }"
              @click="handleSubcategoryChange(subcategory.key)"
            >
              {{ subcategory.name }}
            </view>
          </view>
        </scroll-view>

        <!-- 搜索框 -->
        <view class="search-box">
          <uni-easyinput
            v-model="searchKeyword"
            placeholder="搜索衣物..."
            :clearable="true"
            @input="handleSearch"
          />
        </view>

        <!-- 衣物列表 -->
        <scroll-view class="clothing-list" scroll-y>
          <!-- 加载状态 -->
          <view v-if="wardrobeStore.isLoading" class="loading-state">
            <uni-load-more
              status="loading"
              content-text="正在加载衣物..."
            ></uni-load-more>
          </view>

          <!-- 空状态 -->
          <view
            v-else-if="filteredClothingItems.length === 0"
            class="empty-state"
          >
            <text class="empty-text">暂无衣物</text>
            <text class="empty-subtext">请先添加一些衣物到衣橱</text>
          </view>

          <!-- 衣物网格 -->
          <view v-else class="clothing-grid">
            <view
              v-for="item in filteredClothingItems"
              :key="item.id"
              class="clothing-item"
              :class="{ selected: isSelected(item.id) }"
              @click="toggleSelect(item)"
              @longpress="showItemPreview(item)"
            >
              <view class="item-image-container">
                <image
                  class="item-image"
                  :src="
                    item.imageUrls && item.imageUrls[0]
                      ? item.imageUrls[0]
                      : '/static/images/empty-collection.png'
                  "
                  mode="aspectFill"
                  @error="handleImageError"
                />
                <view v-if="isSelected(item.id)" class="select-indicator">
                  <uni-icons type="checkmarkempty" size="16" color="#fff" />
                </view>
                <view class="item-preview-btn" @click.stop="showItemPreview(item)">
                  <uni-icons type="eye" size="14" color="#fff" />
                </view>
              </view>
              <view class="item-info">
                <text class="item-name">{{ item.name }}</text>
                <text class="item-category">{{
                  item.categoryDisplay || item.category || '未分类'
                }}</text>
              </view>
            </view>
          </view>
        </scroll-view>

        <!-- 底部操作栏 -->
        <view class="selector-footer">
          <button class="cancel-button" @click="handleClose">取消</button>
          <button class="confirm-button" type="primary" @click="handleConfirm">
            确定 ({{ selectedItems.length }})
          </button>
        </view>
      </view>
    </uni-popup>

    <!-- 已选衣物展示区 -->
    <scroll-view class="selected-preview" scroll-x>
      <view class="preview-list">
        <view v-for="item in selectedItems" :key="item.id" class="preview-item">
          <image
            class="preview-image"
            :src="item.imageUrls[0]"
            mode="aspectFill"
          />
          <view class="remove-button" @click.stop="removeItem(item)">×</view>
        </view>
        <view v-if="!disabled" class="add-button" @click="showSelector">
          <text class="add-icon">+</text>
          <text class="add-text">添加衣物</text>
        </view>
      </view>
    </scroll-view>

    <!-- 搭配提示弹窗 -->
    <uni-popup ref="outfitTipsPopup" type="dialog">
      <uni-popup-dialog
        type="warning"
        title="搭配提示"
        :content="outfitTips"
        :before-close="true"
        @confirm="handleOutfitTipsConfirm"
        @close="handleOutfitTipsCancel"
      />
    </uni-popup>

    <!-- 衣物预览弹窗 -->
    <uni-popup ref="itemPreviewPopup" type="center">
      <view class="item-preview-modal">
        <view class="preview-modal-header">
          <text class="preview-modal-title">{{ previewItem?.name }}</text>
          <view class="preview-modal-close" @click="closeItemPreview">
            <uni-icons type="clear" size="20" color="#666" />
          </view>
        </view>
        <view class="preview-modal-content">
          <image 
            class="preview-large-image" 
            :src="getPreviewImage(previewItem)" 
            mode="aspectFit"
          />
          <view class="preview-item-details">
            <text class="preview-item-name">{{ previewItem?.name }}</text>
            <text class="preview-item-category">{{ getPreviewCategory(previewItem) }}</text>
            <text class="preview-item-description" v-if="previewItem?.description">
              {{ previewItem.description }}
            </text>
          </view>
        </view>
        <view class="preview-modal-actions">
          <button 
            class="select-item-btn" 
            :class="{ selected: isSelected(previewItem?.id) }"
            @click="toggleSelectFromPreview"
          >
            {{ isSelected(previewItem?.id) ? '取消选择' : '选择此衣物' }}
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useWardrobeStore } from '@/stores/wardrobeStore';

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'change']);

// Store
const wardrobeStore = useWardrobeStore();

// Refs
const selectorPopup = ref(null);
const outfitTipsPopup = ref(null);
const itemPreviewPopup = ref(null);
const searchKeyword = ref('');
const currentCategory = ref('');
const currentSubcategory = ref('');
const outfitTips = ref('');
const previewItem = ref(null);

// 计算属性
const selectedItems = computed(() => {
  return props.modelValue;
});

const currentSubcategories = computed(() => {
  if (
    !currentCategory.value ||
    !wardrobeStore.categorySystem[currentCategory.value]
  ) {
    return [];
  }

  const subcategories =
    wardrobeStore.categorySystem[currentCategory.value].subcategories;
  return Object.entries(subcategories).map(([key, value]) => ({
    key,
    name: value.name
  }));
});

const filteredClothingItems = computed(() => {
  let items = wardrobeStore.wardrobeItems;

  // 分类筛选
  if (currentCategory.value) {
    items = items.filter(item => item.category === currentCategory.value);

    if (currentSubcategory.value) {
      items = items.filter(
        item => item.subcategory === currentSubcategory.value
      );
    }
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase();
    items = items.filter(
      item =>
        item.name.toLowerCase().includes(keyword) ||
        item.categoryDisplay?.toLowerCase().includes(keyword)
    );
  }

  return items;
});

// 方法
const showSelector = () => {
  if (!props.disabled) {
    selectorPopup.value?.open();
  }
};

const handleClose = () => {
  selectorPopup.value?.close();
  searchKeyword.value = '';
  currentCategory.value = '';
  currentSubcategory.value = '';
};

const handlePopupChange = e => {
  if (!e.show) {
    searchKeyword.value = '';
    currentCategory.value = '';
    currentSubcategory.value = '';
  }
};

const handleSearch = value => {
  searchKeyword.value = value;
};

const handleCategoryChange = category => {
  currentCategory.value = category;
  currentSubcategory.value = '';
};

const handleSubcategoryChange = subcategory => {
  currentSubcategory.value = subcategory;
};

const handleImageError = e => {
  e.target.src = '/static/default-clothing.png';
};

const isSelected = itemId => {
  return selectedItems.value.some(item => item.id === itemId);
};

const checkOutfitCompatibility = items => {
  try {
    const warnings = [];

    // 检查 wardrobeStore 方法是否可用
    if (typeof wardrobeStore.isItemInCategory !== 'function') {
      console.error('wardrobeStore.isItemInCategory is not available');
      return [];
    }

    // 检查裤子和裙子/连衣裙同时存在
    const hasSkirt = items.some(
      item =>
        wardrobeStore.isItemInCategory(item, 'bottoms', 'skirts') ||
        wardrobeStore.isItemInCategory(item, 'dresses')
    );
    const hasPants = items.some(item =>
      wardrobeStore.isItemInCategory(item, 'bottoms', 'pants')
    );

    if (hasSkirt && hasPants) {
      warnings.push('当前搭配同时包含裙装和裤子，这可能不太合适。是否继续？');
    }

    // 检查多个外套
    const outerwearCount = items.filter(item =>
      wardrobeStore.isItemInCategory(item, 'tops', 'outerwear')
    ).length;

    if (outerwearCount > 1) {
      warnings.push(
        `当前搭配包含${outerwearCount}件外套，这可能会显得太臃肿。是否继续？`
      );
    }

    // 检查基本搭配是否完整
    const hasTop = items.some(item =>
      wardrobeStore.isItemInCategory(item, 'tops')
    );
    const hasBottom = items.some(
      item =>
        wardrobeStore.isItemInCategory(item, 'bottoms') ||
        wardrobeStore.isItemInCategory(item, 'dresses')
    );

    if (items.length > 1 && !hasTop && !hasBottom) {
      warnings.push('当前搭配缺少上装或下装，可能不完整。是否继续？');
    }

    // 检查颜色系统搭配
    try {
      const allColors = items.reduce((colors, item) => {
        if (item.colorTags && item.colorTags.length > 0) {
          colors.push(...item.colorTags);
        }
        return colors;
      }, []);

      const harmonyScore = wardrobeStore.getColorHarmonyScore(allColors);
      if (harmonyScore <= 0.4) {
        warnings.push('当前搭配的色系组合可能不够协调。是否继续？');
      }
    } catch (error) {
      console.error('检查颜色系统时出错:', error);
    }

    // 检查场景搭配
    for (let i = 0; i < items.length; i++) {
      for (let j = i + 1; j < items.length; j++) {
        if (!wardrobeStore.areItemsOccasionCompatible(items[i], items[j])) {
          warnings.push(
            '当前搭配中的部分单品场合不匹配，可能不适合一起穿着。是否继续？'
          );
          break;
        }
      }
    }

    // 检查季节适配性
    const seasons = new Set();
    items.forEach(item => {
      if (item.seasonTags) {
        item.seasonTags.forEach(season => seasons.add(season));
      }
    });

    if (seasons.size > 2) {
      warnings.push('当前搭配包含跨季节的单品，可能不适合同时穿着。是否继续？');
    }

    return warnings;
  } catch (error) {
    console.error('检查搭配合理性时出错:', error);
    return ['搭配检查过程中出现错误，请稍后重试'];
  }
};

const toggleSelect = async item => {
  const currentSelected = [...selectedItems.value];
  const index = currentSelected.findIndex(i => i.id === item.id);

  if (index === -1) {
    // 添加新项目时检查搭配合理性
    const newItems = [...currentSelected, item];
    const warnings = checkOutfitCompatibility(newItems);

    if (warnings.length > 0) {
      outfitTips.value = warnings.join('\n\n');
      outfitTipsPopup.value.open();
      return;
    }

    currentSelected.push(item);
  } else {
    currentSelected.splice(index, 1);
  }

  emit('update:modelValue', currentSelected);
  emit('change', currentSelected);
};

const removeItem = item => {
  const currentSelected = selectedItems.value.filter(i => i.id !== item.id);
  emit('update:modelValue', currentSelected);
  emit('change', currentSelected);
};

const handleConfirm = () => {
  handleClose();
};

const handleOutfitTipsConfirm = () => {
  const item = filteredClothingItems.value.find(i => !isSelected(i.id));
  if (item) {
    const currentSelected = [...selectedItems.value, item];
    emit('update:modelValue', currentSelected);
    emit('change', currentSelected);
  }
  outfitTipsPopup.value.close();
};

const handleOutfitTipsCancel = () => {
  outfitTipsPopup.value.close();
};

// 预览相关方法
const showItemPreview = (item) => {
  previewItem.value = item;
  itemPreviewPopup.value?.open();
};

const closeItemPreview = () => {
  itemPreviewPopup.value?.close();
  previewItem.value = null;
};

const getPreviewImage = (item) => {
  if (item?.imageUrls && item.imageUrls.length > 0) {
    return item.imageUrls[0];
  }
  return '/static/images/empty-collection.png';
};

const getPreviewCategory = (item) => {
  return item?.categoryDisplay || item?.category || '未分类';
};

const toggleSelectFromPreview = () => {
  if (previewItem.value) {
    toggleSelect(previewItem.value);
  }
};
</script>

<style lang="scss">
.clothing-selector {
  width: 100%;

  .selector-content {
    background-color: #fff;
    border-radius: 20rpx 20rpx 0 0;
    height: 80vh;
    display: flex;
    flex-direction: column;
    position: relative;

    .selector-header {
      padding: 30rpx;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-bottom: 1px solid #eee;
      position: sticky;
      top: 0;
      background: #fff;
      z-index: 1;

      .selector-title {
        font-size: 32rpx;
        font-weight: bold;
      }

      .selector-close {
        font-size: 40rpx;
        color: #999;
        padding: 10rpx;
      }
    }

    .category-filter,
    .subcategory-filter {
      background-color: #fff;
      padding: 20rpx 30rpx;
      position: sticky;
      z-index: 1;

      .filter-tabs {
        display: flex;
        white-space: nowrap;

        .filter-tab {
          padding: 12rpx 30rpx;
          margin-right: 20rpx;
          background-color: #f5f5f5;
          border-radius: 30rpx;
          font-size: 26rpx;
          color: #666;
          transition: all 0.3s ease;

          &.active {
            background: linear-gradient(to right, #fda085, #f6d365);
            color: #fff;
          }

          &:last-child {
            margin-right: 0;
          }
        }
      }
    }

    .category-filter {
      top: 90rpx;
      border-bottom: 1px solid #eee;
    }

    .subcategory-filter {
      top: 170rpx;
      border-bottom: 1px solid #eee;
    }

    .search-box {
      padding: 20rpx 30rpx;
      position: sticky;
      top: 250rpx;
      background: #fff;
      z-index: 1;
      border-bottom: 1px solid #eee;
    }

    .clothing-list {
      flex: 1;
      padding: 0 20rpx;
      overflow-y: auto;

      .clothing-grid {
        display: flex;
        flex-wrap: wrap;
        padding: 10rpx;

        .clothing-item {
          width: calc(50% - 20rpx);
          margin: 10rpx;
          background: #fff;
          border-radius: 12rpx;
          overflow: hidden;
          position: relative;
          border: 1px solid #eee;
          transition: all 0.3s ease;

          &.selected {
            border-color: #fda085;
            background-color: rgba(253, 160, 133, 0.1);
            transform: scale(0.98);
          }

          &:active {
            transform: scale(0.95);
          }

          .item-image-container {
            position: relative;
            width: 100%;
            height: 300rpx;
            background-color: #f5f5f5;

            .item-image {
              width: 100%;
              height: 100%;
            }

            .select-indicator {
              position: absolute;
              top: 10rpx;
              right: 10rpx;
              width: 40rpx;
              height: 40rpx;
              background: #fda085;
              color: #fff;
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              box-shadow: 0 2rpx 8rpx rgba(253, 160, 133, 0.4);
            }

            .item-preview-btn {
              position: absolute;
              bottom: 10rpx;
              right: 10rpx;
              width: 36rpx;
              height: 36rpx;
              background: rgba(0, 0, 0, 0.6);
              border-radius: 50%;
              display: flex;
              align-items: center;
              justify-content: center;
              opacity: 0.8;
              transition: opacity 0.3s ease;

              &:active {
                opacity: 1;
                transform: scale(0.9);
              }
            }
          }

          .item-info {
            padding: 16rpx;

            .item-name {
              font-size: 28rpx;
              color: #333;
              display: block;
              font-weight: 500;
              line-height: 1.3;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            .item-category {
              font-size: 24rpx;
              color: #999;
              margin-top: 8rpx;
              display: block;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }

    .selector-footer {
      padding: 20rpx 30rpx;
      display: flex;
      justify-content: space-between;
      gap: 20rpx;
      border-top: 1px solid #eee;
      position: sticky;
      bottom: 0;
      background: #fff;
      z-index: 1;

      button {
        flex: 1;
        height: 80rpx;
        line-height: 80rpx;
        border-radius: 40rpx;
        font-size: 28rpx;
        margin: 0;

        &.cancel-button {
          background: #f5f5f5;
          color: #666;
        }

        &.confirm-button {
          background: linear-gradient(to right, #fda085, #f6d365);
          color: #fff;
        }
      }
    }
  }

  .selected-preview {
    width: 100%;
    white-space: nowrap;
    padding: 20rpx 0;

    .preview-list {
      display: inline-flex;
      padding: 0 20rpx;

      .preview-item {
        position: relative;
        margin-right: 20rpx;

        .preview-image {
          width: 160rpx;
          height: 160rpx;
          border-radius: 12rpx;
          background-color: #f5f5f5;
        }

        .remove-button {
          position: absolute;
          top: -10rpx;
          right: -10rpx;
          width: 40rpx;
          height: 40rpx;
          background: rgba(0, 0, 0, 0.5);
          color: #fff;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24rpx;
        }
      }

      .add-button {
        width: 160rpx;
        height: 160rpx;
        border: 2rpx dashed #ddd;
        border-radius: 12rpx;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;

        .add-icon {
          font-size: 48rpx;
          color: #999;
          line-height: 1;
        }

        .add-text {
          font-size: 24rpx;
          color: #999;
          margin-top: 10rpx;
        }
      }
    }
  }

  .loading-state {
    padding: 60rpx 0;
    text-align: center;
  }

  .empty-state {
    padding: 60rpx 0;
    text-align: center;

    .empty-text {
      display: block;
      font-size: 32rpx;
      color: #666;
      margin-bottom: 20rpx;
    }

    .empty-subtext {
      display: block;
      font-size: 28rpx;
      color: #999;
    }
  }
}

// 衣物预览弹窗样式
.item-preview-modal {
  width: 90vw;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;

  .preview-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .preview-modal-title {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 20rpx;
    }

    .preview-modal-close {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: #f5f5f5;
    }
  }

  .preview-modal-content {
    padding: 30rpx;

    .preview-large-image {
      width: 100%;
      height: 400rpx;
      border-radius: 12rpx;
      background-color: #f5f5f5;
      margin-bottom: 24rpx;
    }

    .preview-item-details {
      .preview-item-name {
        display: block;
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
      }

      .preview-item-category {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 16rpx;
      }

      .preview-item-description {
        display: block;
        font-size: 24rpx;
        color: #999;
        line-height: 1.5;
      }
    }
  }

  .preview-modal-actions {
    padding: 20rpx 30rpx 30rpx;

    .select-item-btn {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      background: linear-gradient(to right, #fda085, #f6d365);
      color: #fff;
      border: none;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-weight: 500;
      transition: all 0.3s ease;

      &.selected {
        background: #ff4757;
      }

      &:active {
        transform: scale(0.98);
      }
    }
  }
}
</style>
