<template>
  <view class="recent-outfits">
    <view class="section-header">
      <text class="title">最近搭配</text>
      <text class="subtitle">快速复用已创建的搭配</text>
    </view>

    <scroll-view class="outfits-list" scroll-x>
      <view class="outfits-wrapper">
        <view
          v-for="outfit in recentOutfits"
          :key="outfit.id"
          class="outfit-card"
          @click="handleOutfitSelect(outfit)"
        >
          <view class="images-preview">
            <image
              v-for="(item, index) in outfit.clothingItems.slice(0, 4)"
              :key="item.id"
              :class="['preview-image', `position-${index}`]"
              :src="item.imageUrl"
              mode="aspectFill"
            />
          </view>

          <view class="outfit-info">
            <text class="outfit-name">{{ outfit.name }}</text>
            <text class="item-count"
              >{{ outfit.clothingItems.length }}件搭配</text
            >
          </view>

          <view v-if="outfit.tags && outfit.tags.length" class="outfit-tags">
            <uni-tag
              v-for="tag in outfit.tags.slice(0, 2)"
              :key="tag"
              :text="tag"
              type="primary"
              size="small"
            />
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</template>

<script setup>
import { computed } from 'vue';
import { useOutfitStore } from '@/stores/outfitStore';

// Store
const outfitStore = useOutfitStore();

// Props
const props = defineProps({
  limit: {
    type: Number,
    default: 10
  }
});

// Emits
const emit = defineEmits(['select']);

// 计算属性
const recentOutfits = computed(() => {
  return outfitStore.outfits
    .slice()
    .sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
    .slice(0, props.limit);
});

// 方法
const handleOutfitSelect = outfit => {
  emit('select', outfit);
};
</script>

<style lang="scss">
.recent-outfits {
  padding: 20rpx 0;

  .section-header {
    padding: 0 30rpx;
    margin-bottom: 20rpx;

    .title {
      font-size: 32rpx;
      font-weight: bold;
      color: #333;
      display: block;
    }

    .subtitle {
      font-size: 24rpx;
      color: #999;
      margin-top: 4rpx;
      display: block;
    }
  }

  .outfits-list {
    width: 100%;
    white-space: nowrap;

    .outfits-wrapper {
      display: inline-flex;
      padding: 0 20rpx;

      .outfit-card {
        width: 240rpx;
        background: #fff;
        border-radius: 12rpx;
        margin-right: 20rpx;
        padding: 16rpx;
        box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);

        .images-preview {
          width: 100%;
          height: 240rpx;
          position: relative;
          margin-bottom: 16rpx;

          .preview-image {
            width: 120rpx;
            height: 120rpx;
            border-radius: 8rpx;
            position: absolute;

            &.position-0 {
              top: 0;
              left: 0;
            }

            &.position-1 {
              top: 0;
              right: 0;
            }

            &.position-2 {
              bottom: 0;
              left: 0;
            }

            &.position-3 {
              bottom: 0;
              right: 0;
            }
          }
        }

        .outfit-info {
          .outfit-name {
            font-size: 28rpx;
            color: #333;
            display: block;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
          }

          .item-count {
            font-size: 24rpx;
            color: #999;
            margin-top: 4rpx;
            display: block;
          }
        }

        .outfit-tags {
          margin-top: 12rpx;
          display: flex;
          gap: 8rpx;
          flex-wrap: wrap;
        }
      }
    }
  }
}
</style>
