<template>
  <view class="add-item-container">
    <view class="header">
      <view class="title-section">
        <text class="title">{{ pageTitle }}</text>
      </view>
    </view>

    <scroll-view scroll-y class="form-scroll-view">
      <view class="form-container">
        <view class="photo-upload-section">
          <view class="section-title">衣物照片</view>
          <image-uploader
            ref="imageUploader"
            :value="clothingPhotos"
            :max-count="5"
            tip-text="添加衣物的正面照片，最多5张"
            @update:value="clothingPhotos = $event"
          />
        </view>

        <view class="info-section">
          <view class="section-title">基本信息</view>

          <view class="form-item">
            <text class="form-label required">衣物名称</text>
            <uni-easyinput
              v-model="formData.name"
              placeholder="请输入衣物名称"
              :styles="inputStyles"
              :input-border="false"
            ></uni-easyinput>
          </view>

          <view class="form-item">
            <text class="form-label required">主分类</text>
            <picker
              mode="selector"
              :range="mainCategoryOptions"
              range-key="label"
              class="form-picker"
              @change="onMainCategoryChange"
            >
              <view class="picker-value">
                {{
                  formData.categoryKey
                    ? mainCategoryOptions.find(
                        opt => opt.value === formData.categoryKey
                      )?.label
                    : '请选择主分类'
                }}
                <uni-icons type="arrowdown" size="16" color="#999"></uni-icons>
              </view>
            </picker>
          </view>

          <view
            v-if="formData.categoryKey && subCategoryOptions.length > 0"
            class="form-item"
          >
            <text class="form-label required">子分类</text>
            <picker
              mode="selector"
              :range="subCategoryOptions"
              range-key="label"
              class="form-picker"
              @change="onSubCategoryChange"
            >
              <view class="picker-value">
                {{
                  formData.subcategoryKey
                    ? subCategoryOptions.find(
                        opt => opt.value === formData.subcategoryKey
                      )?.label
                    : '请选择子分类'
                }}
                <uni-icons type="arrowdown" size="16" color="#999"></uni-icons>
              </view>
            </picker>
          </view>

          <view
            v-if="formData.subcategoryKey && typeOptions.length > 0"
            class="form-item"
          >
            <text class="form-label required">具体类型</text>
            <picker
              mode="selector"
              :range="typeOptions"
              range-key="label"
              class="form-picker"
              @change="onTypeChange"
            >
              <view class="picker-value">
                {{ formData.typeValue ? formData.typeValue : '请选择具体类型' }}
                <uni-icons type="arrowdown" size="16" color="#999"></uni-icons>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">品牌</text>
            <uni-easyinput
              v-model="formData.brand"
              placeholder="请输入品牌名称"
              :styles="inputStyles"
              :input-border="false"
            ></uni-easyinput>
          </view>

          <view class="form-item">
            <text class="form-label required">颜色</text>
            <picker
              mode="selector"
              :range="allColorOptions"
              range-key="label"
              class="form-picker"
              @change="onColorChange"
            >
              <view class="picker-value">
                {{
                  formData.colorValue
                    ? allColorOptions.find(
                        opt => opt.value === formData.colorValue
                      )?.label
                    : '请选择颜色'
                }}
                <uni-icons type="arrowdown" size="16" color="#999"></uni-icons>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label required">尺码</text>
            <uni-easyinput
              v-model="formData.size"
              placeholder="请输入尺码"
              :styles="inputStyles"
              :input-border="false"
            ></uni-easyinput>
          </view>

          <view class="form-item">
            <text class="form-label required">季节</text>
            <view class="checkbox-group">
              <label
                v-for="season in seasonOptions"
                :key="season.value"
                class="checkbox-item"
              >
                <checkbox
                  :value="season.value"
                  :checked="formData.seasons.includes(season.value)"
                  @click="handleSeasonChange(season.value)"
                />
                <text>{{ season.label }}</text>
              </label>
            </view>
          </view>

          <view class="form-item">
            <text class="form-label">购买日期</text>
            <picker
              mode="date"
              :value="formData.purchaseDate"
              class="form-picker"
              @change="onDateChange"
            >
              <view class="picker-value">
                {{
                  formData.purchaseDate
                    ? formData.purchaseDate
                    : '请选择购买日期'
                }}
                <uni-icons type="calendar" size="16" color="#999"></uni-icons>
              </view>
            </picker>
          </view>

          <view class="form-item">
            <text class="form-label">价格</text>
            <uni-easyinput
              v-model="formData.price"
              type="number"
              placeholder="请输入价格"
              :styles="inputStyles"
              :input-border="false"
            ></uni-easyinput>
          </view>
        </view>

        <view class="notes-section">
          <view class="section-title">备注信息</view>
          <uni-easyinput
            v-model="formData.description"
            type="textarea"
            placeholder="添加关于这件衣物的备注（可选）"
            :styles="textareaStyles"
            :input-border="false"
            class="notes-input"
          ></uni-easyinput>
        </view>

        <view class="tags-section">
          <view class="section-title">标签</view>
          <view class="tags-input-container">
            <uni-easyinput
              v-model="tagInput"
              placeholder="输入标签，按回车添加"
              :styles="inputStyles"
              :input-border="false"
              class="tag-input"
              @confirm="addTag"
            ></uni-easyinput>
            <button class="add-tag-button" @click="addTag">添加</button>
          </view>
          <view v-if="formData.tags.length > 0" class="tags-container">
            <view
              v-for="(tag, index) in formData.tags"
              :key="index"
              class="tag"
            >
              {{ tag }}
              <text class="remove-tag" @click="removeTag(index)">×</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>

    <view class="bottom-actions">
      <button class="save-button" :disabled="!canSave" @click="saveItem">
        {{
          editingItemId
            ? BUTTON_TEXTS.SAVE_CHANGES || '保存更改'
            : BUTTON_TEXTS.SAVE_ITEM || '保存衣物'
        }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, computed, watch } from 'vue';
import { onLoad } from '@dcloudio/uni-app';
import uniEasyinput from '@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
import { useWardrobeStore } from '@/stores/wardrobeStore.js';
import { BUTTON_TEXTS, TOAST_TITLES } from '@/constants/strings.js';
import { uploadApi } from '@/api/upload.js';
import ImageUploader from '@/components/common/ImageUploader.vue';

const wardrobeStore = useWardrobeStore();
const editingItemId = ref(null);
const pageTitle = ref(BUTTON_TEXTS.ADD_NEW_ITEM || '添加新衣物');
const imageUploader = ref(null); // 图片上传组件引用

// 表单数据
const formData = reactive({
  name: '',
  categoryKey: '', // e.g., 'tops'
  subcategoryKey: '', // e.g., 'outerwear'
  typeValue: '', // e.g., '夹克'
  brand: '',
  colorValue: '', // Specific color string e.g., 'black', 'beige'
  size: '',
  seasons: [], // Changed to array for multi-select
  purchaseDate: '',
  price: '',
  description: '',
  tags: []
});

// 照片
const clothingPhotos = ref([]);
const tagInput = ref('');

// 样式配置
const inputStyles = {
  borderColor: '#e0e0e0',
  color: '#333333',
  backgroundColor: '#f9f9f9',
  borderRadius: '12rpx'
};

const textareaStyles = {
  ...inputStyles,
  height: '200rpx'
};

// 选项数据
const mainCategoryOptions = computed(() =>
  Object.entries(wardrobeStore.categorySystem).map(([key, value]) => ({
    value: key,
    label: value.name
  }))
);
const subCategoryOptions = ref([]);
const typeOptions = ref([]);

// 颜色中英文映射
const colorNameMap = {
  black: '黑色',
  white: '白色',
  gray: '灰色',
  red: '红色',
  blue: '蓝色',
  green: '绿色',
  yellow: '黄色',
  purple: '紫色',
  pink: '粉色',
  orange: '橙色',
  brown: '棕色',
  beige: '米色',
  navy: '藏青色',
  cream: '奶油色',
  camel: '驼色',
  charcoal: '炭灰色',
  coral: '珊瑚色',
  burgundy: '酒红色',
  khaki: '卡其色',
  olive: '橄榄绿',
  'pastel-blue': '淡蓝色',
  'pastel-green': '淡绿色',
  'pastel-pink': '淡粉色',
  'pastel-yellow': '淡黄色',
  silver: '银色',
  gold: '金色',
  metallic: '金属色',
  multicolor: '多彩'
};

const allColorOptions = computed(() => {
  const colors = [];
  for (const systemKey in wardrobeStore.colorSystems) {
    wardrobeStore.colorSystems[systemKey].colors.forEach(colorName => {
      if (!colors.some(c => c.value === colorName)) {
        colors.push({
          value: colorName,
          label: colorNameMap[colorName] || colorName // 如果没有对应的中文名，使用原始名称
        });
      }
    });
  }
  return colors.sort((a, b) => a.label.localeCompare(b.label, 'zh-CN')); // 使用中文排序
});

const seasonOptions = ref([
  { value: 'spring', label: '春季' },
  { value: 'summer', label: '夏季' },
  { value: 'autumn', label: '秋季' },
  { value: 'winter', label: '冬季' }
]);

// 计算属性：是否可以保存
const canSave = computed(() => {
  let isCategoryValid = !!formData.categoryKey;
  if (
    formData.categoryKey &&
    wardrobeStore.categorySystem[formData.categoryKey] &&
    Object.keys(
      wardrobeStore.categorySystem[formData.categoryKey]?.subcategories || {}
    ).length > 0
  ) {
    isCategoryValid = isCategoryValid && !!formData.subcategoryKey;
    if (
      formData.subcategoryKey &&
      wardrobeStore.categorySystem[formData.categoryKey]?.subcategories[
        formData.subcategoryKey
      ] &&
      (
        wardrobeStore.categorySystem[formData.categoryKey]?.subcategories[
          formData.subcategoryKey
        ]?.types || []
      ).length > 0
    ) {
      isCategoryValid = isCategoryValid && !!formData.typeValue;
    }
  }
  // 添加对颜色的检查，确保颜色不为空
  const isColorValid = !!formData.colorValue;

  // 在调试模式下，可以启用以下代码查看验证状态
  //     name: formData.name,
  //     categoryKey: formData.categoryKey,
  //     subcategoryKey: formData.subcategoryKey,
  //     typeValue: formData.typeValue,
  //     colorValue: formData.colorValue,
  //     isColorValid,
  //     photosCount: clothingPhotos.value.length,
  //     isCategoryValid: isCategoryValid,
  //     canSave: formData.name && isCategoryValid && isColorValid && clothingPhotos.value.length > 0,
  //     editing: !!editingItemId.value
  // });

  return (
    formData.name &&
    isCategoryValid &&
    isColorValid &&
    clothingPhotos.value.length > 0
  );
});

// 选择器变更事件处理
const onMainCategoryChange = e => {
  const selectedIndex = e.detail.value;
  formData.categoryKey = mainCategoryOptions.value[selectedIndex].value;
  formData.subcategoryKey = ''; // Reset
  formData.typeValue = ''; // Reset

  const mainCat = wardrobeStore.categorySystem[formData.categoryKey];
  if (mainCat && mainCat.subcategories) {
    subCategoryOptions.value = Object.entries(mainCat.subcategories).map(
      ([key, value]) => ({ value: key, label: value.name })
    );
  } else {
    subCategoryOptions.value = [];
  }
  typeOptions.value = []; // Clear type options
};

const onSubCategoryChange = e => {
  const selectedIndex = e.detail.value;
  formData.subcategoryKey = subCategoryOptions.value[selectedIndex].value;
  formData.typeValue = ''; // Reset

  const mainCat = wardrobeStore.categorySystem[formData.categoryKey];
  const subCat = mainCat?.subcategories[formData.subcategoryKey];
  if (subCat && subCat.types) {
    typeOptions.value = subCat.types.map(type => ({
      value: type,
      label: type
    }));
  } else {
    typeOptions.value = [];
  }
};

const onTypeChange = e => {
  const selectedIndex = e.detail.value;
  formData.typeValue = typeOptions.value[selectedIndex].value;
};

const onColorChange = e => {
  const selectedIndex = e.detail.value;
  formData.colorValue = allColorOptions.value[selectedIndex].value;
};

const handleSeasonChange = seasonValue => {
  const index = formData.seasons.indexOf(seasonValue);
  if (index === -1) {
    formData.seasons.push(seasonValue);
  } else {
    formData.seasons.splice(index, 1);
  }
};

const onDateChange = e => {
  formData.purchaseDate = e.detail.value;
};

const populateFormForEdit = item => {
  formData.name = item.name;
  formData.categoryKey = item.category || '';
  // Note: The order of setting categoryKey, then subcategoryKey, then typeValue,
  // and updating options in between, is crucial. This will be handled by watchers or explicit calls.

  formData.brand = item.brand || '';
  formData.colorValue = item.color || '';
  formData.size = item.size || '';
  formData.seasons = Array.isArray(item.season)
    ? [...item.season]
    : item.season
      ? [item.season]
      : [];
  formData.purchaseDate = item.purchaseDate || '';
  formData.price = item.price || '';
  formData.description = item.description || '';
  formData.tags = Array.isArray(item.tags) ? [...item.tags] : [];
  clothingPhotos.value = item.imageUrls ? [...item.imageUrls] : [];

  // Manually trigger dependent option loading after initial data is set
  // This ensures subCategoryOptions and typeOptions are populated based on itemToEdit data
  if (formData.categoryKey) {
    const mainCat = wardrobeStore.categorySystem[formData.categoryKey];
    if (mainCat && mainCat.subcategories) {
      subCategoryOptions.value = Object.entries(mainCat.subcategories).map(
        ([key, value]) => ({ value: key, label: value.name })
      );
      formData.subcategoryKey = item.subcategory || ''; // Set after options are populated
    } else {
      subCategoryOptions.value = [];
      formData.subcategoryKey = '';
    }
  }
  if (formData.subcategoryKey) {
    const mainCat = wardrobeStore.categorySystem[formData.categoryKey];
    const subCat = mainCat?.subcategories[formData.subcategoryKey];
    if (subCat && subCat.types) {
      typeOptions.value = subCat.types.map(type => ({
        value: type,
        label: type
      }));
      formData.typeValue = item.type || ''; // Set after options are populated
    } else {
      typeOptions.value = [];
      formData.typeValue = '';
    }
  }
};

onLoad(async options => {
  if (options.itemId) {
    editingItemId.value = options.itemId;
    pageTitle.value = BUTTON_TEXTS.EDIT_ITEM || '编辑衣物';

    if (wardrobeStore.wardrobeItems.length === 0) {
      // Ensure wardrobe items are loaded if not already, important for getItemById
      await wardrobeStore.fetchWardrobeItemsAction();
    }
    const itemToEdit = wardrobeStore.getItemById(editingItemId.value);

    if (itemToEdit) {
      populateFormForEdit(itemToEdit);
    } else {
      console.error(`Item with ID ${editingItemId.value} not found.`);
      uni.showToast({
        title: TOAST_TITLES.LOAD_FAILED_GENERAL || '加载衣物信息失败',
        icon: 'none'
      });
    }
  } else {
    pageTitle.value = BUTTON_TEXTS.ADD_NEW_ITEM || '添加新衣物';
  }
});

// 标签相关功能
const addTag = () => {
  const tag = tagInput.value.trim();
  if (tag && !formData.tags.includes(tag)) {
    formData.tags.push(tag);
    tagInput.value = '';
  }
};

const removeTag = index => {
  formData.tags.splice(index, 1);
};

// 保存衣物
const saveItem = async () => {
  let isLoadingShown = false;
  try {
    // 表单验证 - 名称
    if (!formData.name.trim()) {
      uni.showToast({ title: '请输入衣物名称', icon: 'none' });
      return;
    }

    // 表单验证 - 分类
    if (!formData.categoryKey) {
      uni.showToast({ title: '请选择主分类', icon: 'none' });
      return;
    }

    // 表单验证 - 子分类（如果主分类有子分类）
    if (subCategoryOptions.value.length > 0 && !formData.subcategoryKey) {
      uni.showToast({ title: '请选择子分类', icon: 'none' });
      return;
    }

    // 表单验证 - 具体类型（如果子分类有具体类型）
    if (typeOptions.value.length > 0 && !formData.typeValue) {
      uni.showToast({ title: '请选择具体类型', icon: 'none' });
      return;
    }

    // 表单验证 - 颜色（必填）
    if (!formData.colorValue) {
      uni.showToast({ title: '请选择衣物颜色', icon: 'none' });
      return;
    }

    // 表单验证 - 尺码（必填）
    if (!formData.size.trim()) {
      uni.showToast({ title: '请输入尺码', icon: 'none' });
      return;
    }

    // 表单验证 - 季节（必填，至少选择一个）
    if (formData.seasons.length === 0) {
      uni.showToast({ title: '请至少选择一个季节', icon: 'none' });
      return;
    }

    // 表单验证 - 照片
    if (clothingPhotos.value.length === 0) {
      uni.showToast({ title: '请至少添加一张衣物照片', icon: 'none' });
      return;
    }

    // Upload images first
    const uploadResult = await imageUploader.value.uploadImages();

    if (!uploadResult.success) {
      throw new Error(uploadResult.message || '图片上传失败');
    }

    // 构建衣物数据
    const dataToSave = {
      name: formData.name.trim(),
      category: formData.categoryKey,
      subcategory: formData.subcategoryKey || '',
      type: formData.typeValue || '',
      brand: formData.brand.trim() || undefined,
      color: formData.colorValue,
      size: formData.size.trim(),
      season: formData.seasons.length > 0 ? formData.seasons.join(',') : '',
      purchaseDate: formData.purchaseDate || undefined,
      price: formData.price ? parseFloat(formData.price) : undefined,
      description: formData.description.trim() || undefined,
      imageUrls: uploadResult.data // 使用上传后的图片URL
    };

    // 调试：打印发送的数据
    console.log('发送的衣物数据:', dataToSave);

    // Images uploaded successfully, show saving indicator
    uni.showLoading({
      title: editingItemId.value ? '正在更新...' : '正在保存...',
      mask: true
    });
    isLoadingShown = true;

    // 保存数据
    let savedItem;
    if (editingItemId.value) {
      dataToSave.id = editingItemId.value;
      savedItem = await wardrobeStore.updateItemAction(editingItemId.value, dataToSave);
    } else {
      savedItem = await wardrobeStore.addItemAction(dataToSave);
    }

    // 确保隐藏loading
    if (isLoadingShown) {
      uni.hideLoading();
      isLoadingShown = false;
    }

    // 保存成功提示
    uni.showToast({
      title: editingItemId.value ? '更新成功' : '添加成功',
      icon: 'success'
    });

    // 触发数据同步通知（实时更新机制会自动处理）
    // wardrobeStore 的 addItemAction 和 updateItemAction 已经包含了通知机制

    // 返回上一页
    setTimeout(() => {
      uni.navigateBack();
    }, 1500);
  } catch (error) {
    console.error('保存失败:', error);

    // 确保隐藏loading（如果之前显示过）
    if (isLoadingShown) {
      uni.hideLoading();
      isLoadingShown = false;
    }

    uni.showToast({
      title: error.message || '保存失败，请重试',
      icon: 'none'
    });
  } finally {
    // 再次确保隐藏loading，防止任何情况下loading未被关闭
    if (isLoadingShown) {
      uni.hideLoading();
    }
  }
};
</script>

<style lang="scss" scoped>
.add-item-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  background-color: #f8f8f8;
}

.header {
  padding: 30rpx 30rpx 20rpx;
  background-color: #fff;
  display: flex;
  justify-content: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.title-section {
  display: flex;
  align-items: center;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.form-scroll-view {
  flex: 1;
  padding: 0 0 120rpx 0; /* 底部留出按钮高度 */
}

.form-container {
  padding: 30rpx;
  display: flex;
  flex-direction: column;
  gap: 40rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.photo-upload-section,
.info-section,
.notes-section,
.tags-section {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.03);
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.photo-item {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.photo-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-photo {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.add-photo-btn {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  cursor: pointer;
  border: 2rpx dashed #ddd;
}

.add-photo-btn:active {
  background-color: #f0f0f0;
}

.add-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.form-item {
  margin-bottom: 24rpx;
}

.form-label {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 12rpx;
  display: block;

  &.required::before {
    content: '*';
    color: #ff4d4f;
    margin-right: 4rpx;
  }
}

.form-picker {
  width: 100%;
  height: 88rpx;
  background-color: #f9f9f9;
  border-radius: 12rpx;
  padding: 0 24rpx;
  box-sizing: border-box;
}

.picker-value {
  height: 88rpx;
  line-height: 88rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.notes-input {
  width: 100%;
}

.tags-input-container {
  display: flex;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.tag-input {
  flex: 1;
}

.add-tag-button {
  width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
  color: #666;
  border-radius: 10rpx;
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag {
  padding: 8rpx 16rpx;
  background-color: rgba(253, 160, 133, 0.1);
  color: #fda085;
  border-radius: 30rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
}

.remove-tag {
  margin-left: 8rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx; // Space between items
}

.checkbox-item {
  display: flex;
  align-items: center;
  // Adjust padding/margin as needed for visual spacing
  // Example: margin-right: 20rpx; margin-bottom: 10rpx;

  text {
    margin-left: 8rpx; // Space between checkbox and label text
    font-size: 28rpx;
    color: #333;
  }
}

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 24rpx 30rpx;
  background-color: #fff;
  border-top: 1px solid #eee;
  z-index: 100;
}

.save-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(to right, #f6d365, #fda085);
  border-radius: 12rpx;
  color: #fff;
  font-size: 32rpx;
  font-weight: 500;
}

.save-button[disabled] {
  background: #fdc8b8;
  opacity: 0.7;
  color: #fff;
}

.upload-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.upload-progress-container {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
}

.progress-bar-container {
  width: 100%;
  height: 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(to right, #f6d365, #fda085);
  border-radius: 10rpx;
  transition: width 0.3s;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
}
</style>
