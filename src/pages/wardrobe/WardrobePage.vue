<template>
  <view class="wardrobe-container">
    <view class="header">
      <view class="title-container">
        <text class="title">{{ MISC_TEXT.WARDROBE_TITLE }}</text>
      </view>
      <view class="filter-section">
        <view class="filter-button" @click="showFilterOptions">
          <uni-icons type="filter" size="24" color="#777"></uni-icons>
          <text class="filter-text">{{ BUTTON_TEXTS.FILTER }}</text>
        </view>
      </view>
    </view>

    <scroll-view
      scroll-y
      class="wardrobe-content"
      refresher-enabled
      :refresher-triggered="isRefreshing"
      @scrolltolower="loadMore"
      @refresherrefresh="handleRefresh"
      @scroll="handleScroll"
    >
      <view v-if="isLoading && !isLoadingMore" class="loading-container">
        <uni-load-more
          status="loading"
          :content-text="loadMoreText"
        ></uni-load-more>
      </view>

      <view
        v-else-if="filteredClothingItems.length === 0"
        class="empty-container"
      >
        <image
          src="/static/empty-wardrobe.png"
          mode="aspectFit"
          class="empty-image"
        ></image>
        <text class="empty-text">{{ MISC_TEXT.EMPTY_WARDROBE_PROMPT }}</text>
        <text class="empty-subtext">{{
          MISC_TEXT.EMPTY_WARDROBE_SUBPROMPT
        }}</text>
        <button class="add-button" @click="navigateToAddItem">
          {{ BUTTON_TEXTS.ADD_ITEM }}
        </button>
      </view>

      <view v-else class="clothing-grid-container">
        <!-- 虚拟滚动容器 -->
        <view class="virtual-container" :style="{ height: totalHeight + 'px' }">
          <view class="clothing-grid">
            <!-- 使用虚拟滚动的可见项目 -->
            <view
              v-for="item in visibleItems"
              :key="item.id"
              class="clothing-card"
              :style="{
                transform: `translateY(${Math.floor(item.virtualIndex / 2) * ITEM_HEIGHT}px)`,
                position: 'absolute',
                left:
                  item.virtualIndex % 2 === 0 ? '10rpx' : 'calc(50% + 10rpx)',
                width: 'calc(50% - 20rpx)'
              }"
              @click="viewClothingDetail(item.id)"
            >
              <view class="clothing-image-container">
                <image
                  v-if="shouldLoadImage(item.actualIndex)"
                  :src="getValidImageUrl(item)"
                  mode="aspectFill"
                  class="clothing-image"
                  @load="markImageLoaded(item.actualIndex)"
                  @error="handleImageError(item, $event)"
                />
                <view v-else class="image-placeholder">
                  <uni-icons type="image" size="40" color="#ccc" />
                </view>
                <view class="clothing-category-tag">{{
                  item.categoryDisplay || item.category
                }}</view>
              </view>
              <view class="clothing-info">
                <text class="clothing-name">{{ item.name }}</text>
                <text class="clothing-brand">{{ item.brand }}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <uni-load-more
        v-if="filteredClothingItems.length > 0"
        :status="loadMoreStatus"
        :content-text="loadMoreText"
      ></uni-load-more>
    </scroll-view>

    <view class="floating-button" @click="navigateToAddItem">
      <uni-icons type="plusempty" size="30" color="#FFFFFF"></uni-icons>
    </view>

    <WardrobeFilterModal
      :is-visible="showFilterModal"
      :initial-filters="activeFilters"
      @close="handleCloseFilterModal"
      @apply="handleApplyFilters"
    />
  </view>
</template>

<script setup>
// src/pages/wardrobe/WardrobePage.vue
import { ref, onMounted, reactive, computed, onBeforeUnmount } from 'vue'; // Add computed and onBeforeUnmount
import uniLoadMore from '@dcloudio/uni-ui/lib/uni-load-more/uni-load-more.vue';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
import WardrobeFilterModal from '@/components/WardrobeFilterModal.vue'; // Import the modal
import { useWardrobeStore } from '@/stores/wardrobeStore.js'; // To get all items
import { BUTTON_TEXTS, MISC_TEXT } from '@/constants/uiTexts.js'; // Import strings

const wardrobeStore = useWardrobeStore(); // Initialize store

const showFilterModal = ref(false);
const activeFilters = ref({
  category: null,
  subcategory: null,
  type: null,
  seasons: [],
  colorSystem: null
});

// 状态管理 - isLoading, isRefreshing, loadMoreStatus remain for UI control
const isLoading = ref(true);
const isLoadingMore = ref(false); // Will be set to noMore for filtered results for now
const isRefreshing = ref(false);
const loadMoreStatus = ref('more'); // 'more', 'loading', 'noMore'

const loadMoreText = reactive({
  contentdown: MISC_TEXT.LOAD_MORE_CONTENT_DOWN,
  contentrefresh: MISC_TEXT.LOAD_MORE_CONTENT_REFRESH,
  contentnomore: MISC_TEXT.LOAD_MORE_CONTENT_NOMORE
});

const filteredClothingItems = computed(() => {
  let itemsToFilter = [...wardrobeStore.wardrobeItems];

  if (activeFilters.value.category) {
    itemsToFilter = itemsToFilter.filter(
      item => item.category === activeFilters.value.category
    );
  }
  // Ensure category is set before filtering by subcategory
  if (activeFilters.value.category && activeFilters.value.subcategory) {
    itemsToFilter = itemsToFilter.filter(
      item => item.subcategory === activeFilters.value.subcategory
    );
  }
  // Ensure subcategory is set before filtering by type
  if (activeFilters.value.subcategory && activeFilters.value.type) {
    itemsToFilter = itemsToFilter.filter(
      item => item.type === activeFilters.value.type
    );
  }

  if (activeFilters.value.seasons && activeFilters.value.seasons.length > 0) {
    itemsToFilter = itemsToFilter.filter(
      item =>
        item.season &&
        item.season.length > 0 &&
        activeFilters.value.seasons.some(s => item.season.includes(s))
    );
  }

  if (activeFilters.value.colorSystem) {
    const targetSystemName =
      wardrobeStore.colorSystems[activeFilters.value.colorSystem]?.name;
    if (targetSystemName) {
      itemsToFilter = itemsToFilter.filter(
        item =>
          item.color &&
          wardrobeStore.getColorSystem(item.color) === targetSystemName
      );
    }
  }
  return itemsToFilter;
});

// 虚拟滚动的可见项目
const visibleItems = computed(() => {
  const items = filteredClothingItems.value;
  const start = Math.max(0, visibleRange.value.start - BUFFER_SIZE);
  const end = Math.min(items.length, visibleRange.value.end + BUFFER_SIZE);

  return items.slice(start, end).map((item, index) => ({
    ...item,
    virtualIndex: start + index,
    actualIndex: start + index
  }));
});

// 计算虚拟滚动的总高度
const totalHeight = computed(() => {
  const itemCount = filteredClothingItems.value.length;
  const rowCount = Math.ceil(itemCount / 2); // 每行2个
  return rowCount * ITEM_HEIGHT;
});

// 处理滚动事件
const handleScroll = e => {
  if (!isComponentMounted.value) return;

  const scrollTop = e.detail.scrollTop;
  const startIndex = Math.floor(scrollTop / ITEM_HEIGHT) * 2; // 每行2个
  const endIndex = startIndex + VISIBLE_COUNT;

  visibleRange.value = {
    start: Math.max(0, startIndex),
    end: Math.min(filteredClothingItems.value.length, endIndex)
  };
};

// 图片懒加载
const shouldLoadImage = index => {
  return (
    loadedImages.value.has(index) ||
    (index >= visibleRange.value.start - BUFFER_SIZE &&
      index <= visibleRange.value.end + BUFFER_SIZE)
  );
};

const markImageLoaded = index => {
  loadedImages.value.add(index);
};

// 获取有效的图片URL
const getValidImageUrl = item => {
  const imageUrl = item.imageUrl || (item.imageUrls && item.imageUrls[0]);

  // 检查是否为blob URL
  if (imageUrl && imageUrl.startsWith('blob:')) {
    // 尝试验证blob URL是否有效
    try {
      const img = new Image();
      img.onerror = () => {
        // Blob URL无效，使用默认图片
        return '/static/default-clothing.png';
      };
      img.src = imageUrl;
      return imageUrl;
    } catch (error) {
      console.warn('Blob URL验证失败:', error);
      return '/static/default-clothing.png';
    }
  }

  return imageUrl || '/static/default-clothing.png';
};

// 处理图片加载错误
const handleImageError = (item, event) => {
  console.warn('图片加载失败:', item.id, event);

  // 如果是blob URL失败，尝试清理本地缓存
  const imageUrl = item.imageUrl || (item.imageUrls && item.imageUrls[0]);
  if (imageUrl && imageUrl.startsWith('blob:')) {
    try {
      const cachedUrls = uni.getStorageSync('mock_uploaded_images') || [];
      const updatedUrls = cachedUrls.filter(url => url !== imageUrl);
      uni.setStorageSync('mock_uploaded_images', updatedUrls);
      console.log('已清理失效的blob URL:', imageUrl);
    } catch (error) {
      console.error('清理blob URL缓存失败:', error);
    }
  }

  // 设置为默认图片
  event.target.src = '/static/default-clothing.png';
};

// 虚拟滚动和性能优化配置
const ITEM_HEIGHT = 380; // 每个卡片高度 (300rpx image + 80rpx content)
const CONTAINER_HEIGHT = 600; // 可视区域高度
const VISIBLE_COUNT = Math.ceil(CONTAINER_HEIGHT / ITEM_HEIGHT) + 2; // 多缓存2个
const BUFFER_SIZE = 5; // 上下缓冲区大小

// 虚拟滚动状态
const visibleRange = ref({ start: 0, end: VISIBLE_COUNT });

// 懒加载图片状态
const loadedImages = ref(new Set());
const imageObserver = ref(null);

// 组件卸载标记
const isComponentMounted = ref(true);

onBeforeUnmount(() => {
  isComponentMounted.value = false;
  // 清理图片观察器
  if (imageObserver.value) {
    imageObserver.value.disconnect();
  }
});

// 页面加载时获取数据
onMounted(async () => {
  isLoading.value = true;
  if (wardrobeStore.wardrobeItems.length === 0) {
    await wardrobeStore.fetchWardrobeItemsAction(); // Fetch initial data into store
  }

  // 初始化虚拟滚动
  const itemCount = filteredClothingItems.value.length;
  visibleRange.value = {
    start: 0,
    end: Math.min(VISIBLE_COUNT, itemCount)
  };

  // 预加载前几张图片
  for (let i = 0; i < Math.min(6, itemCount); i++) {
    markImageLoaded(i);
  }

  isLoading.value = false;
});

// 刷新数据
const handleRefresh = () => {
  if (!isComponentMounted.value) return;

  isRefreshing.value = true;
  isLoading.value = true;

  // 直接更新状态，不使用 setTimeout
  try {
    // 重新计算过滤后的数据
    filteredClothingItems.value;
  } finally {
    if (isComponentMounted.value) {
      isLoading.value = false;
      isRefreshing.value = false;
    }
  }
};

const loadMore = () => {
  if (
    activeFilters.value.category ||
    (activeFilters.value.seasons && activeFilters.value.seasons.length > 0) ||
    activeFilters.value.colorSystem
  ) {
    // No pagination on filtered results for now with mock data
    loadMoreStatus.value = 'noMore';
    return;
  }
  // Original loadMore logic for non-filtered data (if any) would go here.
  // For now, assume all items are loaded by fetchWardrobeItemsAction.
  loadMoreStatus.value = 'noMore';
};

// 导航到新增衣物页面
const navigateToAddItem = () => {
  uni.navigateTo({
    url: '/pages/main/addItem/AddItemPage'
  });
};

// 查看衣物详情
const viewClothingDetail = id => {
  uni.navigateTo({
    url: `/pages/main/clothingDetail/ClothingDetailPage?id=${id}`,
    fail: err => {
      console.error('页面跳转失败:', err);
      uni.showToast({
        title: '页面跳转失败',
        icon: 'none'
      });
    }
  });
};

// 显示筛选选项（待实现）
const showFilterOptions = () => {
  // uni.showToast({ title: TOAST_TITLES.FILTER_SOON, icon: 'none' }); // Old logic
  showFilterModal.value = true; // New logic
};

const handleCloseFilterModal = () => {
  if (!isComponentMounted.value) return;
  showFilterModal.value = false;
};

const handleApplyFilters = newFilters => {
  if (!isComponentMounted.value) return;

  activeFilters.value = newFilters;

  // 先更新状态
  isLoading.value = true;

  try {
    // 重新计算过滤后的数据
    const filteredResults = filteredClothingItems.value;

    // 显示过滤结果反馈
    uni.showToast({
      title:
        filteredResults.length > 0
          ? `找到 ${filteredResults.length} 件衣物`
          : '未找到符合条件的衣物',
      icon: filteredResults.length > 0 ? 'success' : 'none',
      duration: 2000
    });
  } finally {
    if (isComponentMounted.value) {
      isLoading.value = false;
      showFilterModal.value = false; // 确保模态框关闭
    }
  }
};
</script>

<style scoped lang="scss">
.wardrobe-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100vh;
  background-color: #f8f8f8;
  position: relative;
}

.header {
  padding: 30rpx 30rpx 20rpx;
  background-color: #fff;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.title-container {
  flex: 1;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.filter-section {
  display: flex;
  align-items: center;
}

.filter-button {
  display: flex;
  align-items: center;
  padding: 10rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
}

.filter-text {
  font-size: 26rpx;
  color: #777;
  margin-left: 8rpx;
}

.wardrobe-content {
  flex: 1;
  width: 100%;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 300rpx;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 40rpx;
}

.empty-image {
  width: 250rpx;
  height: 250rpx;
  margin-bottom: 30rpx;
}

.empty-text {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 10rpx;
}

.empty-subtext {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 40rpx;
  text-align: center;
}

.add-button {
  background: linear-gradient(to right, #f6d365, #fda085);
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
  padding: 0 60rpx;
  height: 80rpx;
  line-height: 80rpx;
}

.clothing-grid-container {
  position: relative;
  min-height: 300rpx;
}

.virtual-container {
  position: relative;
  width: 100%;
}

.clothing-grid {
  position: relative;
  width: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

.clothing-card {
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 20rpx;
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.98);
  }
}

.clothing-image-container {
  position: relative;
  width: 100%;
  height: 300rpx;
}

.clothing-image {
  width: 100%;
  height: 100%;
  transition: opacity 0.3s ease;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  color: #ccc;
}

.clothing-category-tag {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  padding: 4rpx 12rpx;
  background-color: rgba(253, 160, 133, 0.8);
  color: #fff;
  font-size: 22rpx;
  border-radius: 20rpx;
}

.clothing-info {
  padding: 16rpx;
}

.clothing-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 6rpx;
  display: block;
}

.clothing-brand {
  font-size: 24rpx;
  color: #999;
  display: block;
}

.floating-button {
  position: fixed;
  right: 40rpx;
  bottom: 80rpx;
  width: 100rpx;
  height: 100rpx;
  border-radius: 50%;
  background: linear-gradient(to right, #f6d365, #fda085);
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 6rpx 16rpx rgba(253, 160, 133, 0.4);
  z-index: 100;
}
</style>
