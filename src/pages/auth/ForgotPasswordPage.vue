<template>
  <view class="forgot-container">
    <view class="forgot-card">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <view class="title">重置密码</view>
      <view class="subtitle">请输入您的账号/邮箱，我们将发送重置链接</view>

      <view class="form-container">
        <view class="input-group">
          <text class="input-label">账号 / 邮箱</text>
          <uni-easyinput
            v-model="forgotForm.username"
            type="text"
            placeholder="请输入账号/邮箱"
            prefix-icon="person"
            class="input-field"
            :styles="easyInputStyles"
            :input-border="false"
            placeholder-style="font-size: 28rpx; color: #bbbbbb;"
          ></uni-easyinput>
        </view>

        <button
          type="primary"
          :loading="isLoading"
          :disabled="isLoading"
          class="submit-button"
          @click="handleSubmit"
        >
          发送重置链接
        </button>
      </view>

      <view class="links-container">
        <text class="link-text" @click="handleLogin">返回登录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue';
// 显式导入uni-easyinput组件
import uniEasyinput from '@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue';

const forgotForm = reactive({
  username: ''
});

const easyInputStyles = reactive({
  borderColor: '#e0e0e0',
  color: '#303133',
  backgroundColor: '#FFFFFF',
  borderRadius: '10rpx'
});

const isLoading = ref(false);

const handleSubmit = async () => {
  if (!forgotForm.username) {
    uni.showToast({ title: '请输入账号/邮箱', icon: 'none' });
    return;
  }

  // 简单的邮箱格式验证
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  if (!emailRegex.test(forgotForm.username)) {
    uni.showToast({ title: '请输入正确的邮箱格式', icon: 'none' });
    return;
  }

  isLoading.value = true;
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1500));

    // 显示成功信息
    uni.showToast({
      title: '重置链接已发送',
      icon: 'success',
      duration: 2000,
      success: () => {
        // 延迟导航以便用户看到成功消息
        setTimeout(() => {
          uni.redirectTo({
            url: '/pages/auth/login/LoginPage'
          });
        }, 2000);
      }
    });
  } catch (error) {
    const errorMessage = error.message || '发送重置链接失败';
    uni.showToast({
      title: errorMessage,
      icon: 'none'
    });
  } finally {
    isLoading.value = false;
  }
};

const handleLogin = () => {
  uni.redirectTo({ url: '/pages/auth/login/LoginPage' });
};

// onLoad(() => {
//   // Page load logic if any
// });
</script>

<style scoped lang="scss">
.forgot-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  box-sizing: border-box;
  background: linear-gradient(120deg, #f6d365 0%, #fda085 100%);
  padding: 40rpx 30rpx;
}

.forgot-card {
  width: 100%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);
  padding: 50rpx 40rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 40rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 16rpx;
  text-align: center;
}

.subtitle {
  font-size: 28rpx;
  color: #777777;
  margin-bottom: 50rpx;
  text-align: center;
  padding: 0 20rpx;
}

.form-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.input-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #555555;
  padding-left: 4rpx;
}

.input-field {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;

  ::v-deep .uni-easyinput__content {
    border: 1px solid #dddddd;
    border-radius: 12rpx;
    background-color: #ffffff;
    height: 100%;
    transition: all 0.3s ease;

    &:hover {
      border-color: #fda085;
    }
  }

  ::v-deep .uni-easyinput__content-input {
    height: 88rpx;
    line-height: 88rpx;
    font-size: 28rpx;
    color: #333333;
    padding: 0 25rpx;
  }

  ::v-deep .uni-icons {
    color: #999999;
    font-size: 36rpx;
    margin: 0 20rpx;
  }

  &.is-focused {
    ::v-deep .uni-easyinput__content {
      border-color: #fda085;
      box-shadow: 0 0 0 2px rgba(253, 160, 133, 0.1);
    }
  }
}

.submit-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(to right, #f6d365, #fda085);
  border-radius: 12rpx;
  color: #ffffff;
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 6rpx 16rpx rgba(253, 160, 133, 0.4);
  margin-top: 20rpx;
  transition: all 0.3s ease;

  &:active {
    transform: translateY(2rpx);
    box-shadow: 0 3rpx 8rpx rgba(253, 160, 133, 0.4);
  }

  &::after {
    border: none;
  }
}

.links-container {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

.link-text {
  font-size: 28rpx;
  color: #777777;
  text-decoration: underline;
  transition: color 0.3s ease;

  &:active {
    color: #fda085;
  }
}

.divider {
  margin: 0 16rpx;
  color: #dddddd;
}

// Responsive adjustments if any, similar to LoginPage.vue
@media screen and (max-width: 320px) {
  .forgot-card {
    padding: 40rpx 30rpx;
  }
  .title {
    font-size: 40rpx;
  }
  .subtitle {
    font-size: 26rpx;
  }
}
</style>
