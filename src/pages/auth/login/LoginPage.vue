<template>
  <view class="login-container">
    <view class="login-card">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <view class="title">欢迎回来!</view>
      <view class="subtitle">登录您的衣柜账户</view>
      <view class="form-container">
        <view class="input-group">
          <text class="input-label">账号 / 邮箱</text>
          <uni-easyinput
            v-model="loginForm.email"
            type="text"
            placeholder="请输入邮箱地址"
            prefix-icon="email"
            class="input-field"
            :styles="easyInputStyles"
            :input-border="false"
            placeholder-style="font-size: 28rpx; color: #bbbbbb;"
          ></uni-easyinput>
        </view>
        <view class="input-group">
          <text class="input-label">密码</text>
          <uni-easyinput
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
            prefix-icon="locked"
            class="input-field"
            :styles="easyInputStyles"
            :input-border="false"
            placeholder-style="font-size: 28rpx; color: #bbbbbb;"
          ></uni-easyinput>
        </view>
        <button
          type="primary"
          :loading="isLoading"
          :disabled="isLoading"
          class="login-button"
          @click="handleLogin"
        >
          登录
        </button>
      </view>
      <view class="links-container">
        <text class="link-text" @click="handleForgotPassword">忘记密码？</text>
        <text class="divider">|</text>
        <text
class="link-text" @click="handleRegister"
          >还没有账号？立即注册</text
        >
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue';
// 显式导入uni-easyinput组件
import uniEasyinput from '@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue';
import { useUserStore } from '@/stores/userStore';
import { navigateAfterLogin } from '@/utils/navigation';

const loginForm = reactive({
  email: '',
  password: ''
});

const easyInputStyles = reactive({
  borderColor: '#e0e0e0', // 更新边框颜色
  color: '#303133', // 输入文字颜色 (uni-easyinput的styles.color 指的是文字颜色)
  // placeholderColor: '#a9a9a9', // 移动到 placeholderStyle prop
  backgroundColor: '#FFFFFF',
  // 以下为新增或调整以期通过 props 控制更多样式
  borderRadius: '10rpx' // 尝试通过 prop 设置圆角
  // padding: '0 25rpx', // uni-easyinput 的 styles 不直接支持 padding, 需要通过 inputStyle 或 ::v-deep
  // height: '90rpx' // uni-easyinput 的 styles 不直接支持 height, 由外部class控制
});

const isLoading = ref(false);
const userStore = useUserStore();

const handleLogin = async () => {
  if (!loginForm.email) {
    uni.showToast({ title: '请输入邮箱', icon: 'none' });
    return;
  }
  if (!loginForm.password) {
    uni.showToast({ title: '请输入密码', icon: 'none' });
    return;
  }
  if (loginForm.password.length < 6) {
    uni.showToast({ title: '密码长度至少为6位', icon: 'none' });
    return;
  }

  isLoading.value = true;
  try {
    await userStore.login({
      email: loginForm.email,
      password: loginForm.password
    });
    uni.showToast({ title: '登录成功', icon: 'success' });
    // 使用导航工具函数进行登录后导航，确保正确的导航栈管理
    navigateAfterLogin();
  } catch (error) {
    const errorMessage = error.message || '登录时发生未知错误';
    uni.showToast({ title: `登录失败: ${errorMessage}`, icon: 'none' });
  } finally {
    isLoading.value = false;
  }
};

const handleForgotPassword = () => {
  uni.navigateTo({ url: '/pages/auth/ForgotPasswordPage' });
};

const handleRegister = () => {
  uni.navigateTo({ url: '/pages/auth/register/RegisterPage' });
};
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  box-sizing: border-box;
  background: linear-gradient(120deg, #f6d365 0%, #fda085 100%);
  padding: 40rpx 30rpx;
}

.login-card {
  width: 100%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);
  padding: 50rpx 40rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 40rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 16rpx;
  text-align: center;
}

.subtitle {
  font-size: 28rpx;
  color: #777777;
  margin-bottom: 50rpx;
  text-align: center;
}

.form-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.input-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #555555;
  padding-left: 4rpx;
}

.input-field {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;

  ::v-deep .uni-easyinput__content {
    border: 1px solid #dddddd;
    border-radius: 12rpx;
    background-color: #ffffff;
    height: 100%;
    transition: all 0.3s ease;

    &:hover {
      border-color: #fda085;
    }
  }

  ::v-deep .uni-easyinput__content-input {
    height: 88rpx;
    line-height: 88rpx;
    font-size: 28rpx;
    color: #333333;
    padding: 0 25rpx;
  }

  ::v-deep .uni-icons {
    color: #999999;
    font-size: 36rpx;
    margin: 0 20rpx;
  }

  &.is-focused {
    ::v-deep .uni-easyinput__content {
      border-color: #fda085;
      box-shadow: 0 0 0 2px rgba(253, 160, 133, 0.1);
    }
  }
}

.login-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(to right, #f6d365, #fda085);
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-top: 20rpx;
  border: none;
  transition: opacity 0.3s ease;

  &:active {
    opacity: 0.9;
  }
}

.links-container {
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  font-size: 28rpx;

  .link-text {
    color: #fda085;
    padding: 10rpx;
    transition: opacity 0.3s ease;

    &:active {
      opacity: 0.7;
    }
  }

  .divider {
    color: #dddddd;
  }
}

@media screen and (max-width: 320px) {
  .login-card {
    padding: 40rpx 30rpx;
  }

  .title {
    font-size: 40rpx;
  }

  .subtitle {
    font-size: 26rpx;
  }
}
</style>
