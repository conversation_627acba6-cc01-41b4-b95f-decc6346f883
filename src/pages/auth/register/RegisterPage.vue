<template>
  <view class="login-container">
    <view class="login-card">
      <image class="logo" src="/static/logo.png" mode="aspectFit"></image>
      <view class="title">创建您的账户</view>
      <view class="subtitle">快速加入，开启您的时尚之旅</view>
      <view class="form-container">
        <view class="input-group">
          <text class="input-label">用户名</text>
          <uni-easyinput
            v-model="registerForm.username"
            type="text"
            placeholder="请输入您的用户名"
            prefix-icon="person"
            class="input-field"
            :styles="easyInputStyles"
            :input-border="false"
            placeholder-style="font-size: 28rpx; color: #bbbbbb;"
          ></uni-easyinput>
        </view>
        <view class="input-group">
          <text class="input-label">邮箱</text>
          <uni-easyinput
            v-model="registerForm.email"
            type="text"
            placeholder="请输入您的邮箱地址"
            prefix-icon="email"
            class="input-field"
            :styles="easyInputStyles"
            :input-border="false"
            placeholder-style="font-size: 28rpx; color: #bbbbbb;"
          ></uni-easyinput>
        </view>
        <view class="input-group">
          <text class="input-label">密码</text>
          <uni-easyinput
            v-model="registerForm.password"
            type="password"
            placeholder="请输入密码 (至少6位)"
            prefix-icon="locked"
            class="input-field"
            :styles="easyInputStyles"
            :input-border="false"
            placeholder-style="font-size: 28rpx; color: #bbbbbb;"
          ></uni-easyinput>
        </view>
        <view class="input-group">
          <text class="input-label">确认密码</text>
          <uni-easyinput
            v-model="registerForm.confirmPassword"
            type="password"
            placeholder="请再次输入密码"
            prefix-icon="locked"
            class="input-field"
            :styles="easyInputStyles"
            :input-border="false"
            placeholder-style="font-size: 28rpx; color: #bbbbbb;"
          ></uni-easyinput>
        </view>
        <button
          type="primary"
          :loading="isLoading"
          :disabled="isLoading"
          class="login-button"
          @click="handleRegister"
        >
          立即注册
        </button>
      </view>
      <view class="links-container">
        <text class="link-text" @click="handleLogin">已有账户？立即登录</text>
      </view>
    </view>
  </view>
</template>

<script setup>
import { reactive, ref } from 'vue';
// 显式导入uni-easyinput组件
import uniEasyinput from '@dcloudio/uni-ui/lib/uni-easyinput/uni-easyinput.vue';
import { useUserStore } from '@/stores/userStore';

const registerForm = reactive({
  username: '',
  email: '',
  password: '',
  confirmPassword: ''
});

const easyInputStyles = reactive({
  borderColor: '#e0e0e0',
  color: '#303133',
  backgroundColor: '#FFFFFF',
  borderRadius: '10rpx'
});

const isLoading = ref(false);

// 简单邮箱验证函数
const isValidEmail = email => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

const handleRegister = async () => {
  // 表单验证
  if (!registerForm.username) {
    uni.showToast({ title: '请输入用户名', icon: 'none' });
    return;
  }
  if (!registerForm.email) {
    uni.showToast({ title: '请输入邮箱地址', icon: 'none' });
    return;
  }
  if (!isValidEmail(registerForm.email)) {
    uni.showToast({ title: '请输入有效的邮箱地址', icon: 'none' });
    return;
  }
  if (!registerForm.password) {
    uni.showToast({ title: '请输入密码', icon: 'none' });
    return;
  }
  if (registerForm.password.length < 6) {
    uni.showToast({ title: '密码长度至少为6位', icon: 'none' });
    return;
  }
  if (!registerForm.confirmPassword) {
    uni.showToast({ title: '请确认密码', icon: 'none' });
    return;
  }
  if (registerForm.password !== registerForm.confirmPassword) {
    uni.showToast({ title: '两次输入的密码不一致', icon: 'none' });
    return;
  }

  const userStore = useUserStore(); // Get instance of the store
  isLoading.value = true;
  try {
    // 修改为使用正确的方法名和参数格式，并包含email字段
    await userStore.register({
      username: registerForm.username,
      email: registerForm.email,
      password: registerForm.password
    });

    // 模拟成功，在Toast完成后再执行跳转
    uni.showToast({
      title: '注册成功！',
      icon: 'success',
      duration: 1500,
      complete: () => {
        // 在Toast完全显示后再执行跳转
        uni.redirectTo({
          url: '/pages/auth/login/LoginPage'
        });
      }
    });
  } catch (error) {
    const errorMessage = error.message || '注册时发生未知错误';
    uni.showToast({ title: `注册失败: ${errorMessage}`, icon: 'none' }); // Added "注册失败: " prefix for clarity
  } finally {
    isLoading.value = false;
  }
};

const handleLogin = () => {
  uni.redirectTo({ url: '/pages/auth/login/LoginPage' });
};
</script>

<style scoped lang="scss">
.login-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  box-sizing: border-box;
  background: linear-gradient(120deg, #f6d365 0%, #fda085 100%);
  padding: 40rpx 30rpx;
}

.login-card {
  width: 100%;
  max-width: 650rpx;
  background-color: #ffffff;
  border-radius: 30rpx;
  box-shadow: 0 15rpx 35rpx rgba(0, 0, 0, 0.1);
  padding: 50rpx 40rpx;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.logo {
  width: 180rpx;
  height: 180rpx;
  margin-bottom: 40rpx;
}

.title {
  font-size: 44rpx;
  font-weight: 700;
  color: #333333;
  margin-bottom: 16rpx;
  text-align: center;
}

.subtitle {
  font-size: 28rpx;
  color: #777777;
  margin-bottom: 50rpx;
  text-align: center;
}

.form-container {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 30rpx;
}

.input-group {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.input-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #555555;
  padding-left: 4rpx;
}

.input-field {
  width: 100%;
  height: 88rpx;
  background-color: #ffffff;

  ::v-deep .uni-easyinput__content {
    border: 1px solid #dddddd;
    border-radius: 12rpx;
    background-color: #ffffff;
    height: 100%;
    transition: all 0.3s ease;

    &:hover {
      border-color: #fda085;
    }
  }

  ::v-deep .uni-easyinput__content-input {
    height: 88rpx;
    line-height: 88rpx;
    font-size: 28rpx;
    color: #333333;
    padding: 0 25rpx;
  }

  ::v-deep .uni-icons {
    color: #999999;
    font-size: 36rpx;
    margin: 0 20rpx;
  }

  &.is-focused {
    ::v-deep .uni-easyinput__content {
      border-color: #fda085;
      box-shadow: 0 0 0 2px rgba(253, 160, 133, 0.1);
    }
  }
}

.login-button {
  width: 100%;
  height: 88rpx;
  line-height: 88rpx;
  background: linear-gradient(to right, #f6d365, #fda085);
  border-radius: 12rpx;
  font-size: 32rpx;
  font-weight: 600;
  color: #ffffff;
  margin-top: 20rpx;
  border: none;
  transition: opacity 0.3s ease;

  &:active {
    opacity: 0.9;
  }
}

.links-container {
  margin-top: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20rpx;
  font-size: 28rpx;

  .link-text {
    color: #fda085;
    padding: 10rpx;
    transition: opacity 0.3s ease;

    &:active {
      opacity: 0.7;
    }
  }

  .divider {
    color: #dddddd;
  }
}

@media screen and (max-width: 320px) {
  .login-card {
    padding: 40rpx 30rpx;
  }

  .title {
    font-size: 40rpx;
  }

  .subtitle {
    font-size: 26rpx;
  }
}
</style>
