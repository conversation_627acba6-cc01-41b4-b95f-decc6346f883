import { createApp } from 'vue';
import ToastSystem from '@/components/common/ToastSystem.vue';
import { setToastInstance } from '@/composables/useFeedback.js';

// 全局反馈插件
export default {
  install(app, options = {}) {
    const defaultOptions = {
      position: 'top',
      maxCount: 5,
      defaultDuration: 3000,
      ...options
    };

    // 创建 Toast 容器
    const createToastContainer = () => {
      const container = document.createElement('div');
      container.id = 'toast-container';
      document.body.appendChild(container);

      const toastApp = createApp(ToastSystem, defaultOptions);
      const toastInstance = toastApp.mount(container);

      // 设置全局 Toast 实例
      setToastInstance(toastInstance);

      return toastInstance;
    };

    // 在应用挂载后创建 Toast 容器
    app.mixin({
      mounted() {
        if (this.$el && this.$el.nodeType === 1 && !document.getElementById('toast-container')) {
          createToastContainer();
        }
      }
    });

    // 提供全局属性
    app.config.globalProperties.$toast = {
      success: (message, options) => {
        const instance = document.getElementById('toast-container')?.__vue_app__?.mount()?.$refs?.toastSystem;
        return instance?.success(message, options);
      },
      error: (message, options) => {
        const instance = document.getElementById('toast-container')?.__vue_app__?.mount()?.$refs?.toastSystem;
        return instance?.error(message, options);
      },
      warning: (message, options) => {
        const instance = document.getElementById('toast-container')?.__vue_app__?.mount()?.$refs?.toastSystem;
        return instance?.warning(message, options);
      },
      info: (message, options) => {
        const instance = document.getElementById('toast-container')?.__vue_app__?.mount()?.$refs?.toastSystem;
        return instance?.info(message, options);
      },
      loading: (message, options) => {
        const instance = document.getElementById('toast-container')?.__vue_app__?.mount()?.$refs?.toastSystem;
        return instance?.loading(message, options);
      }
    };

    // 提供全局方法
    app.provide('$feedback', {
      toast: app.config.globalProperties.$toast,
      ...defaultOptions
    });
  }
};

// uni-app 专用反馈工具
export const uniFeedback = {
  // 显示 Toast
  showToast(options) {
    const { title, icon = 'none', duration = 2000, mask = false } = options;
    
    if (typeof uni !== 'undefined' && uni.showToast) {
      uni.showToast({
        title,
        icon,
        duration,
        mask
      });
    }
  },

  // 显示加载
  showLoading(options) {
    const { title = '加载中...', mask = true } = options;
    
    if (typeof uni !== 'undefined' && uni.showLoading) {
      uni.showLoading({
        title,
        mask
      });
    }
  },

  // 隐藏加载
  hideLoading() {
    if (typeof uni !== 'undefined' && uni.hideLoading) {
      uni.hideLoading();
    }
  },

  // 显示模态框
  showModal(options) {
    const {
      title = '提示',
      content,
      showCancel = true,
      cancelText = '取消',
      confirmText = '确定',
      cancelColor = '#000000',
      confirmColor = '#007aff'
    } = options;

    return new Promise((resolve) => {
      if (typeof uni !== 'undefined' && uni.showModal) {
        uni.showModal({
          title,
          content,
          showCancel,
          cancelText,
          confirmText,
          cancelColor,
          confirmColor,
          success: (res) => {
            resolve(res.confirm);
          },
          fail: () => {
            resolve(false);
          }
        });
      } else {
        resolve(false);
      }
    });
  },

  // 显示操作菜单
  showActionSheet(options) {
    const { itemList, itemColor = '#000000' } = options;

    return new Promise((resolve) => {
      if (typeof uni !== 'undefined' && uni.showActionSheet) {
        uni.showActionSheet({
          itemList,
          itemColor,
          success: (res) => {
            resolve(res.tapIndex);
          },
          fail: () => {
            resolve(-1);
          }
        });
      } else {
        resolve(-1);
      }
    });
  },

  // 触觉反馈
  vibrateShort(type = 'light') {
    if (typeof uni !== 'undefined' && uni.vibrateShort) {
      try {
        uni.vibrateShort({ type });
      } catch (e) {
        console.warn('Vibrate not supported:', e);
      }
    }
  },

  vibrateLong() {
    if (typeof uni !== 'undefined' && uni.vibrateLong) {
      try {
        uni.vibrateLong();
      } catch (e) {
        console.warn('Vibrate not supported:', e);
      }
    }
  }
};

// 反馈工具函数
export const feedbackUtils = {
  // 成功反馈
  success(message, options = {}) {
    if (typeof uni !== 'undefined') {
      uniFeedback.showToast({
        title: message,
        icon: 'success',
        ...options
      });
      uniFeedback.vibrateShort('light');
    }
  },

  // 错误反馈
  error(message, options = {}) {
    if (typeof uni !== 'undefined') {
      uniFeedback.showToast({
        title: message,
        icon: 'error',
        duration: 3000,
        ...options
      });
      uniFeedback.vibrateShort('heavy');
    }
  },

  // 警告反馈
  warning(message, options = {}) {
    if (typeof uni !== 'undefined') {
      uniFeedback.showToast({
        title: message,
        icon: 'none',
        duration: 3000,
        ...options
      });
      uniFeedback.vibrateShort('medium');
    }
  },

  // 信息反馈
  info(message, options = {}) {
    if (typeof uni !== 'undefined') {
      uniFeedback.showToast({
        title: message,
        icon: 'none',
        ...options
      });
    }
  },

  // 加载反馈
  loading(message = '加载中...', options = {}) {
    if (typeof uni !== 'undefined') {
      uniFeedback.showLoading({
        title: message,
        ...options
      });
    }
  },

  // 隐藏加载
  hideLoading() {
    uniFeedback.hideLoading();
  },

  // 确认对话框
  async confirm(message, title = '确认') {
    return await uniFeedback.showModal({
      title,
      content: message,
      showCancel: true
    });
  },

  // 警告对话框
  async alert(message, title = '提示') {
    return await uniFeedback.showModal({
      title,
      content: message,
      showCancel: false
    });
  },

  // 操作菜单
  async actionSheet(items) {
    return await uniFeedback.showActionSheet({
      itemList: items
    });
  }
};

// 导出默认实例
export { uniFeedback as default };