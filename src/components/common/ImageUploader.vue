<!--
/**
 * ImageUploader Component
 * 
 * A comprehensive image upload component for uni-app with advanced features including:
 * - Multi-image selection and upload
 * - Image compression and caching
 * - Progress tracking and loading states
 * - Lazy loading and performance optimization
 * - Cross-platform compatibility (H5, WeChat Mini Program, App)
 * 
 * @component ImageUploader
 * @example
 * <ImageUploader
 *   v-model:value="imageList"
 *   :max-count="5"
 *   :auto-upload="true"
 *   @success="handleUploadSuccess"
 *   @error="handleUploadError"
 * />
 */
-->
<template>
  <view class="image-uploader">
    <!-- 图片列表 -->
    <view class="photo-grid">
      <view v-for="(photo, index) in photos" :key="index" class="photo-item">
        <image
          :src="photo"
          mode="aspectFill"
          class="photo-preview"
          @click="handlePreview(index)"
        ></image>
        <view
          v-if="!disabled"
          class="delete-photo"
          @click="handleRemove(index)"
        >
          <uni-icons type="close" size="20" color="#fff"></uni-icons>
        </view>
        <view
          v-if="isUploading && uploadingIndex === index"
          class="loading-overlay"
        >
          <view class="loading-spinner"></view>
        </view>
      </view>
      <view
        v-if="photos.length < maxCount && !disabled"
        class="add-photo-btn"
        @click="handleChoose"
      >
        <uni-icons type="camera" size="30" color="#bbbbbb"></uni-icons>
        <text class="add-text">添加照片</text>
      </view>
    </view>
    <text v-if="showTip" class="tip-text">{{ tipText }}</text>

    <!-- 上传进度指示器 -->
    <view v-if="showProgress" class="upload-progress-overlay">
      <view class="upload-progress-container">
        <view class="progress-title">正在上传图片</view>
        <view class="progress-bar-container">
          <view class="progress-bar" :style="{ width: progress + '%' }"></view>
        </view>
        <view class="progress-text">{{ progress }}%</view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch } from 'vue';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
import { uploadApi } from '@/api/upload.js';

// 图片缓存管理
class ImageCacheManager {
  constructor() {
    this.cache = new Map();
    this.maxCacheSize = 50; // 最大缓存50张图片
    this.compressionCache = new Map();
  }

  // 获取缓存key
  getCacheKey(url, size = '') {
    return `${url}_${size}`;
  }

  // 设置缓存
  setCache(key, data) {
    if (this.cache.size >= this.maxCacheSize) {
      // 删除最老的缓存
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(key, {
      data,
      timestamp: Date.now()
    });
  }

  // 获取缓存
  getCache(key) {
    const cached = this.cache.get(key);
    if (cached) {
      // 检查缓存是否过期（1小时）
      if (Date.now() - cached.timestamp < 3600000) {
        return cached.data;
      } else {
        this.cache.delete(key);
      }
    }
    return null;
  }

  // 清理缓存
  clearCache() {
    this.cache.clear();
    this.compressionCache.clear();
  }
}

// 图片压缩工具
class ImageCompressor {
  static async compressImage(filePath, options = {}) {
    const { quality = 0.8 } = options;

    return new Promise(resolve => {
      // 在小程序环境中使用uni.compressImage
      if (uni.compressImage) {
        uni.compressImage({
          src: filePath,
          quality: Math.floor(quality * 100),
          success: res => {
            resolve({
              success: true,
              tempFilePath: res.tempFilePath,
              originalSize: 0,
              compressedSize: 0
            });
          },
          fail: err => {
            // 如果压缩失败，返回原图片
            resolve({
              success: false,
              tempFilePath: filePath,
              error: err
            });
          }
        });
      } else {
        // H5环境下的压缩逻辑
        resolve({
          success: false,
          tempFilePath: filePath,
          error: 'H5 compression not implemented'
        });
      }
    });
  }
}

// 全局图片缓存实例
const imageCache = new ImageCacheManager();

/**
 * Component Props
 * @typedef {Object} ImageUploaderProps
 * @property {Array<string>} value - Array of selected image paths (v-model)
 * @property {number} maxCount - Maximum number of images allowed (default: 5)
 * @property {boolean} showTip - Whether to show tip text (default: true)
 * @property {string} tipText - Tip text to display (default: '添加照片，最多5张')
 * @property {number} quality - Image compression quality 0-1 (default: 0.8)
 * @property {number} maxWidth - Maximum width after compression (default: 1080)
 * @property {number} maxHeight - Maximum height after compression (default: 1080)
 * @property {boolean} enableCache - Enable image caching (default: true)
 * @property {boolean} enableCompression - Enable image compression (default: true)
 * @property {Array<string>} sourceType - Image source types ['album', 'camera'] (default: ['album', 'camera'])
 * @property {Array<string>} sizeType - Image size types ['original', 'compressed'] (default: ['compressed'])
 * @property {boolean} disabled - Disable the component (default: false)
 * @property {boolean} autoUpload - Automatically upload images after selection (default: false)
 */
const props = defineProps({
  // 已选择的图片路径数组
  value: {
    type: Array,
    default: () => []
  },
  // 最大图片数量
  maxCount: {
    type: Number,
    default: 5
  },
  // 是否显示提示文字
  showTip: {
    type: Boolean,
    default: true
  },
  // 提示文字
  tipText: {
    type: String,
    default: '添加照片，最多5张'
  },
  // 图片压缩质量(0-1)
  quality: {
    type: Number,
    default: 0.8
  },
  // 压缩后最大宽度
  maxWidth: {
    type: Number,
    default: 1080
  },
  // 压缩后最大高度
  maxHeight: {
    type: Number,
    default: 1080
  },
  // 是否启用图片缓存
  enableCache: {
    type: Boolean,
    default: true
  },
  // 是否启用图片压缩
  enableCompression: {
    type: Boolean,
    default: true
  },
  // 图片来源
  sourceType: {
    type: Array,
    default: () => ['album', 'camera']
  },
  // 图片大小类型
  sizeType: {
    type: Array,
    default: () => ['compressed']
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false
  },
  // 是否自动上传
  autoUpload: {
    type: Boolean,
    default: false
  }
});

/**
 * Component Events
 * @typedef {Object} ImageUploaderEvents
 * @event update:value - Emitted when image list changes (v-model)
 * @event upload - Emitted when upload starts { status: 'start', photos: Array }
 * @event success - Emitted when upload succeeds (Array of image URLs)
 * @event fail - Emitted when upload fails (Error object)
 * @event remove - Emitted when image is removed { index: number, removed: Array }
 * @event choose - Emitted when images are selected (Array of temp paths)
 * @event error - Emitted on various errors { type: string, message: string, error: Error }
 */
const emit = defineEmits([
  'update:value',
  'upload',
  'success',
  'fail',
  'remove',
  'choose',
  'error'
]);

// 内部状态
const photos = ref([...props.value]);
const progress = ref(0);
const showProgress = ref(false);
const isUploading = ref(false);
const uploadingIndex = ref(-1);
// 添加内部更新标志，用于防止递归触发
const isInternalUpdate = ref(false);

// 监听外部value变化
watch(
  () => props.value,
  newVal => {
    // 如果是内部更新触发的props变化，则不需要再次更新photos
    if (!isInternalUpdate.value) {
      photos.value = [...newVal];
    }
  }
);

// 监听内部photos变化
watch(
  () => photos.value,
  newVal => {
    // 标记为内部更新开始
    isInternalUpdate.value = true;
    emit('update:value', newVal);

    // 如果设置了自动上传且有新图片，且不是由上传结果更新photos触发的
    if (props.autoUpload && newVal.length > 0 && !isUploading.value) {
      // 检查是否有本地临时路径的图片（需要上传的图片）
      const hasLocalImages = newVal.some(
        path =>
          path.startsWith('file://') ||
          path.startsWith('wxfile://') ||
          path.startsWith('blob:')
      );
      if (hasLocalImages) {
        uploadImages();
      }
    }

    // 设置延时关闭内部更新标志，确保props的监听器不会再次触发更新
    setTimeout(() => {
      isInternalUpdate.value = false;
    }, 0);
  }
);

// 选择图片
const handleChoose = () => {
  if (props.disabled) return;

  const count = props.maxCount - photos.value.length;
  if (count <= 0) return;

  uni.chooseImage({
    count,
    sizeType: props.sizeType,
    sourceType: props.sourceType,
    success: async res => {
      let processedPaths = res.tempFilePaths;

      // 如果启用了压缩，先对图片进行压缩
      if (props.enableCompression) {
        processedPaths = await Promise.all(
          res.tempFilePaths.map(async path => {
            const compressed = await ImageCompressor.compressImage(path, {
              quality: props.quality
            });
            return compressed.tempFilePath;
          })
        );
      }

      photos.value = [...photos.value, ...processedPaths];
      emit('choose', processedPaths);
    },
    fail: err => {
      if (err.errMsg !== 'chooseImage:fail cancel') {
        const errorMsg = '选择图片失败';
        uni.showToast({
          title: errorMsg,
          icon: 'none'
        });
        emit('error', { type: 'choose', message: errorMsg, error: err });
      }
    }
  });
};

// 预览图片
const handlePreview = index => {
  try {
    const imageUrl = photos.value[index];

    // 如果启用了缓存，先检查缓存
    if (props.enableCache) {
      const cacheKey = imageCache.getCacheKey(imageUrl, 'preview');
      const cached = imageCache.getCache(cacheKey);
      if (cached) {
        uni.previewImage({
          current: cached,
          urls: photos.value
        });
        return;
      }
    }

    uploadApi.previewImage(imageUrl, photos.value);
  } catch (error) {
    emit('error', { type: 'preview', message: '预览图片失败', error });
  }
};

// 移除图片
const handleRemove = index => {
  if (props.disabled) return;

  try {
    const removed = photos.value.splice(index, 1);
    emit('remove', { index, removed });
  } catch (error) {
    emit('error', { type: 'remove', message: '移除图片失败', error });
  }
};

// 上传图片
const uploadImages = async () => {
  if (photos.value.length === 0) {
    return { success: false, message: '请先选择图片' };
  }

  try {
    showProgress.value = true;
    isUploading.value = true;
    progress.value = 0;

    emit('upload', { status: 'start', photos: photos.value });

    // 过滤出需要上传的本地图片
    const localImages = photos.value.filter(
      path =>
        path.startsWith('file://') ||
        path.startsWith('wxfile://') ||
        path.startsWith('blob:')
    );

    // 已经是网络URL的图片
    const networkImages = photos.value.filter(
      path => path.startsWith('http://') || path.startsWith('https://')
    );

    // 如果没有需要上传的图片，直接返回
    if (localImages.length === 0) {
      showProgress.value = false;
      isUploading.value = false;
      return { success: true, data: photos.value };
    }

    // 上传本地图片
    const uploadResult = await uploadApi.uploadImages(
      localImages,
      progressValue => {
        progress.value = progressValue;
      }
    );

    showProgress.value = false;
    isUploading.value = false;

    if (uploadResult.success) {
      // 合并网络图片和新上传的图片URL
      const allImageUrls = [...networkImages, ...uploadResult.data];

      // 更新photos数组，替换本地路径为网络URL
      photos.value = allImageUrls;

      emit('success', allImageUrls);
      return { success: true, data: allImageUrls };
    } else {
      throw new Error('图片上传失败');
    }
  } catch (error) {
    showProgress.value = false;
    isUploading.value = false;
    emit('fail', error);
    return { success: false, message: error.message || '上传失败' };
  }
};

/**
 * Exposed Methods
 * @typedef {Object} ImageUploaderMethods
 * @method uploadImages - Manually trigger image upload
 * @method clear - Clear all images and cache
 * @method getPhotos - Get current photo array
 * @method addPhoto - Add a photo URL programmatically
 * @method clearCache - Clear image cache
 * @method getCacheInfo - Get cache information
 */
// 暴露方法给父组件
defineExpose({
  uploadImages,
  clear: () => {
    photos.value = [];
    // 清理缓存
    if (props.enableCache) {
      imageCache.clearCache();
    }
  },
  getPhotos: () => photos.value,
  addPhoto: photoUrl => {
    if (photos.value.length < props.maxCount) {
      photos.value.push(photoUrl);
      return true;
    }
    return false;
  },
  // 缓存管理方法
  clearCache: () => {
    imageCache.clearCache();
  },
  getCacheInfo: () => {
    return {
      cacheSize: imageCache.cache.size,
      maxCacheSize: imageCache.maxCacheSize
    };
  }
});
</script>

<style lang="scss" scoped>
.image-uploader {
  width: 100%;
}

.photo-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.photo-item {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  overflow: hidden;
  position: relative;
}

.photo-preview {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.delete-photo {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  width: 40rpx;
  height: 40rpx;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 5;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid rgba(255, 255, 255, 0.3);
  border-radius: 50%;
  border-top-color: #fff;
  animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

.add-photo-btn {
  width: 160rpx;
  height: 160rpx;
  background-color: #f5f5f5;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  cursor: pointer;
  border: 2rpx dashed #ddd;
}

.add-photo-btn:active {
  background-color: #f0f0f0;
}

.add-text {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

.tip-text {
  font-size: 24rpx;
  color: #999;
}

.upload-progress-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 999;
}

.upload-progress-container {
  width: 80%;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.progress-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
  margin-bottom: 30rpx;
}

.progress-bar-container {
  width: 100%;
  height: 20rpx;
  background-color: #f5f5f5;
  border-radius: 10rpx;
  overflow: hidden;
  margin-bottom: 20rpx;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(to right, #f6d365, #fda085);
  border-radius: 10rpx;
  transition: width 0.3s;
}

.progress-text {
  font-size: 28rpx;
  color: #666;
}
</style>
