<template>
  <view class="feedback-system">
    <!-- 加载状态反馈 -->
    <view v-if="type === 'loading'" class="feedback-loading" :class="[`loading-${variant}`, animationClass]">
      <view v-if="variant === 'spinner'" class="spinner-container">
        <view class="spinner" :class="sizeClass"></view>
        <text v-if="message" class="feedback-message">{{ message }}</text>
      </view>
      
      <view v-else-if="variant === 'dots'" class="dots-container">
        <view class="dot" v-for="i in 3" :key="i" :style="{ animationDelay: `${i * 0.1}s` }"></view>
        <text v-if="message" class="feedback-message">{{ message }}</text>
      </view>
      
      <view v-else-if="variant === 'pulse'" class="pulse-container">
        <view class="pulse-circle" :class="sizeClass"></view>
        <text v-if="message" class="feedback-message">{{ message }}</text>
      </view>
      
      <view v-else-if="variant === 'skeleton'" class="skeleton-container">
        <view class="skeleton-line" v-for="i in skeletonLines" :key="i" :style="getSkeletonStyle(i)"></view>
      </view>
      
      <view v-else class="progress-container">
        <progress 
          :percent="progress" 
          :stroke-width="4" 
          :active="true"
          :activeColor="progressColor"
          backgroundColor="rgba(0,0,0,0.1)"
        />
        <text v-if="message" class="feedback-message">{{ message }}</text>
      </view>
    </view>

    <!-- 成功状态反馈 -->
    <view v-else-if="type === 'success'" class="feedback-success" :class="animationClass">
      <view class="success-icon" :class="sizeClass">
        <view class="checkmark">
          <view class="checkmark-stem"></view>
          <view class="checkmark-kick"></view>
        </view>
      </view>
      <text v-if="message" class="feedback-message success-message">{{ message }}</text>
    </view>

    <!-- 错误状态反馈 -->
    <view v-else-if="type === 'error'" class="feedback-error" :class="animationClass">
      <view class="error-icon" :class="sizeClass">
        <view class="error-cross">
          <view class="error-line error-line-1"></view>
          <view class="error-line error-line-2"></view>
        </view>
      </view>
      <text v-if="message" class="feedback-message error-message">{{ message }}</text>
      <button v-if="showRetry" @click="handleRetry" class="retry-button">
        重试
      </button>
    </view>

    <!-- 警告状态反馈 -->
    <view v-else-if="type === 'warning'" class="feedback-warning" :class="animationClass">
      <view class="warning-icon" :class="sizeClass">
        <text class="warning-symbol">!</text>
      </view>
      <text v-if="message" class="feedback-message warning-message">{{ message }}</text>
    </view>

    <!-- 信息状态反馈 -->
    <view v-else-if="type === 'info'" class="feedback-info" :class="animationClass">
      <view class="info-icon" :class="sizeClass">
        <text class="info-symbol">i</text>
      </view>
      <text v-if="message" class="feedback-message info-message">{{ message }}</text>
    </view>

    <!-- 空状态反馈 -->
    <view v-else-if="type === 'empty'" class="feedback-empty" :class="animationClass">
      <view class="empty-icon" :class="sizeClass">
        <uni-icons type="inbox" :size="iconSize" color="#ccc" />
      </view>
      <text v-if="message" class="feedback-message empty-message">{{ message || '暂无数据' }}</text>
      <button v-if="showAction" @click="handleAction" class="action-button">
        {{ actionText || '刷新' }}
      </button>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  // 反馈类型
  type: {
    type: String,
    required: true,
    validator: value => ['loading', 'success', 'error', 'warning', 'info', 'empty'].includes(value)
  },
  // 反馈消息
  message: {
    type: String,
    default: ''
  },
  // 加载变体
  variant: {
    type: String,
    default: 'spinner',
    validator: value => ['spinner', 'dots', 'pulse', 'progress', 'skeleton'].includes(value)
  },
  // 尺寸
  size: {
    type: String,
    default: 'medium',
    validator: value => ['small', 'medium', 'large'].includes(value)
  },
  // 进度值 (0-100)
  progress: {
    type: Number,
    default: 0
  },
  // 是否显示重试按钮
  showRetry: {
    type: Boolean,
    default: false
  },
  // 是否显示操作按钮
  showAction: {
    type: Boolean,
    default: false
  },
  // 操作按钮文本
  actionText: {
    type: String,
    default: ''
  },
  // 骨架屏行数
  skeletonLines: {
    type: Number,
    default: 3
  },
  // 自动隐藏时间 (毫秒)
  autoHide: {
    type: Number,
    default: 0
  },
  // 动画类型
  animation: {
    type: String,
    default: 'fade',
    validator: value => ['fade', 'slide', 'bounce', 'zoom'].includes(value)
  },
  // 是否启用触觉反馈
  hapticFeedback: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['retry', 'action', 'hide']);

// 响应式数据
const visible = ref(true);
let hideTimer = null;

// 计算属性
const sizeClass = computed(() => `size-${props.size}`);
const animationClass = computed(() => `animation-${props.animation}`);

const iconSize = computed(() => {
  const sizes = { small: 24, medium: 32, large: 48 };
  return sizes[props.size];
});

const progressColor = computed(() => {
  if (props.progress < 30) return '#ff3b30';
  if (props.progress < 70) return '#ff9500';
  return '#34c759';
});

// 方法
const handleRetry = () => {
  triggerHapticFeedback('light');
  emit('retry');
};

const handleAction = () => {
  triggerHapticFeedback('light');
  emit('action');
};

const triggerHapticFeedback = (type = 'light') => {
  if (!props.hapticFeedback) return;
  
  // uni-app 触觉反馈
  if (typeof uni !== 'undefined' && uni.vibrateShort) {
    try {
      if (type === 'light') {
        uni.vibrateShort({ type: 'light' });
      } else if (type === 'medium') {
        uni.vibrateShort({ type: 'medium' });
      } else if (type === 'heavy') {
        uni.vibrateShort({ type: 'heavy' });
      }
    } catch (e) {
      console.warn('Haptic feedback not supported:', e);
    }
  }
};

const getSkeletonStyle = (index) => {
  const widths = ['100%', '80%', '60%'];
  return {
    width: widths[index - 1] || '100%',
    animationDelay: `${index * 0.1}s`
  };
};

const hide = () => {
  visible.value = false;
  emit('hide');
};

// 生命周期
onMounted(() => {
  // 触发相应的触觉反馈
  if (props.type === 'success') {
    triggerHapticFeedback('light');
  } else if (props.type === 'error') {
    triggerHapticFeedback('heavy');
  } else if (props.type === 'warning') {
    triggerHapticFeedback('medium');
  }

  // 自动隐藏
  if (props.autoHide > 0 && ['success', 'error', 'warning', 'info'].includes(props.type)) {
    hideTimer = setTimeout(hide, props.autoHide);
  }
});

onUnmounted(() => {
  if (hideTimer) {
    clearTimeout(hideTimer);
  }
});

// 暴露方法
defineExpose({
  hide,
  triggerHapticFeedback
});
</script>

<style lang="scss" scoped>
@import '@/styles/responsive.scss';

.feedback-system {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx 20rpx;
  text-align: center;
  
  .feedback-message {
    margin-top: 20rpx;
    font-size: 28rpx;
    color: #666;
    line-height: 1.4;
    
    @include uni-media-up(md) {
      font-size: 30rpx;
    }
  }
}

// 动画类
.animation-fade {
  animation: fadeIn 0.3s ease-in-out;
}

.animation-slide {
  animation: slideUp 0.3s ease-out;
}

.animation-bounce {
  animation: bounceIn 0.5s ease-out;
}

.animation-zoom {
  animation: zoomIn 0.3s ease-out;
}

// 尺寸类
.size-small {
  width: 40rpx;
  height: 40rpx;
  font-size: 20rpx;
}

.size-medium {
  width: 60rpx;
  height: 60rpx;
  font-size: 28rpx;
}

.size-large {
  width: 80rpx;
  height: 80rpx;
  font-size: 36rpx;
}

// 加载状态样式
.feedback-loading {
  .spinner-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .spinner {
      border: 4rpx solid #f3f3f3;
      border-top: 4rpx solid #007aff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }
  
  .dots-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .dot {
      width: 12rpx;
      height: 12rpx;
      border-radius: 50%;
      background-color: #007aff;
      margin: 0 6rpx;
      animation: dotPulse 1.4s ease-in-out infinite both;
      display: inline-block;
      
      &:nth-child(1) { animation-delay: -0.32s; }
      &:nth-child(2) { animation-delay: -0.16s; }
      &:nth-child(3) { animation-delay: 0s; }
    }
  }
  
  .pulse-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    
    .pulse-circle {
      border-radius: 50%;
      background-color: #007aff;
      animation: pulse 2s ease-in-out infinite;
    }
  }
  
  .skeleton-container {
    width: 100%;
    max-width: 400rpx;
    
    .skeleton-line {
      height: 32rpx;
      background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
      background-size: 200% 100%;
      border-radius: 8rpx;
      margin-bottom: 16rpx;
      animation: shimmer 1.5s ease-in-out infinite;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
  
  .progress-container {
    width: 100%;
    max-width: 400rpx;
    
    progress {
      width: 100%;
      height: 8rpx;
    }
  }
}

// 成功状态样式
.feedback-success {
  .success-icon {
    border-radius: 50%;
    background-color: #34c759;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: successPop 0.5s ease-out;
    
    .checkmark {
      position: relative;
      
      .checkmark-stem,
      .checkmark-kick {
        position: absolute;
        background-color: white;
        border-radius: 2rpx;
      }
      
      .checkmark-stem {
        width: 6rpx;
        height: 20rpx;
        top: 10rpx;
        left: 16rpx;
        transform: rotate(45deg);
        animation: checkmarkStem 0.3s ease-out 0.1s both;
      }
      
      .checkmark-kick {
        width: 12rpx;
        height: 6rpx;
        top: 22rpx;
        left: 8rpx;
        transform: rotate(-45deg);
        animation: checkmarkKick 0.2s ease-out both;
      }
    }
  }
  
  .success-message {
    color: #34c759;
    font-weight: 500;
  }
}

// 错误状态样式
.feedback-error {
  .error-icon {
    border-radius: 50%;
    background-color: #ff3b30;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: errorShake 0.5s ease-out;
    
    .error-cross {
      position: relative;
      
      .error-line {
        position: absolute;
        background-color: white;
        border-radius: 2rpx;
        width: 24rpx;
        height: 4rpx;
        top: 50%;
        left: 50%;
        
        &.error-line-1 {
          transform: translate(-50%, -50%) rotate(45deg);
        }
        
        &.error-line-2 {
          transform: translate(-50%, -50%) rotate(-45deg);
        }
      }
    }
  }
  
  .error-message {
    color: #ff3b30;
    font-weight: 500;
  }
  
  .retry-button {
    margin-top: 20rpx;
    padding: 16rpx 32rpx;
    background-color: #ff3b30;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #e6342a;
      transform: translateY(-2rpx);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

// 警告状态样式
.feedback-warning {
  .warning-icon {
    border-radius: 50%;
    background-color: #ff9500;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: warningPulse 0.5s ease-out;
    
    .warning-symbol {
      color: white;
      font-weight: bold;
      font-size: inherit;
    }
  }
  
  .warning-message {
    color: #ff9500;
    font-weight: 500;
  }
}

// 信息状态样式
.feedback-info {
  .info-icon {
    border-radius: 50%;
    background-color: #007aff;
    display: flex;
    align-items: center;
    justify-content: center;
    animation: infoBounce 0.5s ease-out;
    
    .info-symbol {
      color: white;
      font-weight: bold;
      font-size: inherit;
    }
  }
  
  .info-message {
    color: #007aff;
    font-weight: 500;
  }
}

// 空状态样式
.feedback-empty {
  .empty-icon {
    opacity: 0.6;
    animation: emptyFloat 2s ease-in-out infinite;
  }
  
  .empty-message {
    color: #999;
    font-size: 32rpx;
    margin-top: 24rpx;
  }
  
  .action-button {
    margin-top: 24rpx;
    padding: 20rpx 40rpx;
    background-color: #007aff;
    color: white;
    border: none;
    border-radius: 8rpx;
    font-size: 28rpx;
    cursor: pointer;
    transition: all 0.2s ease;
    
    &:hover {
      background-color: #0056d3;
      transform: translateY(-2rpx);
    }
    
    &:active {
      transform: translateY(0);
    }
  }
}

// 动画定义
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

@keyframes slideUp {
  from { 
    opacity: 0;
    transform: translateY(20rpx);
  }
  to { 
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes zoomIn {
  from {
    opacity: 0;
    transform: scale(0.5);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes dotPulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes successPop {
  0% {
    transform: scale(0);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes checkmarkStem {
  0% {
    height: 0;
  }
  100% {
    height: 20rpx;
  }
}

@keyframes checkmarkKick {
  0% {
    width: 0;
  }
  100% {
    width: 12rpx;
  }
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-4rpx);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(4rpx);
  }
}

@keyframes warningPulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes infoBounce {
  0%, 20%, 60%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-8rpx);
  }
  80% {
    transform: translateY(-4rpx);
  }
}

@keyframes emptyFloat {
  0%, 100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-8rpx);
  }
}
</style>