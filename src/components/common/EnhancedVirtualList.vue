<template>
  <view class="enhanced-virtual-list" :style="containerStyle">
    <scroll-view
      scroll-y
      class="virtual-scroll"
      :scroll-top="scrollTop"
      :refresher-enabled="refresherEnabled"
      :refresher-triggered="refresherTriggered"
      :enable-back-to-top="enableBackToTop"
      :scroll-with-animation="scrollWithAnimation"
      @scroll="handleScroll"
      @scrolltolower="handleScrollToLower"
      @refresherrefresh="handleRefresh"
    >
      <!-- 上方占位 -->
      <view
        v-if="startOffset > 0"
        class="virtual-spacer"
        :style="{ height: startOffset + 'px' }"
      />

      <!-- 可见项目 -->
      <view class="virtual-items">
        <view
          v-for="(item, index) in visibleItems"
          :key="getItemKey(item, startIndex + index)"
          class="virtual-item"
          :style="getItemStyle(item, startIndex + index)"
          :data-index="startIndex + index"
        >
          <slot
            :item="item"
            :index="startIndex + index"
            :is-visible="true"
            :is-first="startIndex + index === 0"
            :is-last="startIndex + index === items.length - 1"
          />
        </view>
      </view>

      <!-- 下方占位 -->
      <view
        v-if="endOffset > 0"
        class="virtual-spacer"
        :style="{ height: endOffset + 'px' }"
      />

      <!-- 加载更多 -->
      <view v-if="hasMore && visibleItems.length > 0" class="load-more">
        <uni-load-more :status="loadMoreStatus" />
      </view>

      <!-- 空状态 -->
      <view v-if="items.length === 0 && !loading" class="empty-state">
        <slot name="empty">
          <view class="empty-content">
            <uni-icons type="inbox" size="48" color="#ccc" />
            <text class="empty-text">暂无数据</text>
          </view>
        </slot>
      </view>
    </scroll-view>

    <!-- 滚动指示器 -->
    <view v-if="showScrollIndicator && items.length > 0" class="scroll-indicator">
      <view class="indicator-track">
        <view
          class="indicator-thumb"
          :style="indicatorStyle"
        />
      </view>
    </view>

    <!-- 回到顶部按钮 -->
    <view
      v-if="showBackToTop && currentScrollTop > backToTopThreshold"
      class="back-to-top"
      @click="scrollToTop"
    >
      <uni-icons type="up" size="20" color="#fff" />
    </view>
  </view>
</template><s
cript setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';

const props = defineProps({
  // 数据列表
  items: {
    type: Array,
    default: () => []
  },
  // 每项高度（可以是数字或函数）
  itemHeight: {
    type: [Number, Function],
    default: 200
  },
  // 容器高度
  containerHeight: {
    type: Number,
    default: 600
  },
  // 缓冲区大小（额外渲染的项目数）
  bufferSize: {
    type: Number,
    default: 5
  },
  // 获取项目唯一键的字段或函数
  keyField: {
    type: [String, Function],
    default: 'id'
  },
  // 是否有更多数据
  hasMore: {
    type: Boolean,
    default: false
  },
  // 加载更多状态
  loadMoreStatus: {
    type: String,
    default: 'more'
  },
  // 是否启用下拉刷新
  refresherEnabled: {
    type: Boolean,
    default: true
  },
  // 下拉刷新状态
  refresherTriggered: {
    type: Boolean,
    default: false
  },
  // 是否显示滚动指示器
  showScrollIndicator: {
    type: Boolean,
    default: false
  },
  // 是否显示回到顶部按钮
  showBackToTop: {
    type: Boolean,
    default: true
  },
  // 回到顶部按钮显示阈值
  backToTopThreshold: {
    type: Number,
    default: 500
  },
  // 是否启用回到顶部功能
  enableBackToTop: {
    type: Boolean,
    default: true
  },
  // 是否启用滚动动画
  scrollWithAnimation: {
    type: Boolean,
    default: true
  },
  // 预加载阈值（距离底部多少像素时开始加载）
  preloadThreshold: {
    type: Number,
    default: 200
  },
  // 是否启用动态高度
  dynamicHeight: {
    type: Boolean,
    default: false
  },
  // 最小项目高度（动态高度模式下）
  minItemHeight: {
    type: Number,
    default: 50
  },
  // 最大项目高度（动态高度模式下）
  maxItemHeight: {
    type: Number,
    default: 500
  },
  // 是否加载中
  loading: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['scroll', 'load-more', 'refresh', 'item-visible', 'item-hidden']);

// 响应式数据
const scrollTop = ref(0);
const currentScrollTop = ref(0);
const itemHeights = ref(new Map()); // 存储每个项目的实际高度
const visibleIndexes = ref(new Set()); // 当前可见的项目索引
const intersectionObserver = ref(null);

// 计算属性
const containerStyle = computed(() => ({
  height: `${props.containerHeight}px`,
  position: 'relative'
}));

// 获取项目高度
const getItemHeight = (item, index) => {
  if (props.dynamicHeight && itemHeights.value.has(index)) {
    return itemHeights.value.get(index);
  }
  
  if (typeof props.itemHeight === 'function') {
    return props.itemHeight(item, index);
  }
  
  return props.itemHeight;
};

// 计算总高度
const totalHeight = computed(() => {
  if (props.dynamicHeight) {
    let height = 0;
    for (let i = 0; i < props.items.length; i++) {
      height += getItemHeight(props.items[i], i);
    }
    return height;
  }
  
  return props.items.length * (typeof props.itemHeight === 'number' ? props.itemHeight : 200);
});

// 计算可见区域
const visibleCount = computed(() => {
  const avgHeight = typeof props.itemHeight === 'number' ? props.itemHeight : 200;
  return Math.ceil(props.containerHeight / avgHeight) + props.bufferSize * 2;
});

// 计算开始索引
const startIndex = computed(() => {
  if (props.dynamicHeight) {
    return getStartIndexForDynamicHeight();
  }
  
  const avgHeight = typeof props.itemHeight === 'number' ? props.itemHeight : 200;
  const index = Math.floor(currentScrollTop.value / avgHeight) - props.bufferSize;
  return Math.max(0, index);
});

// 计算结束索引
const endIndex = computed(() => {
  const index = startIndex.value + visibleCount.value;
  return Math.min(props.items.length, index);
});

// 获取动态高度模式下的开始索引
const getStartIndexForDynamicHeight = () => {
  let accumulatedHeight = 0;
  let index = 0;
  
  for (let i = 0; i < props.items.length; i++) {
    const itemHeight = getItemHeight(props.items[i], i);
    
    if (accumulatedHeight + itemHeight > currentScrollTop.value - props.bufferSize * 100) {
      index = Math.max(0, i - props.bufferSize);
      break;
    }
    
    accumulatedHeight += itemHeight;
  }
  
  return index;
};

// 计算开始偏移
const startOffset = computed(() => {
  if (props.dynamicHeight) {
    let offset = 0;
    for (let i = 0; i < startIndex.value; i++) {
      offset += getItemHeight(props.items[i], i);
    }
    return offset;
  }
  
  const avgHeight = typeof props.itemHeight === 'number' ? props.itemHeight : 200;
  return startIndex.value * avgHeight;
});

// 计算结束偏移
const endOffset = computed(() => {
  if (props.dynamicHeight) {
    let offset = 0;
    for (let i = endIndex.value; i < props.items.length; i++) {
      offset += getItemHeight(props.items[i], i);
    }
    return offset;
  }
  
  const avgHeight = typeof props.itemHeight === 'number' ? props.itemHeight : 200;
  const remainingItems = props.items.length - endIndex.value;
  return remainingItems * avgHeight;
});

// 可见项目
const visibleItems = computed(() => {
  return props.items.slice(startIndex.value, endIndex.value);
});

// 滚动指示器样式
const indicatorStyle = computed(() => {
  const scrollPercent = currentScrollTop.value / (totalHeight.value - props.containerHeight);
  const thumbHeight = Math.max(20, (props.containerHeight / totalHeight.value) * 100);
  const thumbTop = Math.min(100 - thumbHeight, scrollPercent * (100 - thumbHeight));
  
  return {
    height: `${thumbHeight}%`,
    top: `${thumbTop}%`
  };
});

// 获取项目键值
const getItemKey = (item, index) => {
  if (typeof props.keyField === 'function') {
    return props.keyField(item, index);
  }
  
  if (typeof item === 'object' && item !== null) {
    return item[props.keyField] || index;
  }
  
  return index;
};

// 获取项目样式
const getItemStyle = (item, index) => {
  const height = getItemHeight(item, index);
  
  return {
    height: `${height}px`,
    minHeight: props.dynamicHeight ? `${props.minItemHeight}px` : undefined,
    maxHeight: props.dynamicHeight ? `${props.maxItemHeight}px` : undefined
  };
};

// 事件处理
const handleScroll = (e) => {
  const { scrollTop: newScrollTop } = e.detail;
  currentScrollTop.value = newScrollTop;
  
  // 检查可见性变化
  updateVisibleItems();
  
  // 预加载检查
  if (props.hasMore && !props.loading) {
    const scrollBottom = newScrollTop + props.containerHeight;
    const totalScrollHeight = totalHeight.value;
    
    if (totalScrollHeight - scrollBottom <= props.preloadThreshold) {
      emit('load-more');
    }
  }
  
  emit('scroll', e);
};

const handleScrollToLower = () => {
  if (props.hasMore && props.loadMoreStatus !== 'loading') {
    emit('load-more');
  }
};

const handleRefresh = () => {
  emit('refresh');
};

// 更新可见项目
const updateVisibleItems = () => {
  const newVisibleIndexes = new Set();
  
  for (let i = startIndex.value; i < endIndex.value; i++) {
    newVisibleIndexes.add(i);
  }
  
  // 检查新出现的项目
  for (const index of newVisibleIndexes) {
    if (!visibleIndexes.value.has(index)) {
      emit('item-visible', props.items[index], index);
    }
  }
  
  // 检查消失的项目
  for (const index of visibleIndexes.value) {
    if (!newVisibleIndexes.has(index)) {
      emit('item-hidden', props.items[index], index);
    }
  }
  
  visibleIndexes.value = newVisibleIndexes;
};

// 滚动到顶部
const scrollToTop = () => {
  scrollTo(0);
};

// 滚动到指定位置
const scrollTo = (top) => {
  scrollTop.value = top;
  currentScrollTop.value = top;
  
  nextTick(() => {
    scrollTop.value = 0; // 重置以允许下次滚动
  });
};

// 滚动到指定项目
const scrollToItem = (index) => {
  if (index < 0 || index >= props.items.length) return;
  
  let targetScrollTop = 0;
  
  if (props.dynamicHeight) {
    for (let i = 0; i < index; i++) {
      targetScrollTop += getItemHeight(props.items[i], i);
    }
  } else {
    const avgHeight = typeof props.itemHeight === 'number' ? props.itemHeight : 200;
    targetScrollTop = index * avgHeight;
  }
  
  scrollTo(targetScrollTop);
};

// 更新项目高度（动态高度模式）
const updateItemHeight = (index, height) => {
  if (props.dynamicHeight) {
    itemHeights.value.set(index, Math.max(props.minItemHeight, Math.min(props.maxItemHeight, height)));
  }
};

// 设置交叉观察器（用于动态高度测量）
const setupIntersectionObserver = () => {
  if (props.dynamicHeight && typeof uni !== 'undefined' && uni.createIntersectionObserver) {
    intersectionObserver.value = uni.createIntersectionObserver();
    
    intersectionObserver.value
      .relativeToViewport()
      .observe('.virtual-item', (res) => {
        if (res.dataset && res.dataset.index) {
          const index = parseInt(res.dataset.index);
          const height = res.boundingClientRect.height;
          
          if (height > 0) {
            updateItemHeight(index, height);
          }
        }
      });
  }
};

// 清理观察器
const cleanupObserver = () => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
    intersectionObserver.value = null;
  }
};

// 重置状态
const reset = () => {
  currentScrollTop.value = 0;
  scrollTop.value = 0;
  itemHeights.value.clear();
  visibleIndexes.value.clear();
};

// 刷新列表
const refresh = () => {
  reset();
  nextTick(() => {
    updateVisibleItems();
  });
};

// 监听数据变化
watch(
  () => props.items,
  (newItems, oldItems) => {
    if (newItems.length !== oldItems?.length) {
      // 数据长度变化时重置高度缓存
      if (props.dynamicHeight) {
        itemHeights.value.clear();
      }
      
      nextTick(() => {
        updateVisibleItems();
      });
    }
  },
  { deep: false }
);

// 生命周期
onMounted(() => {
  setupIntersectionObserver();
  updateVisibleItems();
});

onUnmounted(() => {
  cleanupObserver();
});

// 暴露方法
defineExpose({
  scrollTo,
  scrollToItem,
  scrollToTop,
  updateItemHeight,
  reset,
  refresh,
  getVisibleItems: () => visibleItems.value,
  getVisibleIndexes: () => Array.from(visibleIndexes.value)
});
</script><s
tyle lang="scss" scoped>
@import '@/styles/responsive.scss';

.enhanced-virtual-list {
  position: relative;
  overflow: hidden;

  .virtual-scroll {
    height: 100%;
    width: 100%;
  }

  .virtual-spacer {
    width: 100%;
    flex-shrink: 0;
  }

  .virtual-items {
    .virtual-item {
      width: 100%;
      overflow: hidden;
      box-sizing: border-box;
    }
  }

  .load-more {
    padding: 20rpx;
    text-align: center;
  }

  .empty-state {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 400rpx;
    padding: 40rpx;

    .empty-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 16rpx;

      .empty-text {
        font-size: 28rpx;
        color: #999;
      }
    }
  }

  .scroll-indicator {
    position: absolute;
    right: 4rpx;
    top: 0;
    bottom: 0;
    width: 8rpx;
    z-index: 10;
    pointer-events: none;

    .indicator-track {
      position: relative;
      width: 100%;
      height: 100%;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4rpx;

      .indicator-thumb {
        position: absolute;
        width: 100%;
        background: rgba(0, 0, 0, 0.3);
        border-radius: 4rpx;
        transition: background-color 0.2s ease;
      }
    }

    &:hover .indicator-thumb {
      background: rgba(0, 0, 0, 0.5);
    }
  }

  .back-to-top {
    position: absolute;
    right: 20rpx;
    bottom: 20rpx;
    width: 80rpx;
    height: 80rpx;
    background: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 20;
    cursor: pointer;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);

    &:hover {
      background: rgba(0, 0, 0, 0.8);
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// 响应式适配
@include uni-media-up(md) {
  .enhanced-virtual-list {
    .scroll-indicator {
      width: 12rpx;
      right: 8rpx;

      .indicator-track {
        border-radius: 6rpx;

        .indicator-thumb {
          border-radius: 6rpx;
        }
      }
    }

    .back-to-top {
      width: 100rpx;
      height: 100rpx;
      right: 40rpx;
      bottom: 40rpx;
    }
  }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  .enhanced-virtual-list {
    .empty-state {
      .empty-content {
        .empty-text {
          color: #ccc;
        }
      }
    }

    .scroll-indicator {
      .indicator-track {
        background: rgba(255, 255, 255, 0.1);

        .indicator-thumb {
          background: rgba(255, 255, 255, 0.3);
        }
      }

      &:hover .indicator-thumb {
        background: rgba(255, 255, 255, 0.5);
      }
    }

    .back-to-top {
      background: rgba(255, 255, 255, 0.1);

      &:hover {
        background: rgba(255, 255, 255, 0.2);
      }
    }
  }
}
</style>