<template>
  <view class="toast-system">
    <transition-group name="toast" tag="view" class="toast-container" :class="positionClass">
      <view
        v-for="toast in toasts"
        :key="toast.id"
        class="toast-item"
        :class="[
          `toast-${toast.type}`,
          `toast-${toast.size}`,
          { 'toast-closable': toast.closable }
        ]"
        @click="handleToastClick(toast)"
      >
        <!-- 图标 -->
        <view class="toast-icon" v-if="toast.showIcon">
          <uni-icons 
            :type="getIconType(toast.type)" 
            :size="getIconSize(toast.size)" 
            :color="getIconColor(toast.type)" 
          />
        </view>

        <!-- 内容 -->
        <view class="toast-content">
          <text class="toast-title" v-if="toast.title">{{ toast.title }}</text>
          <text class="toast-message">{{ toast.message }}</text>
        </view>

        <!-- 关闭按钮 -->
        <view 
          v-if="toast.closable" 
          class="toast-close" 
          @click.stop="removeToast(toast.id)"
        >
          <uni-icons type="close" size="16" color="#999" />
        </view>

        <!-- 进度条 -->
        <view 
          v-if="toast.showProgress && toast.duration > 0" 
          class="toast-progress"
          :style="{ animationDuration: `${toast.duration}ms` }"
        ></view>
      </view>
    </transition-group>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';

const props = defineProps({
  // 位置
  position: {
    type: String,
    default: 'top',
    validator: value => ['top', 'bottom', 'center', 'top-left', 'top-right', 'bottom-left', 'bottom-right'].includes(value)
  },
  // 最大显示数量
  maxCount: {
    type: Number,
    default: 5
  },
  // 默认持续时间
  defaultDuration: {
    type: Number,
    default: 3000
  }
});

const emit = defineEmits(['click', 'close']);

// 响应式数据
const toasts = ref([]);
let toastId = 0;

// 计算属性
const positionClass = computed(() => `toast-position-${props.position}`);

// 方法
const show = (options) => {
  const toast = {
    id: ++toastId,
    type: options.type || 'info',
    title: options.title || '',
    message: options.message || '',
    duration: options.duration !== undefined ? options.duration : props.defaultDuration,
    closable: options.closable !== undefined ? options.closable : true,
    showIcon: options.showIcon !== undefined ? options.showIcon : true,
    showProgress: options.showProgress !== undefined ? options.showProgress : true,
    size: options.size || 'medium',
    onClick: options.onClick,
    onClose: options.onClose,
    ...options
  };

  // 限制最大数量
  if (toasts.value.length >= props.maxCount) {
    toasts.value.shift();
  }

  toasts.value.push(toast);

  // 触觉反馈
  triggerHapticFeedback(toast.type);

  // 自动移除
  if (toast.duration > 0) {
    setTimeout(() => {
      removeToast(toast.id);
    }, toast.duration);
  }

  return toast.id;
};

const removeToast = (id) => {
  const index = toasts.value.findIndex(toast => toast.id === id);
  if (index > -1) {
    const toast = toasts.value[index];
    toasts.value.splice(index, 1);
    
    if (toast.onClose) {
      toast.onClose();
    }
    
    emit('close', toast);
  }
};

const clear = () => {
  toasts.value = [];
};

const handleToastClick = (toast) => {
  if (toast.onClick) {
    toast.onClick();
  }
  emit('click', toast);
};

const getIconType = (type) => {
  const iconMap = {
    success: 'checkmarkempty',
    error: 'closeempty',
    warning: 'info',
    info: 'info',
    loading: 'spinner'
  };
  return iconMap[type] || 'info';
};

const getIconSize = (size) => {
  const sizeMap = {
    small: 16,
    medium: 20,
    large: 24
  };
  return sizeMap[size] || 20;
};

const getIconColor = (type) => {
  const colorMap = {
    success: '#34c759',
    error: '#ff3b30',
    warning: '#ff9500',
    info: '#007aff',
    loading: '#007aff'
  };
  return colorMap[type] || '#007aff';
};

const triggerHapticFeedback = (type) => {
  if (typeof uni !== 'undefined' && uni.vibrateShort) {
    try {
      let feedbackType = 'light';
      if (type === 'error') feedbackType = 'heavy';
      else if (type === 'warning') feedbackType = 'medium';
      
      uni.vibrateShort({ type: feedbackType });
    } catch (e) {
      console.warn('Haptic feedback not supported:', e);
    }
  }
};

// 便捷方法
const success = (message, options = {}) => {
  return show({ ...options, type: 'success', message });
};

const error = (message, options = {}) => {
  return show({ ...options, type: 'error', message });
};

const warning = (message, options = {}) => {
  return show({ ...options, type: 'warning', message });
};

const info = (message, options = {}) => {
  return show({ ...options, type: 'info', message });
};

const loading = (message, options = {}) => {
  return show({ ...options, type: 'loading', message, duration: 0 });
};

// 暴露方法
defineExpose({
  show,
  success,
  error,
  warning,
  info,
  loading,
  removeToast,
  clear
});
</script>

<style lang="scss" scoped>
@import '@/styles/responsive.scss';

.toast-system {
  position: fixed;
  z-index: 9999;
  pointer-events: none;
  
  .toast-container {
    position: fixed;
    display: flex;
    flex-direction: column;
    gap: 16rpx;
    padding: 20rpx;
    
    &.toast-position-top {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
      align-items: center;
    }
    
    &.toast-position-bottom {
      bottom: 0;
      left: 50%;
      transform: translateX(-50%);
      align-items: center;
    }
    
    &.toast-position-center {
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      align-items: center;
    }
    
    &.toast-position-top-left {
      top: 0;
      left: 0;
      align-items: flex-start;
    }
    
    &.toast-position-top-right {
      top: 0;
      right: 0;
      align-items: flex-end;
    }
    
    &.toast-position-bottom-left {
      bottom: 0;
      left: 0;
      align-items: flex-start;
    }
    
    &.toast-position-bottom-right {
      bottom: 0;
      right: 0;
      align-items: flex-end;
    }
  }
}

.toast-item {
  position: relative;
  display: flex;
  align-items: flex-start;
  min-width: 200rpx;
  max-width: 600rpx;
  padding: 24rpx;
  border-radius: 12rpx;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  pointer-events: auto;
  overflow: hidden;
  
  @include uni-media-up(md) {
    min-width: 300rpx;
    max-width: 500rpx;
  }
  
  &.toast-small {
    padding: 16rpx 20rpx;
    min-width: 160rpx;
    
    .toast-content {
      .toast-message {
        font-size: 24rpx;
      }
    }
  }
  
  &.toast-large {
    padding: 32rpx;
    min-width: 240rpx;
    
    .toast-content {
      .toast-title {
        font-size: 32rpx;
      }
      
      .toast-message {
        font-size: 30rpx;
      }
    }
  }
  
  &.toast-closable {
    padding-right: 60rpx;
  }
  
  // 类型样式
  &.toast-success {
    border-left: 6rpx solid #34c759;
    
    .toast-icon {
      color: #34c759;
    }
  }
  
  &.toast-error {
    border-left: 6rpx solid #ff3b30;
    
    .toast-icon {
      color: #ff3b30;
    }
  }
  
  &.toast-warning {
    border-left: 6rpx solid #ff9500;
    
    .toast-icon {
      color: #ff9500;
    }
  }
  
  &.toast-info {
    border-left: 6rpx solid #007aff;
    
    .toast-icon {
      color: #007aff;
    }
  }
  
  &.toast-loading {
    border-left: 6rpx solid #007aff;
    
    .toast-icon {
      color: #007aff;
      animation: spin 1s linear infinite;
    }
  }
}

.toast-icon {
  flex-shrink: 0;
  margin-right: 16rpx;
  margin-top: 2rpx;
}

.toast-content {
  flex: 1;
  min-width: 0;
  
  .toast-title {
    display: block;
    font-size: 28rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 8rpx;
    line-height: 1.3;
  }
  
  .toast-message {
    display: block;
    font-size: 26rpx;
    color: #666;
    line-height: 1.4;
    word-break: break-word;
  }
}

.toast-close {
  position: absolute;
  top: 16rpx;
  right: 16rpx;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border-radius: 50%;
  transition: background-color 0.2s ease;
  
  &:hover {
    background-color: rgba(0, 0, 0, 0.05);
  }
  
  &:active {
    background-color: rgba(0, 0, 0, 0.1);
  }
}

.toast-progress {
  position: absolute;
  bottom: 0;
  left: 0;
  height: 4rpx;
  background: linear-gradient(90deg, #007aff, #5ac8fa);
  border-radius: 0 0 12rpx 12rpx;
  animation: progressShrink linear forwards;
}

// 过渡动画
.toast-enter-active {
  transition: all 0.3s ease-out;
}

.toast-leave-active {
  transition: all 0.3s ease-in;
}

.toast-enter-from {
  opacity: 0;
  transform: translateY(-20rpx) scale(0.9);
}

.toast-leave-to {
  opacity: 0;
  transform: translateY(-20rpx) scale(0.9);
}

.toast-move {
  transition: transform 0.3s ease;
}

// 动画定义
@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

@keyframes progressShrink {
  0% {
    width: 100%;
  }
  100% {
    width: 0%;
  }
}

// 响应式适配
@include uni-media-up(md) {
  .toast-container {
    padding: 40rpx;
    gap: 20rpx;
  }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  .toast-item {
    background: rgba(44, 44, 46, 0.95);
    
    .toast-content {
      .toast-title {
        color: #fff;
      }
      
      .toast-message {
        color: #ccc;
      }
    }
  }
}
</style>