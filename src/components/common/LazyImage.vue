<template>
  <view class="lazy-image-container" :style="containerStyle">
    <!-- 占位符 -->
    <view
      v-if="!loaded && !error"
      class="image-placeholder"
      :class="[`loading-${loadingAnimation}`, { 'is-loading': loading }]"
      :style="placeholderStyle"
    >
      <view v-if="showPlaceholder" class="placeholder-content">
        <uni-icons v-if="!loading" type="image" size="24" color="#ccc" />
        
        <!-- 脉冲动画 -->
        <view v-else-if="loadingAnimation === 'pulse'" class="pulse-loader">
          <view class="pulse-dot"></view>
          <view class="pulse-dot"></view>
          <view class="pulse-dot"></view>
        </view>
        
        <!-- 微光动画 -->
        <view v-else-if="loadingAnimation === 'shimmer'" class="shimmer-loader">
          <view class="shimmer-line"></view>
        </view>
        
        <!-- 旋转动画 -->
        <view v-else-if="loadingAnimation === 'spinner'" class="spinner-loader">
          <view class="spinner"></view>
        </view>
        
        <!-- 默认加载器 -->
        <uni-load-more
          v-else
          status="loading"
          :content-text="{
            contentdown: '',
            contentrefresh: '',
            contentnomore: ''
          }"
        />
      </view>
    </view>

    <!-- 实际图片 -->
    <image
      v-show="loaded && !error"
      :src="currentSrc"
      :mode="mode"
      :lazy-load="true"
      :fade-show="fadeShow"
      :webp="webp"
      class="lazy-image"
      :style="imageStyle"
      @load="handleLoad"
      @error="handleError"
    />

    <!-- 错误状态 -->
    <view
      v-if="error"
      class="image-error"
      :style="placeholderStyle"
      @click="retry"
    >
      <view class="error-content">
        <uni-icons type="close" size="24" color="#ccc" />
        <text class="error-text">加载失败，点击重试</text>
      </view>
    </view>

    <!-- 加载进度 -->
    <view v-if="showProgress && loading && !error" class="loading-progress">
      <progress
        :percent="loadProgress"
        stroke-width="2"
        activeColor="#fda085"
        backgroundColor="rgba(255,255,255,0.3)"
      />
    </view>
  </view>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue';
import imageOptimizer, { generateResponsiveUrl, getNetworkInfo, getDeviceInfo } from '@/utils/imageOptimization';

// 图片缓存管理器
class ImageCacheManager {
  constructor() {
    this.cache = new Map();
    this.preloadQueue = new Set();
    this.maxCacheSize = 100; // 最大缓存数量
    this.networkInfo = getNetworkInfo();
    this.deviceInfo = getDeviceInfo();
    this.setupNetworkListener();
  }

  // 设置网络监听
  setupNetworkListener() {
    if (navigator.connection) {
      navigator.connection.addEventListener('change', () => {
        this.networkInfo = getNetworkInfo();
      });
    }
  }

  // 检查图片是否已缓存
  isCached(src) {
    return this.cache.has(src);
  }

  // 添加到缓存
  addToCache(src, status = 'loaded') {
    if (this.cache.size >= this.maxCacheSize) {
      // 删除最旧的缓存项
      const firstKey = this.cache.keys().next().value;
      this.cache.delete(firstKey);
    }
    this.cache.set(src, {
      status,
      timestamp: Date.now()
    });
  }

  // 预加载图片
  preload(src, options = {}) {
    if (this.isCached(src) || this.preloadQueue.has(src)) {
      return Promise.resolve();
    }

    this.preloadQueue.add(src);
    
    // 根据网络条件决定是否预加载
    if (this.networkInfo.saveData || this.networkInfo.effectiveType === 'slow-2g') {
      this.preloadQueue.delete(src);
      return Promise.resolve(); // 跳过预加载
    }
    
    return new Promise((resolve, reject) => {
      const img = new Image();
      
      // 设置优化后的图片源
      const optimizedSrc = this.getOptimizedSrc(src, options);
      
      img.onload = () => {
        this.addToCache(src, 'loaded');
        this.preloadQueue.delete(src);
        resolve();
      };
      img.onerror = () => {
        this.addToCache(src, 'error');
        this.preloadQueue.delete(src);
        reject();
      };
      img.src = optimizedSrc;
    });
  }

  // 获取优化后的图片源
  getOptimizedSrc(src, options = {}) {
    const { width, height } = options;
    
    // 根据网络和设备条件生成响应式URL
    return generateResponsiveUrl(src, {
      width,
      height,
      quality: imageOptimizer.getBestQuality(this.networkInfo),
      format: imageOptimizer.getBestFormat()
    });
  }

  // 批量预加载
  preloadBatch(srcList) {
    return Promise.allSettled(
      srcList.map(src => this.preload(src))
    );
  }

  // 清理过期缓存
  cleanup(maxAge = 30 * 60 * 1000) { // 30分钟
    const now = Date.now();
    for (const [src, data] of this.cache.entries()) {
      if (now - data.timestamp > maxAge) {
        this.cache.delete(src);
      }
    }
  }
}

// 全局缓存管理器实例
const imageCache = new ImageCacheManager();

const props = defineProps({
  // 图片源
  src: {
    type: String,
    required: true
  },
  // 占位图片
  placeholder: {
    type: String,
    default: ''
  },
  // 错误时的备用图片
  fallback: {
    type: String,
    default: ''
  },
  // 图片模式
  mode: {
    type: String,
    default: 'aspectFill'
  },
  // 宽度
  width: {
    type: [String, Number],
    default: '100%'
  },
  // 高度
  height: {
    type: [String, Number],
    default: 'auto'
  },
  // 是否显示占位符
  showPlaceholder: {
    type: Boolean,
    default: true
  },
  // 是否显示加载进度
  showProgress: {
    type: Boolean,
    default: false
  },
  // 是否启用淡入效果
  fadeShow: {
    type: Boolean,
    default: true
  },
  // 是否启用webp
  webp: {
    type: Boolean,
    default: true
  },
  // 重试次数
  maxRetries: {
    type: Number,
    default: 3
  },
  // 延迟加载阈值（像素）
  threshold: {
    type: Number,
    default: 100
  },
  // 是否立即加载
  immediate: {
    type: Boolean,
    default: false
  },
  // 预加载相关图片列表
  preloadList: {
    type: Array,
    default: () => []
  },
  // 是否启用缓存
  enableCache: {
    type: Boolean,
    default: true
  },
  // 加载动画类型
  loadingAnimation: {
    type: String,
    default: 'pulse', // pulse, shimmer, spinner
    validator: value => ['pulse', 'shimmer', 'spinner'].includes(value)
  },
  // 是否启用自动优化
  autoOptimize: {
    type: Boolean,
    default: true
  },
  // 图片质量
  quality: {
    type: Number,
    default: null // null表示自动选择
  },
  // 强制格式
  format: {
    type: String,
    default: null // null表示自动选择
  },
  // 是否启用智能缓存
  smartCache: {
    type: Boolean,
    default: true
  },
  // 网络自适应
  networkAdaptive: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['load', 'error', 'retry', 'cache-hit']);

// 响应式数据
const loaded = ref(false);
const loading = ref(false);
const error = ref(false);
const retryCount = ref(0);
const loadProgress = ref(0);
const currentSrc = ref('');
const intersectionObserver = ref(null);
const imageElement = ref(null);

// 计算属性
const containerStyle = computed(() => ({
  width: typeof props.width === 'number' ? `${props.width}px` : props.width,
  height: typeof props.height === 'number' ? `${props.height}px` : props.height,
  position: 'relative',
  overflow: 'hidden'
}));

const placeholderStyle = computed(() => ({
  width: '100%',
  height: '100%',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: '#f5f5f5',
  position: 'absolute',
  top: 0,
  left: 0
}));

const imageStyle = computed(() => ({
  width: '100%',
  height: '100%',
  transition: props.fadeShow ? 'opacity 0.3s ease' : 'none'
}));

// 开始加载图片
const startLoad = () => {
  if (loading.value || loaded.value) return;

  // 检查缓存
  if (props.enableCache && imageCache.isCached(props.src)) {
    const cacheData = imageCache.cache.get(props.src);
    if (cacheData.status === 'loaded') {
      currentSrc.value = getOptimizedImageSrc();
      loaded.value = true;
      emit('cache-hit', props.src);
      return;
    } else if (cacheData.status === 'error') {
      error.value = true;
      return;
    }
  }

  loading.value = true;
  error.value = false;
  loadProgress.value = 0;

  // 设置优化后的图片源
  currentSrc.value = getOptimizedImageSrc();

  // 模拟加载进度（实际项目中可以通过其他方式获取真实进度）
  if (props.showProgress) {
    const progressInterval = setInterval(() => {
      if (loadProgress.value < 90) {
        loadProgress.value += Math.random() * 20;
      } else {
        clearInterval(progressInterval);
      }
    }, 100);
  }

  // 启动预加载
  if (props.preloadList.length > 0) {
    setTimeout(() => {
      const preloadOptions = {
        width: containerStyle.value.width,
        height: containerStyle.value.height
      };
      imageCache.preloadBatch(props.preloadList.map(src => ({ src, options: preloadOptions })));
    }, 500); // 延迟预加载，避免影响当前图片加载
  }
};

// 获取优化后的图片源
const getOptimizedImageSrc = () => {
  if (!props.autoOptimize) {
    return props.src;
  }

  const networkInfo = props.networkAdaptive ? getNetworkInfo() : {};
  const deviceInfo = getDeviceInfo();

  // 计算目标尺寸
  const containerWidth = parseInt(containerStyle.value.width) || 300;
  const containerHeight = parseInt(containerStyle.value.height) || 200;

  const optimizedSize = imageOptimizer.calculateOptimalSize({
    containerWidth,
    containerHeight,
    maxWidth: 1920,
    maxHeight: 1080
  });

  // 生成优化URL
  return generateResponsiveUrl(props.src, {
    width: optimizedSize.width,
    height: optimizedSize.height,
    quality: props.quality || imageOptimizer.getBestQuality(networkInfo),
    format: props.format || imageOptimizer.getBestFormat()
  });
};

// 图片加载成功
const handleLoad = e => {
  loaded.value = true;
  loading.value = false;
  loadProgress.value = 100;
  retryCount.value = 0;
  
  // 添加到缓存
  if (props.enableCache) {
    imageCache.addToCache(props.src, 'loaded');
  }
  
  emit('load', e);
};

// 图片加载失败
const handleError = e => {
  loading.value = false;

  // 尝试使用备用图片
  if (props.fallback && currentSrc.value !== props.fallback) {
    currentSrc.value = props.fallback;
    return;
  }

  // 自动重试
  if (retryCount.value < props.maxRetries) {
    retryCount.value++;
    setTimeout(() => {
      startLoad();
    }, 1000 * retryCount.value); // 递增延迟重试
    return;
  }

  // 最终失败
  error.value = true;
  
  // 添加错误状态到缓存
  if (props.enableCache) {
    imageCache.addToCache(props.src, 'error');
  }
  
  emit('error', e);
};

// 手动重试
const retry = () => {
  retryCount.value = 0;
  error.value = false;
  startLoad();
  emit('retry');
};

// 设置交叉观察器
const setupIntersectionObserver = () => {
  // 在小程序环境中使用 createIntersectionObserver
  if (typeof uni !== 'undefined' && uni.createIntersectionObserver) {
    intersectionObserver.value = uni.createIntersectionObserver();
    intersectionObserver.value
      .relativeToViewport({ bottom: props.threshold })
      .observe('.lazy-image-container', res => {
        if (res.intersectionRatio > 0 && !loaded.value && !loading.value) {
          startLoad();
        }
      });
  }
};

// 清理观察器
const cleanupObserver = () => {
  if (intersectionObserver.value) {
    intersectionObserver.value.disconnect();
    intersectionObserver.value = null;
  }
};

// 监听src变化
watch(
  () => props.src,
  newSrc => {
    if (newSrc && newSrc !== currentSrc.value) {
      loaded.value = false;
      loading.value = false;
      error.value = false;
      retryCount.value = 0;

      if (props.immediate) {
        startLoad();
      }
    }
  }
);

// 定期清理缓存
let cleanupTimer = null;

// 生命周期
onMounted(() => {
  if (props.immediate) {
    startLoad();
  } else {
    setupIntersectionObserver();
  }
  
  // 启动缓存清理定时器
  cleanupTimer = setInterval(() => {
    imageCache.cleanup();
  }, 5 * 60 * 1000); // 每5分钟清理一次
});

onUnmounted(() => {
  cleanupObserver();
  if (cleanupTimer) {
    clearInterval(cleanupTimer);
  }
});

// 暴露方法和缓存管理器
defineExpose({
  retry,
  startLoad,
  preloadImages: (srcList) => imageCache.preloadBatch(srcList),
  clearCache: () => imageCache.cache.clear(),
  getCacheSize: () => imageCache.cache.size
});
</script>

<style lang="scss" scoped>
.lazy-image-container {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  overflow: hidden;

  .image-placeholder,
  .image-error {
    .placeholder-content,
    .error-content {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16rpx;

      .error-text {
        font-size: 24rpx;
        color: #999;
        text-align: center;
      }
    }
  }

  .lazy-image {
    display: block;
    border-radius: inherit;
  }

  .loading-progress {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10rpx;
    background: linear-gradient(to top, rgba(0, 0, 0, 0.5), transparent);
  }
}

// 加载动画样式
.image-placeholder {
  // 脉冲动画
  &.loading-pulse.is-loading {
    .pulse-loader {
      display: flex;
      gap: 8rpx;
      
      .pulse-dot {
        width: 12rpx;
        height: 12rpx;
        border-radius: 50%;
        background-color: #ccc;
        animation: pulse 1.4s ease-in-out infinite both;
        
        &:nth-child(1) { animation-delay: -0.32s; }
        &:nth-child(2) { animation-delay: -0.16s; }
        &:nth-child(3) { animation-delay: 0s; }
      }
    }
  }
  
  // 微光动画
  &.loading-shimmer.is-loading {
    position: relative;
    overflow: hidden;
    
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.6),
        transparent
      );
      animation: shimmer 1.5s infinite;
    }
    
    .shimmer-loader {
      width: 80%;
      height: 4rpx;
      background-color: #e0e0e0;
      border-radius: 2rpx;
      overflow: hidden;
      
      .shimmer-line {
        width: 100%;
        height: 100%;
        background-color: #f0f0f0;
      }
    }
  }
  
  // 旋转动画
  &.loading-spinner.is-loading {
    .spinner-loader {
      .spinner {
        width: 32rpx;
        height: 32rpx;
        border: 4rpx solid #f3f3f3;
        border-top: 4rpx solid #ccc;
        border-radius: 50%;
        animation: spin 1s linear infinite;
      }
    }
  }
}

.image-error {
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f0f0f0;
  }

  &:active {
    background-color: #e8e8e8;
  }
}

// 动画定义
@keyframes pulse {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes shimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
