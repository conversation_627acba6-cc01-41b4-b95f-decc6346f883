<template>
  <view class="data-sync-manager">
    <!-- 同步状态指示器 -->
    <view v-if="showSyncIndicator" class="sync-indicator" :class="syncStatusClass">
      <uni-icons :type="syncIcon" :color="syncIconColor" size="16" />
      <text class="sync-text">{{ syncStatusText }}</text>
    </view>

    <!-- 错误提示 -->
    <view v-if="showErrorToast && syncError" class="error-toast">
      <uni-icons type="info" color="#ff4757" size="16" />
      <text class="error-text">{{ syncError }}</text>
      <view class="retry-btn" @click="retrySync">
        <text>重试</text>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, watch } from 'vue';
import { retry, RetryConfigs } from '@/utils/retryMechanism';
import { cacheData, getCachedData, addToSyncQueue, getNetworkStatus } from '@/utils/offlineSupport';
import { handleError } from '@/utils/errorHandler';

export default {
  name: 'DataSyncManager',
  props: {
    // 数据源配置
    dataSources: {
      type: Array,
      default: () => []
    },
    // 自动刷新间隔（毫秒）
    autoRefreshInterval: {
      type: Number,
      default: 30000 // 30秒
    },
    // 是否启用自动刷新
    enableAutoRefresh: {
      type: Boolean,
      default: true
    },
    // 是否显示同步指示器
    showSyncIndicator: {
      type: Boolean,
      default: true
    },
    // 是否显示错误提示
    showErrorToast: {
      type: Boolean,
      default: true
    },
    // 缓存过期时间（毫秒）
    cacheExpiry: {
      type: Number,
      default: 5 * 60 * 1000 // 5分钟
    },
    // 重试配置
    retryConfig: {
      type: Object,
      default: () => RetryConfigs.NETWORK_REQUEST
    }
  },
  emits: ['sync-start', 'sync-success', 'sync-error', 'data-updated'],
  setup(props, { emit }) {
    // 响应式数据
    const syncStatus = ref('idle'); // idle, syncing, success, error
    const syncError = ref(null);
    const lastSyncTime = ref(null);
    const isOnline = ref(getNetworkStatus());
    const autoRefreshTimer = ref(null);
    const syncQueue = ref([]);

    // 计算属性
    const syncStatusClass = computed(() => ({
      'sync-idle': syncStatus.value === 'idle',
      'sync-syncing': syncStatus.value === 'syncing',
      'sync-success': syncStatus.value === 'success',
      'sync-error': syncStatus.value === 'error'
    }));

    const syncIcon = computed(() => {
      switch (syncStatus.value) {
        case 'syncing':
          return 'spinner-cycle';
        case 'success':
          return 'checkmarkempty';
        case 'error':
          return 'closeempty';
        default:
          return 'refreshempty';
      }
    });

    const syncIconColor = computed(() => {
      switch (syncStatus.value) {
        case 'syncing':
          return '#007aff';
        case 'success':
          return '#4cd964';
        case 'error':
          return '#ff4757';
        default:
          return '#8e8e93';
      }
    });

    const syncStatusText = computed(() => {
      switch (syncStatus.value) {
        case 'syncing':
          return '同步中...';
        case 'success':
          return '同步完成';
        case 'error':
          return '同步失败';
        default:
          return '待同步';
      }
    });

    // 方法
    const setSyncStatus = (status, error = null) => {
      syncStatus.value = status;
      syncError.value = error;
      
      if (status === 'success') {
        lastSyncTime.value = new Date();
        // 3秒后隐藏成功状态
        setTimeout(() => {
          if (syncStatus.value === 'success') {
            syncStatus.value = 'idle';
          }
        }, 3000);
      }
    };

    const syncData = async (force = false) => {
      if (syncStatus.value === 'syncing') return;

      setSyncStatus('syncing');
      emit('sync-start');

      try {
        const syncPromises = props.dataSources.map(async (dataSource) => {
          return await syncDataSource(dataSource, force);
        });

        const results = await Promise.allSettled(syncPromises);
        
        // 检查是否有失败的同步
        const failures = results.filter(result => result.status === 'rejected');
        
        if (failures.length > 0) {
          throw new Error(`${failures.length} 个数据源同步失败`);
        }

        setSyncStatus('success');
        emit('sync-success', results);
        emit('data-updated');

      } catch (error) {
        setSyncStatus('error', error.message);
        emit('sync-error', error);
        handleError(error, { operation: 'data_sync' });
      }
    };

    const syncDataSource = async (dataSource, force = false) => {
      const { key, fetchFn, cacheKey, updateStore } = dataSource;

      try {
        // 如果不强制刷新且有缓存，先尝试使用缓存
        if (!force && cacheKey) {
          const cachedData = await getCachedData(cacheKey);
          if (cachedData) {
            if (updateStore) {
              updateStore(cachedData);
            }
            return { key, data: cachedData, fromCache: true };
          }
        }

        // 使用重试机制获取数据
        const data = await retry(fetchFn, props.retryConfig);

        // 缓存数据
        if (cacheKey) {
          await cacheData(cacheKey, data, props.cacheExpiry);
        }

        // 更新Store
        if (updateStore) {
          updateStore(data);
        }

        return { key, data, fromCache: false };

      } catch (error) {
        // 如果在线获取失败，尝试使用缓存
        if (cacheKey) {
          const cachedData = await getCachedData(cacheKey);
          if (cachedData) {
            if (updateStore) {
              updateStore(cachedData);
            }
            return { key, data: cachedData, fromCache: true, error };
          }
        }

        // 如果离线，添加到同步队列
        if (!isOnline.value) {
          await addToSyncQueue({
            type: 'data_fetch',
            data: { key, fetchFn: fetchFn.toString(), cacheKey }
          });
        }

        throw error;
      }
    };

    const retrySync = async () => {
      await syncData(true);
    };

    const startAutoRefresh = () => {
      if (!props.enableAutoRefresh || autoRefreshTimer.value) return;

      autoRefreshTimer.value = setInterval(() => {
        if (isOnline.value && syncStatus.value !== 'syncing') {
          syncData();
        }
      }, props.autoRefreshInterval);
    };

    const stopAutoRefresh = () => {
      if (autoRefreshTimer.value) {
        clearInterval(autoRefreshTimer.value);
        autoRefreshTimer.value = null;
      }
    };

    const handleNetworkChange = () => {
      isOnline.value = getNetworkStatus();
      
      if (isOnline.value) {
        // 网络恢复，立即同步
        syncData();
        startAutoRefresh();
      } else {
        // 网络断开，停止自动刷新
        stopAutoRefresh();
      }
    };

    // 监听网络状态变化
    const setupNetworkListeners = () => {
      if (typeof window !== 'undefined') {
        window.addEventListener('online', handleNetworkChange);
        window.addEventListener('offline', handleNetworkChange);
      }
    };

    const removeNetworkListeners = () => {
      if (typeof window !== 'undefined') {
        window.removeEventListener('online', handleNetworkChange);
        window.removeEventListener('offline', handleNetworkChange);
      }
    };

    // 监听数据源变化
    watch(() => props.dataSources, () => {
      syncData();
    }, { deep: true });

    // 监听自动刷新配置变化
    watch(() => props.enableAutoRefresh, (enabled) => {
      if (enabled) {
        startAutoRefresh();
      } else {
        stopAutoRefresh();
      }
    });

    // 生命周期
    onMounted(() => {
      setupNetworkListeners();
      
      // 初始同步
      if (props.dataSources.length > 0) {
        syncData();
      }
      
      // 启动自动刷新
      if (props.enableAutoRefresh) {
        startAutoRefresh();
      }
    });

    onUnmounted(() => {
      removeNetworkListeners();
      stopAutoRefresh();
    });

    // 暴露方法给父组件
    const expose = {
      syncData,
      retrySync,
      getSyncStatus: () => syncStatus.value,
      getLastSyncTime: () => lastSyncTime.value,
      isOnline: () => isOnline.value
    };

    return {
      // 响应式数据
      syncStatus,
      syncError,
      lastSyncTime,
      isOnline,
      
      // 计算属性
      syncStatusClass,
      syncIcon,
      syncIconColor,
      syncStatusText,
      
      // 方法
      syncData,
      retrySync,
      
      // 暴露给父组件
      ...expose
    };
  }
};
</script>

<style lang="scss" scoped>
.data-sync-manager {
  position: relative;
}

.sync-indicator {
  display: flex;
  align-items: center;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  transition: all 0.3s ease;

  .sync-text {
    margin-left: 8rpx;
  }

  &.sync-idle {
    background-color: #f8f9fa;
    color: #8e8e93;
  }

  &.sync-syncing {
    background-color: #e3f2fd;
    color: #007aff;
    
    .uni-icons {
      animation: spin 1s linear infinite;
    }
  }

  &.sync-success {
    background-color: #e8f5e8;
    color: #4cd964;
  }

  &.sync-error {
    background-color: #ffebee;
    color: #ff4757;
  }
}

.error-toast {
  display: flex;
  align-items: center;
  padding: 12rpx 16rpx;
  margin-top: 8rpx;
  background-color: #ffebee;
  border-radius: 8rpx;
  border-left: 4rpx solid #ff4757;

  .error-text {
    flex: 1;
    margin-left: 8rpx;
    font-size: 24rpx;
    color: #ff4757;
  }

  .retry-btn {
    padding: 4rpx 12rpx;
    background-color: #ff4757;
    color: white;
    border-radius: 4rpx;
    font-size: 22rpx;
    margin-left: 12rpx;

    &:active {
      opacity: 0.8;
    }
  }
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
</style>