<!--
/**
 * OptimizedImageGrid Component
 * 
 * An optimized image grid component with the following features:
 * - Adaptive column count based on screen size
 * - Lazy loading for images in viewport
 * - Loading placeholders and error state handling
 * - Unified aspect ratio control (1:1.2)
 * - Performance optimized with intersection observer
 * 
 * @component OptimizedImageGrid
 * @example
 * <OptimizedImageGrid
 *   :items="itemList"
 *   :columns="{ mobile: 2, tablet: 3, desktop: 4 }"
 *   item-type="clothing"
 *   @item-click="handleItemClick"
 * />
 */
-->
<template>
  <view class="optimized-image-grid" :class="gridClass">
    <view
      v-for="(item, index) in items"
      :key="item.id || index"
      :ref="el => setItemRef(el, index)"
      class="grid-item"
      @click="handleItemClick(item, index)"
    >
      <view class="image-wrapper">
        <image
          v-if="shouldLoadImage(index)"
          :src="getImageUrl(item)"
          mode="aspectFill"
          class="grid-image"
          :class="{ 'image-loaded': loadedImages.has(index) }"
          @load="handleImageLoad(index)"
          @error="handleImageError($event, index)"
        />
        <view v-else class="image-placeholder">
          <view class="placeholder-content">
            <uni-icons type="image" size="32" color="#ddd" />
          </view>
        </view>

        <!-- 加载状态 -->
        <view
          v-if="shouldLoadImage(index) && !loadedImages.has(index)"
          class="loading-overlay"
        >
          <view class="loading-spinner"></view>
        </view>

        <!-- 错误状态 -->
        <view v-if="errorImages.has(index)" class="error-overlay">
          <uni-icons type="close" size="24" color="#999" />
          <text class="error-text">加载失败</text>
        </view>

        <!-- 收藏按钮 -->
        <FavoriteButton
          v-if="showFavoriteButton && ['clothing', 'outfit'].includes(itemType)"
          :item-id="item.id"
          :item-type="itemType"
          size="small"
          variant="minimal"
          class="favorite-btn"
        />

        <!-- 状态标识 -->
        <view v-if="item.isPublic" class="status-badge">
          <uni-icons type="eye" size="10" color="#fff" />
        </view>
      </view>

      <view v-if="showItemInfo" class="item-info">
        <text class="item-title">{{ getItemTitle(item) }}</text>
        <text class="item-subtitle">{{ getItemSubtitle(item) }}</text>
      </view>
    </view>

    <!-- 空状态 -->
    <view v-if="items.length === 0" class="empty-state">
      <uni-icons type="image" size="48" color="#ddd" />
      <text class="empty-text">{{ emptyText }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue';
import FavoriteButton from '@/components/common/FavoriteButton.vue';

/**
 * Component Props
 */
const props = defineProps({
  // 数据项列表
  items: {
    type: Array,
    default: () => []
  },
  // 列数配置
  columns: {
    type: Object,
    default: () => ({
      mobile: 2,
      tablet: 3,
      desktop: 4
    })
  },
  // 项目类型
  itemType: {
    type: String,
    default: 'clothing',
    validator: value => ['clothing', 'outfit', 'collection'].includes(value)
  },
  // 是否显示收藏按钮
  showFavoriteButton: {
    type: Boolean,
    default: true
  },
  // 是否显示项目信息
  showItemInfo: {
    type: Boolean,
    default: true
  },
  // 空状态文本
  emptyText: {
    type: String,
    default: '暂无内容'
  },
  // 是否启用懒加载
  enableLazyLoad: {
    type: Boolean,
    default: true
  },
  // 预加载距离 (px)
  preloadDistance: {
    type: Number,
    default: 100
  }
});

const emit = defineEmits(['item-click', 'image-load', 'image-error']);

// 状态管理
const itemRefs = ref(new Map());
const visibleItems = ref(new Set());
const loadedImages = ref(new Set());
const errorImages = ref(new Set());
const observer = ref(null);

// 响应式网格类名
const gridClass = computed(() => {
  return `grid-columns-${props.columns.mobile}-${props.columns.tablet}-${props.columns.desktop}`;
});

// 设置项目引用
const setItemRef = (el, index) => {
  if (el) {
    itemRefs.value.set(index, el);
  } else {
    itemRefs.value.delete(index);
  }
};

// 判断是否应该加载图片
const shouldLoadImage = index => {
  if (!props.enableLazyLoad) return true;
  return visibleItems.value.has(index);
};

// 获取图片URL
const getImageUrl = item => {
  if (item.imageUrls && item.imageUrls.length > 0) {
    return item.imageUrls[0];
  }
  if (item.imageUrl) {
    return item.imageUrl;
  }
  if (item.cover_image_url) {
    return item.cover_image_url;
  }
  return '/static/images/empty-collection.png';
};

// 获取项目标题
const getItemTitle = item => {
  return item.name || item.title || '未命名';
};

// 获取项目副标题
const getItemSubtitle = item => {
  switch (props.itemType) {
    case 'clothing': {
      return item.categoryDisplay || item.category || '未分类';
    }
    case 'outfit': {
      return item.occasion || '日常搭配';
    }
    case 'collection': {
      const clothingCount = item.stats?.clothingCount || 0;
      const outfitCount = item.stats?.outfitCount || 0;
      return `${clothingCount} 衣物 · ${outfitCount} 搭配`;
    }
    default:
      return '';
  }
};

// 事件处理
const handleItemClick = (item, index) => {
  emit('item-click', { item, index });
};

const handleImageLoad = index => {
  loadedImages.value.add(index);
  errorImages.value.delete(index);
  emit('image-load', { index, item: props.items[index] });
};

const handleImageError = (event, index) => {
  errorImages.value.add(index);
  loadedImages.value.delete(index);
  // 设置默认图片
  event.target.src = '/static/images/empty-collection.png';
  emit('image-error', { index, item: props.items[index] });
};

// 初始化懒加载观察器
const initLazyLoad = () => {
  if (!props.enableLazyLoad || typeof IntersectionObserver === 'undefined') {
    // 如果不支持懒加载，直接显示所有图片
    props.items.forEach((_, index) => {
      visibleItems.value.add(index);
    });
    return;
  }

  observer.value = new IntersectionObserver(
    entries => {
      entries.forEach(entry => {
        const index = parseInt(entry.target.dataset.index);
        if (entry.isIntersecting) {
          visibleItems.value.add(index);
        }
      });
    },
    {
      rootMargin: `${props.preloadDistance}px`,
      threshold: 0.1
    }
  );

  // 观察所有项目
  nextTick(() => {
    itemRefs.value.forEach((el, index) => {
      if (el) {
        el.dataset.index = index;
        observer.value.observe(el);
      }
    });
  });
};

// 清理观察器
const cleanupObserver = () => {
  if (observer.value) {
    observer.value.disconnect();
    observer.value = null;
  }
};

// 重新初始化观察器
const reinitializeObserver = () => {
  cleanupObserver();
  visibleItems.value.clear();
  loadedImages.value.clear();
  errorImages.value.clear();

  nextTick(() => {
    initLazyLoad();
  });
};

// 生命周期
onMounted(() => {
  initLazyLoad();
});

onUnmounted(() => {
  cleanupObserver();
});

// 监听items变化，重新初始化观察器
const unwatchItems = computed(() => props.items.length);
const prevItemsLength = ref(0);

const checkItemsChange = () => {
  if (unwatchItems.value !== prevItemsLength.value) {
    prevItemsLength.value = unwatchItems.value;
    reinitializeObserver();
  }
};

// 使用定时器检查items变化（uni-app环境下的兼容方案）
let checkTimer = null;
onMounted(() => {
  checkTimer = setInterval(checkItemsChange, 1000);
});

onUnmounted(() => {
  if (checkTimer) {
    clearInterval(checkTimer);
  }
});
</script>

<style lang="scss" scoped>
.optimized-image-grid {
  display: grid;
  gap: 16rpx;
  width: 100%;

  // 默认移动端2列
  grid-template-columns: repeat(2, 1fr);

  // 响应式列数
  &.grid-columns-2-3-4 {
    grid-template-columns: repeat(2, 1fr);

    @media (min-width: 481px) and (max-width: 768px) {
      grid-template-columns: repeat(3, 1fr);
    }

    @media (min-width: 769px) {
      grid-template-columns: repeat(4, 1fr);
    }
  }

  &.grid-columns-3-4-5 {
    grid-template-columns: repeat(3, 1fr);

    @media (min-width: 481px) and (max-width: 768px) {
      grid-template-columns: repeat(4, 1fr);
    }

    @media (min-width: 769px) {
      grid-template-columns: repeat(5, 1fr);
    }
  }
}

.grid-item {
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  }
}

.image-wrapper {
  position: relative;
  width: 100%;
  // 1:1.2 宽高比
  padding-bottom: 120%;
  background-color: #f5f5f5;
  overflow: hidden;

  .grid-image {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.3s ease;

    &.image-loaded {
      opacity: 1;
    }
  }

  .image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: #f5f5f5;

    .placeholder-content {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #ddd;
    }
  }

  .loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(245, 245, 245, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;

    .loading-spinner {
      width: 32rpx;
      height: 32rpx;
      border: 3rpx solid #f3f3f3;
      border-top: 3rpx solid #fda085;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  .error-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(245, 245, 245, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #999;

    .error-text {
      font-size: 20rpx;
      margin-top: 8rpx;
    }
  }

  .favorite-btn {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    z-index: 2;
  }

  .status-badge {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
    width: 24rpx;
    height: 24rpx;
    background-color: rgba(0, 0, 0, 0.6);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1;
  }
}

.item-info {
  padding: 12rpx 16rpx;

  .item-title {
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    line-height: 1.2;
    margin-bottom: 6rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-subtitle {
    font-size: 22rpx;
    color: #666;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

.empty-state {
  grid-column: 1 / -1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  color: #999;

  .empty-text {
    font-size: 28rpx;
    margin-top: 16rpx;
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

// 响应式调整
@media (max-width: 480px) {
  .optimized-image-grid {
    gap: 12rpx;
  }

  .grid-item {
    .item-info {
      padding: 10rpx 12rpx;

      .item-title {
        font-size: 24rpx;
      }

      .item-subtitle {
        font-size: 20rpx;
      }
    }
  }
}

@media (min-width: 769px) {
  .optimized-image-grid {
    gap: 20rpx;
  }
}
</style>
