import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import ErrorBoundary from '../ErrorBoundary.vue';

// Mock uni API
global.uni = {
  showToast: vi.fn()
};

// Mock error handler
vi.mock('@/utils/errorHandler', () => ({
  handleError: vi.fn((error, context) => ({
    errorInfo: {
      id: 'test-error-id',
      message: error.message,
      type: 'UNKNOWN_ERROR',
      level: 'high',
      timestamp: '2023-01-01T00:00:00.000Z',
      context
    }
  }))
}));

// 创建会抛出错误的测试组件
const ThrowingComponent = {
  template: '<div>{{ throwError() }}</div>',
  methods: {
    throwError() {
      throw new Error('Test error');
    }
  }
};

const NormalComponent = {
  template: '<div>Normal content</div>'
};

describe('ErrorBoundary', () => {
  let wrapper;

  beforeEach(() => {
    vi.clearAllMocks();
    console.error = vi.fn(); // Mock console.error
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('正常渲染', () => {
    it('应该正常渲染子组件', () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: '<div>Test content</div>'
        }
      });

      expect(wrapper.text()).toContain('Test content');
      expect(wrapper.find('.error-fallback').exists()).toBe(false);
    });

    it('应该传递props到子组件', () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: NormalComponent
        }
      });

      expect(wrapper.findComponent(NormalComponent).exists()).toBe(true);
    });
  });

  describe('错误捕获', () => {
    it('应该捕获子组件错误并显示错误界面', async () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      expect(wrapper.find('.error-fallback').exists()).toBe(true);
      expect(wrapper.find('.default-error-ui').exists()).toBe(true);
      expect(wrapper.text()).toContain('出现了一些问题');
    });

    it('应该显示自定义错误标题和消息', async () => {
      wrapper = mount(ErrorBoundary, {
        props: {
          title: '自定义错误标题',
          message: '自定义错误消息'
        },
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      expect(wrapper.text()).toContain('自定义错误标题');
      expect(wrapper.text()).toContain('自定义错误消息');
    });

    it('应该触发error事件', async () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      expect(wrapper.emitted('error')).toBeTruthy();
      expect(wrapper.emitted('error')[0][0]).toMatchObject({
        id: 'test-error-id',
        message: 'Test error'
      });
    });
  });

  describe('重试机制', () => {
    it('应该显示重试按钮', async () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      const retryButton = wrapper.find('.retry-button');
      expect(retryButton.exists()).toBe(true);
      expect(retryButton.text()).toBe('重试');
    });

    it('应该在点击重试时重置错误状态', async () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      // 确认错误状态
      expect(wrapper.find('.error-fallback').exists()).toBe(true);

      // 点击重试
      await wrapper.find('.retry-button').trigger('click');
      await nextTick();

      // 检查重试事件
      expect(wrapper.emitted('retry')).toBeTruthy();
      expect(wrapper.emitted('retry')[0][0]).toBe(1); // 重试次数
    });

    it('应该在重试时显示加载状态', async () => {
      wrapper = mount(ErrorBoundary, {
        props: {
          retryDelay: 100
        },
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      const retryButton = wrapper.find('.retry-button');
      await retryButton.trigger('click');

      expect(retryButton.text()).toBe('重试中...');
      expect(retryButton.attributes('disabled')).toBeDefined();
    });

    it('应该限制最大重试次数', async () => {
      wrapper = mount(ErrorBoundary, {
        props: {
          maxRetries: 2,
          retryDelay: 0
        },
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      const retryButton = wrapper.find('.retry-button');

      // 第一次重试
      await retryButton.trigger('click');
      await nextTick();

      // 第二次重试
      await retryButton.trigger('click');
      await nextTick();

      // 第三次应该被禁用
      expect(retryButton.attributes('disabled')).toBeDefined();
    });

    it('应该隐藏重试按钮当showRetry为false', async () => {
      wrapper = mount(ErrorBoundary, {
        props: {
          showRetry: false
        },
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      expect(wrapper.find('.retry-button').exists()).toBe(false);
    });
  });

  describe('错误报告', () => {
    it('应该在生产环境显示报告按钮', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      expect(wrapper.find('.report-button').exists()).toBe(true);

      process.env.NODE_ENV = originalEnv;
    });

    it('应该在开发环境隐藏报告按钮', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      expect(wrapper.find('.report-button').exists()).toBe(false);

      process.env.NODE_ENV = originalEnv;
    });

    it('应该在点击报告按钮时触发report事件', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      await wrapper.find('.report-button').trigger('click');

      expect(wrapper.emitted('report')).toBeTruthy();
      expect(wrapper.emitted('report')[0][0]).toMatchObject({
        error: expect.any(Object),
        userAgent: expect.any(String),
        url: expect.any(String),
        timestamp: expect.any(String),
        retryCount: expect.any(Number)
      });

      process.env.NODE_ENV = originalEnv;
    });

    it('应该显示Toast提示当报告问题时', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'production';

      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      await wrapper.find('.report-button').trigger('click');

      expect(uni.showToast).toHaveBeenCalledWith({
        title: '问题已反馈，感谢您的支持',
        icon: 'success',
        duration: 2000
      });

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('错误详情', () => {
    it('应该在开发环境显示详情切换按钮', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      expect(wrapper.find('.details-button').exists()).toBe(true);

      process.env.NODE_ENV = originalEnv;
    });

    it('应该切换错误详情显示', async () => {
      const originalEnv = process.env.NODE_ENV;
      process.env.NODE_ENV = 'development';

      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      const detailsButton = wrapper.find('.details-button');
      expect(detailsButton.text()).toBe('显示详情');
      expect(wrapper.find('.error-details').exists()).toBe(false);

      await detailsButton.trigger('click');
      await nextTick();

      expect(detailsButton.text()).toBe('隐藏详情');
      expect(wrapper.find('.error-details').exists()).toBe(true);

      process.env.NODE_ENV = originalEnv;
    });
  });

  describe('自定义错误界面', () => {
    it('应该使用自定义fallback插槽', async () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent,
          fallback: '<div class="custom-error">Custom error UI</div>'
        }
      });

      await nextTick();

      expect(wrapper.find('.custom-error').exists()).toBe(true);
      expect(wrapper.text()).toContain('Custom error UI');
      expect(wrapper.find('.default-error-ui').exists()).toBe(false);
    });

    it('应该向fallback插槽传递错误信息和重试方法', async () => {
      const FallbackComponent = {
        template: `
          <div class="custom-fallback">
            <div>Error: {{ error.message }}</div>
            <button @click="retry" class="custom-retry">Custom Retry</button>
          </div>
        `,
        props: ['error', 'retry']
      };

      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent,
          fallback: FallbackComponent
        }
      });

      await nextTick();

      expect(wrapper.find('.custom-fallback').exists()).toBe(true);
      expect(wrapper.text()).toContain('Error: Test error');

      const customRetryButton = wrapper.find('.custom-retry');
      expect(customRetryButton.exists()).toBe(true);

      await customRetryButton.trigger('click');
      expect(wrapper.emitted('retry')).toBeTruthy();
    });
  });

  describe('组件方法', () => {
    it('应该暴露reset方法', async () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      // 确认错误状态
      expect(wrapper.find('.error-fallback').exists()).toBe(true);

      // 调用reset方法
      wrapper.vm.reset();
      await nextTick();

      // 检查状态是否重置
      expect(wrapper.vm.hasError).toBe(false);
    });

    it('应该暴露triggerError方法用于测试', async () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: NormalComponent
        }
      });

      // 初始状态正常
      expect(wrapper.find('.error-fallback').exists()).toBe(false);

      // 手动触发错误
      wrapper.vm.triggerError(new Error('Manual test error'));
      await nextTick();

      // 检查错误状态
      expect(wrapper.find('.error-fallback').exists()).toBe(true);
      expect(wrapper.text()).toContain('出现了一些问题');
    });

    it('应该暴露hasError和errorInfo计算属性', async () => {
      wrapper = mount(ErrorBoundary, {
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      expect(wrapper.vm.hasError).toBe(true);
      expect(wrapper.vm.errorInfo).toMatchObject({
        id: 'test-error-id',
        message: 'Test error'
      });
    });
  });

  describe('样式和类名', () => {
    it('应该应用自定义fallback类名', async () => {
      wrapper = mount(ErrorBoundary, {
        props: {
          fallbackClass: 'custom-fallback-class'
        },
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      const fallback = wrapper.find('.error-fallback');
      expect(fallback.classes()).toContain('custom-fallback-class');
    });

    it('应该根据错误级别应用不同样式', async () => {
      wrapper = mount(ErrorBoundary, {
        props: {
          level: 'critical'
        },
        slots: {
          default: ThrowingComponent
        }
      });

      await nextTick();

      // 这里可以检查是否根据级别应用了不同的样式
      expect(wrapper.find('.error-fallback').exists()).toBe(true);
    });
  });
});