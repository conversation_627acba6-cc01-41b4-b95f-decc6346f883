import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import LazyImage from '../LazyImage.vue';

// Mock uni-app APIs
global.uni = {
  createIntersectionObserver: vi.fn(() => ({
    relativeToViewport: vi.fn().mockReturnThis(),
    observe: vi.fn(),
    disconnect: vi.fn()
  }))
};

// Mock uni-app components
vi.mock('uni-icons', () => ({
  default: {
    name: 'uni-icons',
    template: '<div class="uni-icons-mock"></div>',
    props: ['type', 'size', 'color']
  }
}));

vi.mock('uni-load-more', () => ({
  default: {
    name: 'uni-load-more',
    template: '<div class="uni-load-more-mock"></div>',
    props: ['status', 'contentText']
  }
}));

describe('LazyImage', () => {
  let wrapper;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  it('should render placeholder initially', () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        width: 200,
        height: 200
      }
    });

    expect(wrapper.find('.image-placeholder').exists()).toBe(true);
    expect(wrapper.find('.lazy-image').exists()).toBe(true);
    expect(wrapper.find('.lazy-image').isVisible()).toBe(false);
  });

  it('should show loading state when immediate is true', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        immediate: true,
        showProgress: true
      }
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.vm.loading).toBe(true);
    expect(wrapper.find('.loading-progress').exists()).toBe(true);
  });

  it('should handle image load success', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        immediate: true
      }
    });

    const imageElement = wrapper.find('.lazy-image');
    await imageElement.trigger('load');

    expect(wrapper.vm.loaded).toBe(true);
    expect(wrapper.vm.loading).toBe(false);
    expect(wrapper.vm.error).toBe(false);
    expect(wrapper.emitted('load')).toBeTruthy();
  });

  it('should handle image load error', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'invalid-image.jpg',
        immediate: true
      }
    });

    const imageElement = wrapper.find('.lazy-image');
    await imageElement.trigger('error');

    expect(wrapper.vm.loading).toBe(false);
    expect(wrapper.vm.error).toBe(true);
    expect(wrapper.find('.image-error').exists()).toBe(true);
    expect(wrapper.emitted('error')).toBeTruthy();
  });

  it('should use fallback image on error', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'invalid-image.jpg',
        fallback: 'fallback-image.jpg',
        immediate: true
      }
    });

    const imageElement = wrapper.find('.lazy-image');
    await imageElement.trigger('error');

    // 第一次错误应该尝试使用fallback
    expect(wrapper.vm.currentSrc).toBe('fallback-image.jpg');
  });

  it('should retry on manual retry', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        immediate: true
      }
    });

    // 模拟加载失败
    const imageElement = wrapper.find('.lazy-image');
    await imageElement.trigger('error');

    // 等待重试次数达到上限
    wrapper.vm.retryCount = wrapper.vm.maxRetries;
    await wrapper.vm.$nextTick();

    expect(wrapper.vm.error).toBe(true);

    // 手动重试
    const errorElement = wrapper.find('.image-error');
    await errorElement.trigger('click');

    expect(wrapper.vm.retryCount).toBe(0);
    expect(wrapper.vm.error).toBe(false);
    expect(wrapper.emitted('retry')).toBeTruthy();
  });

  it('should apply correct container styles', () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        width: '300px',
        height: 200
      }
    });

    const container = wrapper.find('.lazy-image-container');
    const style = container.attributes('style');

    expect(style).toContain('width: 300px');
    expect(style).toContain('height: 200px');
  });

  it('should setup intersection observer when not immediate', () => {
    const mockObserver = {
      relativeToViewport: vi.fn().mockReturnThis(),
      observe: vi.fn(),
      disconnect: vi.fn()
    };

    global.uni.createIntersectionObserver.mockReturnValue(mockObserver);

    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        immediate: false
      }
    });

    expect(global.uni.createIntersectionObserver).toHaveBeenCalled();
    expect(mockObserver.relativeToViewport).toHaveBeenCalled();
    expect(mockObserver.observe).toHaveBeenCalled();
  });

  it('should cleanup observer on unmount', () => {
    const mockObserver = {
      relativeToViewport: vi.fn().mockReturnThis(),
      observe: vi.fn(),
      disconnect: vi.fn()
    };

    global.uni.createIntersectionObserver.mockReturnValue(mockObserver);

    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        immediate: false
      }
    });

    wrapper.unmount();

    expect(mockObserver.disconnect).toHaveBeenCalled();
  });

  it('should handle src change', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'image1.jpg',
        immediate: true
      }
    });

    // 模拟第一张图片加载完成
    await wrapper.find('.lazy-image').trigger('load');
    expect(wrapper.vm.loaded).toBe(true);

    // 更改图片源
    await wrapper.setProps({ src: 'image2.jpg' });

    expect(wrapper.vm.loaded).toBe(false);
    expect(wrapper.vm.loading).toBe(false);
    expect(wrapper.vm.error).toBe(false);
    expect(wrapper.vm.retryCount).toBe(0);
  });

  it('should show progress when enabled', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        immediate: true,
        showProgress: true
      }
    });

    await wrapper.vm.$nextTick();

    expect(wrapper.find('.loading-progress').exists()).toBe(true);
    expect(wrapper.find('progress').exists()).toBe(true);
  });

  it('should handle webp and fadeShow props', () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        webp: false,
        fadeShow: false
      }
    });

    const imageElement = wrapper.find('.lazy-image');
    expect(imageElement.attributes('webp')).toBe('false');
    expect(imageElement.attributes('fade-show')).toBe('false');
  });

  it('should expose retry and startLoad methods', () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg'
      }
    });

    expect(typeof wrapper.vm.retry).toBe('function');
    expect(typeof wrapper.vm.startLoad).toBe('function');
    expect(typeof wrapper.vm.preloadImages).toBe('function');
    expect(typeof wrapper.vm.clearCache).toBe('function');
    expect(typeof wrapper.vm.getCacheSize).toBe('function');
  });

  it('should handle different loading animations', async () => {
    // Test pulse animation
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        loadingAnimation: 'pulse',
        immediate: true
      }
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.find('.loading-pulse').exists()).toBe(true);
    expect(wrapper.find('.pulse-loader').exists()).toBe(true);

    wrapper.unmount();

    // Test shimmer animation
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        loadingAnimation: 'shimmer',
        immediate: true
      }
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.find('.loading-shimmer').exists()).toBe(true);
    expect(wrapper.find('.shimmer-loader').exists()).toBe(true);

    wrapper.unmount();

    // Test spinner animation
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        loadingAnimation: 'spinner',
        immediate: true
      }
    });

    await wrapper.vm.$nextTick();
    expect(wrapper.find('.loading-spinner').exists()).toBe(true);
    expect(wrapper.find('.spinner-loader').exists()).toBe(true);
  });

  it('should handle cache functionality', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        enableCache: true,
        immediate: true
      }
    });

    // Simulate successful load
    await wrapper.find('.lazy-image').trigger('load');
    
    expect(wrapper.emitted('load')).toBeTruthy();

    // Create another instance with same src - should hit cache
    const wrapper2 = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        enableCache: true,
        immediate: true
      }
    });

    await wrapper2.vm.$nextTick();
    expect(wrapper2.emitted('cache-hit')).toBeTruthy();

    wrapper2.unmount();
  });

  it('should handle preload functionality', async () => {
    const preloadList = ['image1.jpg', 'image2.jpg', 'image3.jpg'];
    
    wrapper = mount(LazyImage, {
      props: {
        src: 'main-image.jpg',
        preloadList,
        immediate: true
      }
    });

    await wrapper.vm.$nextTick();

    // Test preload method
    const preloadPromise = wrapper.vm.preloadImages(['test1.jpg', 'test2.jpg']);
    expect(preloadPromise).toBeInstanceOf(Promise);
  });

  it('should disable cache when enableCache is false', async () => {
    wrapper = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        enableCache: false,
        immediate: true
      }
    });

    // Simulate successful load
    await wrapper.find('.lazy-image').trigger('load');
    
    expect(wrapper.emitted('load')).toBeTruthy();
    expect(wrapper.emitted('cache-hit')).toBeFalsy();

    // Create another instance with same src - should not hit cache
    const wrapper2 = mount(LazyImage, {
      props: {
        src: 'test-image.jpg',
        enableCache: false,
        immediate: true
      }
    });

    await wrapper2.vm.$nextTick();
    expect(wrapper2.emitted('cache-hit')).toBeFalsy();

    wrapper2.unmount();
  });
});

// 性能测试
describe('LazyImage Performance', () => {
  it('should handle rapid src changes efficiently', async () => {
    const wrapper = mount(LazyImage, {
      props: {
        src: 'image1.jpg',
        immediate: true
      }
    });

    const startTime = performance.now();

    // 快速更改图片源
    for (let i = 2; i <= 20; i++) {
      await wrapper.setProps({ src: `image${i}.jpg` });
    }

    const endTime = performance.now();
    const updateTime = endTime - startTime;

    // 更新时间应该在合理范围内
    expect(updateTime).toBeLessThan(50);

    wrapper.unmount();
  });

  it('should not cause memory leaks with observer cleanup', () => {
    const mockObserver = {
      relativeToViewport: vi.fn().mockReturnThis(),
      observe: vi.fn(),
      disconnect: vi.fn()
    };

    global.uni.createIntersectionObserver.mockReturnValue(mockObserver);

    // 创建和销毁多个实例
    for (let i = 0; i < 10; i++) {
      const wrapper = mount(LazyImage, {
        props: {
          src: `test-image-${i}.jpg`,
          immediate: false
        }
      });
      wrapper.unmount();
    }

    // 每个实例都应该正确清理
    expect(mockObserver.disconnect).toHaveBeenCalledTimes(10);
  });
});
