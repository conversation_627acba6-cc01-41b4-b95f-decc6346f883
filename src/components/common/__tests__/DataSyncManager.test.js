import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import DataSyncManager from '../DataSyncManager.vue';

// Mock utilities
vi.mock('@/utils/retryMechanism', () => ({
  retry: vi.fn(),
  RetryConfigs: {
    NETWORK_REQUEST: { maxRetries: 3, delay: 1000 }
  }
}));

vi.mock('@/utils/offlineSupport', () => ({
  cacheData: vi.fn(),
  getCachedData: vi.fn(),
  addToSyncQueue: vi.fn(),
  getNetworkStatus: vi.fn(() => true)
}));

vi.mock('@/utils/errorHandler', () => ({
  handleError: vi.fn()
}));

// Mock uni-app components
vi.mock('uni-icons', () => ({
  default: {
    name: 'uni-icons',
    template: '<div class="uni-icons-mock" :data-type="type" :data-color="color"></div>',
    props: ['type', 'color', 'size']
  }
}));

describe('DataSyncManager', () => {
  let wrapper;
  let mockDataSources;
  let mockFetchFn;

  beforeEach(() => {
    vi.clearAllMocks();
    
    mockFetchFn = vi.fn().mockResolvedValue({ data: 'test data' });
    mockDataSources = [
      {
        key: 'test-data',
        fetchFn: mockFetchFn,
        cacheKey: 'test-cache',
        updateStore: vi.fn()
      }
    ];

    // Mock timers
    vi.useFakeTimers();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
    vi.useRealTimers();
  });

  describe('渲染和初始状态', () => {
    it('应该正确渲染默认状态', () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      expect(wrapper.find('.data-sync-manager').exists()).toBe(true);
      expect(wrapper.find('.sync-indicator').exists()).toBe(true);
      expect(wrapper.vm.syncStatus).toBe('idle');
    });

    it('应该根据props控制同步指示器显示', () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          showSyncIndicator: false
        }
      });

      expect(wrapper.find('.sync-indicator').exists()).toBe(false);
    });

    it('应该根据props控制错误提示显示', async () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          showErrorToast: false
        }
      });

      // 设置错误状态
      wrapper.vm.syncError = '测试错误';
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.error-toast').exists()).toBe(false);
    });
  });

  describe('同步功能', () => {
    it('应该在挂载时自动同步数据', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      retry.mockResolvedValue({ data: 'test data' });

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      await wrapper.vm.$nextTick();
      
      expect(retry).toHaveBeenCalledWith(mockFetchFn, expect.any(Object));
      expect(mockDataSources[0].updateStore).toHaveBeenCalledWith({ data: 'test data' });
    });

    it('应该正确处理同步状态变化', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      retry.mockImplementation(() => new Promise(resolve => setTimeout(() => resolve({ data: 'test' }), 100)));

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      // 开始同步
      const syncPromise = wrapper.vm.syncData();
      await wrapper.vm.$nextTick();

      expect(wrapper.vm.syncStatus).toBe('syncing');
      expect(wrapper.find('.sync-indicator').classes()).toContain('sync-syncing');

      // 完成同步
      vi.advanceTimersByTime(100);
      await syncPromise;
      await wrapper.vm.$nextTick();

      expect(wrapper.vm.syncStatus).toBe('success');
      expect(wrapper.find('.sync-indicator').classes()).toContain('sync-success');
    });

    it('应该正确处理同步错误', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      const testError = new Error('同步失败');
      retry.mockRejectedValue(testError);

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      await wrapper.vm.$nextTick();

      expect(wrapper.vm.syncStatus).toBe('error');
      expect(wrapper.vm.syncError).toBe('同步失败');
      expect(wrapper.find('.sync-indicator').classes()).toContain('sync-error');
    });

    it('应该支持强制刷新', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      const { getCachedData } = await import('@/utils/offlineSupport');
      
      getCachedData.mockResolvedValue({ cached: 'data' });
      retry.mockResolvedValue({ fresh: 'data' });

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      // 正常同步应该使用缓存
      await wrapper.vm.syncData(false);
      expect(getCachedData).toHaveBeenCalled();

      // 强制刷新应该跳过缓存
      getCachedData.mockClear();
      await wrapper.vm.syncData(true);
      expect(retry).toHaveBeenCalledWith(mockFetchFn, expect.any(Object));
    });
  });

  describe('缓存功能', () => {
    it('应该使用缓存数据当可用时', async () => {
      const { getCachedData, cacheData } = await import('@/utils/offlineSupport');
      const cachedData = { cached: 'data' };
      
      getCachedData.mockResolvedValue(cachedData);

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      await wrapper.vm.$nextTick();

      expect(getCachedData).toHaveBeenCalledWith('test-cache');
      expect(mockDataSources[0].updateStore).toHaveBeenCalledWith(cachedData);
    });

    it('应该缓存新获取的数据', async () => {
      const { retry, cacheData } = await import('@/utils/retryMechanism');
      const { cacheData: cacheDataUtil } = await import('@/utils/offlineSupport');
      const freshData = { fresh: 'data' };
      
      retry.mockResolvedValue(freshData);

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          cacheExpiry: 10000
        }
      });

      await wrapper.vm.$nextTick();

      expect(cacheDataUtil).toHaveBeenCalledWith('test-cache', freshData, 10000);
    });
  });

  describe('自动刷新功能', () => {
    it('应该启动自动刷新定时器', () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          enableAutoRefresh: true,
          autoRefreshInterval: 5000
        }
      });

      expect(wrapper.vm.autoRefreshTimer).toBeTruthy();
    });

    it('应该在禁用时停止自动刷新', async () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          enableAutoRefresh: true,
          autoRefreshInterval: 5000
        }
      });

      const timer = wrapper.vm.autoRefreshTimer;
      expect(timer).toBeTruthy();

      await wrapper.setProps({ enableAutoRefresh: false });
      expect(wrapper.vm.autoRefreshTimer).toBeNull();
    });

    it('应该在自动刷新间隔触发同步', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      retry.mockResolvedValue({ data: 'auto refresh' });

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          enableAutoRefresh: true,
          autoRefreshInterval: 1000
        }
      });

      // 清除初始同步调用
      retry.mockClear();

      // 快进时间触发自动刷新
      vi.advanceTimersByTime(1000);
      await wrapper.vm.$nextTick();

      expect(retry).toHaveBeenCalled();
    });
  });

  describe('网络状态处理', () => {
    it('应该在网络恢复时自动同步', async () => {
      const { getNetworkStatus } = await import('@/utils/offlineSupport');
      
      // 初始离线状态
      getNetworkStatus.mockReturnValue(false);

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      expect(wrapper.vm.isOnline).toBe(false);

      // 模拟网络恢复
      getNetworkStatus.mockReturnValue(true);
      wrapper.vm.handleNetworkChange();
      await wrapper.vm.$nextTick();

      expect(wrapper.vm.isOnline).toBe(true);
    });

    it('应该在离线时停止自动刷新', () => {
      const { getNetworkStatus } = await import('@/utils/offlineSupport');
      
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          enableAutoRefresh: true
        }
      });

      const timer = wrapper.vm.autoRefreshTimer;
      expect(timer).toBeTruthy();

      // 模拟网络断开
      getNetworkStatus.mockReturnValue(false);
      wrapper.vm.handleNetworkChange();

      expect(wrapper.vm.autoRefreshTimer).toBeNull();
    });
  });

  describe('错误处理和重试', () => {
    it('应该显示错误提示', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      retry.mockRejectedValue(new Error('网络错误'));

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          showErrorToast: true
        }
      });

      await wrapper.vm.$nextTick();

      expect(wrapper.find('.error-toast').exists()).toBe(true);
      expect(wrapper.find('.error-text').text()).toBe('网络错误');
    });

    it('应该支持手动重试', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      retry.mockRejectedValueOnce(new Error('网络错误'))
           .mockResolvedValueOnce({ data: 'retry success' });

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          showErrorToast: true
        }
      });

      await wrapper.vm.$nextTick();

      expect(wrapper.vm.syncStatus).toBe('error');

      // 点击重试按钮
      await wrapper.find('.retry-btn').trigger('click');
      await wrapper.vm.$nextTick();

      expect(wrapper.vm.syncStatus).toBe('success');
      expect(retry).toHaveBeenCalledTimes(2);
    });

    it('应该在离线时添加到同步队列', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      const { getNetworkStatus, addToSyncQueue } = await import('@/utils/offlineSupport');
      
      getNetworkStatus.mockReturnValue(false);
      retry.mockRejectedValue(new Error('网络不可用'));

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      await wrapper.vm.$nextTick();

      expect(addToSyncQueue).toHaveBeenCalledWith({
        type: 'data_fetch',
        data: {
          key: 'test-data',
          fetchFn: expect.any(String),
          cacheKey: 'test-cache'
        }
      });
    });
  });

  describe('事件发射', () => {
    it('应该发射sync-start事件', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      retry.mockImplementation(() => new Promise(resolve => setTimeout(resolve, 100)));

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      wrapper.vm.syncData();
      await wrapper.vm.$nextTick();

      expect(wrapper.emitted('sync-start')).toBeTruthy();
    });

    it('应该发射sync-success事件', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      retry.mockResolvedValue({ data: 'success' });

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      await wrapper.vm.$nextTick();

      expect(wrapper.emitted('sync-success')).toBeTruthy();
      expect(wrapper.emitted('data-updated')).toBeTruthy();
    });

    it('应该发射sync-error事件', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      const testError = new Error('同步失败');
      retry.mockRejectedValue(testError);

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      await wrapper.vm.$nextTick();

      expect(wrapper.emitted('sync-error')).toBeTruthy();
      expect(wrapper.emitted('sync-error')[0][0]).toBe(testError);
    });
  });

  describe('计算属性', () => {
    it('应该根据同步状态显示正确的图标', async () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      // 测试不同状态的图标
      wrapper.vm.syncStatus = 'idle';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncIcon).toBe('refreshempty');

      wrapper.vm.syncStatus = 'syncing';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncIcon).toBe('spinner-cycle');

      wrapper.vm.syncStatus = 'success';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncIcon).toBe('checkmarkempty');

      wrapper.vm.syncStatus = 'error';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncIcon).toBe('closeempty');
    });

    it('应该根据同步状态显示正确的颜色', async () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      // 测试不同状态的颜色
      wrapper.vm.syncStatus = 'syncing';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncIconColor).toBe('#007aff');

      wrapper.vm.syncStatus = 'success';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncIconColor).toBe('#4cd964');

      wrapper.vm.syncStatus = 'error';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncIconColor).toBe('#ff4757');
    });

    it('应该根据同步状态显示正确的文本', async () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      // 测试不同状态的文本
      wrapper.vm.syncStatus = 'syncing';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncStatusText).toBe('同步中...');

      wrapper.vm.syncStatus = 'success';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncStatusText).toBe('同步完成');

      wrapper.vm.syncStatus = 'error';
      await wrapper.vm.$nextTick();
      expect(wrapper.vm.syncStatusText).toBe('同步失败');
    });
  });

  describe('暴露的方法', () => {
    it('应该暴露正确的方法给父组件', () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      expect(typeof wrapper.vm.syncData).toBe('function');
      expect(typeof wrapper.vm.retrySync).toBe('function');
      expect(typeof wrapper.vm.getSyncStatus).toBe('function');
      expect(typeof wrapper.vm.getLastSyncTime).toBe('function');
      expect(typeof wrapper.vm.isOnline).toBe('function');
    });

    it('应该返回正确的状态信息', () => {
      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      expect(wrapper.vm.getSyncStatus()).toBe('idle');
      expect(wrapper.vm.isOnline()).toBe(true);
    });
  });

  describe('生命周期', () => {
    it('应该在卸载时清理定时器和监听器', () => {
      const removeEventListenerSpy = vi.spyOn(window, 'removeEventListener');

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources,
          enableAutoRefresh: true
        }
      });

      const timer = wrapper.vm.autoRefreshTimer;
      expect(timer).toBeTruthy();

      wrapper.unmount();

      expect(removeEventListenerSpy).toHaveBeenCalledWith('online', expect.any(Function));
      expect(removeEventListenerSpy).toHaveBeenCalledWith('offline', expect.any(Function));
    });
  });

  describe('数据源变化监听', () => {
    it('应该在数据源变化时重新同步', async () => {
      const { retry } = await import('@/utils/retryMechanism');
      retry.mockResolvedValue({ data: 'test' });

      wrapper = mount(DataSyncManager, {
        props: {
          dataSources: mockDataSources
        }
      });

      // 清除初始同步调用
      retry.mockClear();

      // 更新数据源
      const newDataSources = [
        {
          key: 'new-data',
          fetchFn: vi.fn().mockResolvedValue({ data: 'new data' }),
          cacheKey: 'new-cache',
          updateStore: vi.fn()
        }
      ];

      await wrapper.setProps({ dataSources: newDataSources });
      await wrapper.vm.$nextTick();

      expect(retry).toHaveBeenCalled();
    });
  });
});