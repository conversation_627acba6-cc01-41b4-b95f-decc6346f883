import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import ToastSystem from '../ToastSystem.vue';

// Mock uni-app APIs
global.uni = {
  vibrateShort: vi.fn()
};

// Mock uni-app components
vi.mock('uni-icons', () => ({
  default: {
    name: 'uni-icons',
    template: '<div class="uni-icons-mock" :data-type="type" :data-size="size" :data-color="color"></div>',
    props: ['type', 'size', 'color']
  }
}));

describe('ToastSystem', () => {
  let wrapper;

  beforeEach(() => {
    vi.clearAllMocks();
    vi.useFakeTimers();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
    vi.useRealTimers();
  });

  describe('Basic Functionality', () => {
    it('should render toast container with correct position', () => {
      wrapper = mount(ToastSystem, {
        props: {
          position: 'top-right',
          maxCount: 3
        }
      });

      expect(wrapper.find('.toast-container').exists()).toBe(true);
      expect(wrapper.find('.toast-container').classes()).toContain('toast-position-top-right');
    });

    it('should show toast with correct content', async () => {
      wrapper = mount(ToastSystem);

      const toastId = wrapper.vm.show({
        type: 'success',
        title: 'Success Title',
        message: 'Success message',
        showIcon: true
      });

      await wrapper.vm.$nextTick();

      expect(wrapper.find('.toast-item').exists()).toBe(true);
      expect(wrapper.find('.toast-success').exists()).toBe(true);
      expect(wrapper.text()).toContain('Success Title');
      expect(wrapper.text()).toContain('Success message');
      expect(wrapper.find('.toast-icon').exists()).toBe(true);
      expect(toastId).toBe(1);
    });

    it('should remove toast after duration', async () => {
      wrapper = mount(ToastSystem, {
        props: {
          defaultDuration: 1000
        }
      });

      wrapper.vm.show({
        type: 'info',
        message: 'Test message'
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(1);

      vi.advanceTimersByTime(1000);
      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(0);
    });

    it('should not auto-remove toast when duration is 0', async () => {
      wrapper = mount(ToastSystem);

      wrapper.vm.show({
        type: 'loading',
        message: 'Loading...',
        duration: 0
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(1);

      vi.advanceTimersByTime(5000);
      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(1);
    });
  });

  describe('Toast Types', () => {
    beforeEach(() => {
      wrapper = mount(ToastSystem);
    });

    it('should show success toast', async () => {
      wrapper.vm.success('Operation successful');
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.toast-success').exists()).toBe(true);
      expect(wrapper.text()).toContain('Operation successful');
    });

    it('should show error toast', async () => {
      wrapper.vm.error('Something went wrong');
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.toast-error').exists()).toBe(true);
      expect(wrapper.text()).toContain('Something went wrong');
    });

    it('should show warning toast', async () => {
      wrapper.vm.warning('Please be careful');
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.toast-warning').exists()).toBe(true);
      expect(wrapper.text()).toContain('Please be careful');
    });

    it('should show info toast', async () => {
      wrapper.vm.info('Here is some info');
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.toast-info').exists()).toBe(true);
      expect(wrapper.text()).toContain('Here is some info');
    });

    it('should show loading toast', async () => {
      wrapper.vm.loading('Loading data...');
      await wrapper.vm.$nextTick();

      expect(wrapper.find('.toast-loading').exists()).toBe(true);
      expect(wrapper.text()).toContain('Loading data...');
    });
  });

  describe('Toast Management', () => {
    beforeEach(() => {
      wrapper = mount(ToastSystem, {
        props: {
          maxCount: 3
        }
      });
    });

    it('should limit maximum number of toasts', async () => {
      // Add more toasts than maxCount
      for (let i = 0; i < 5; i++) {
        wrapper.vm.show({
          type: 'info',
          message: `Toast ${i + 1}`
        });
      }

      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(3);
    });

    it('should remove specific toast by id', async () => {
      const id1 = wrapper.vm.show({ type: 'info', message: 'Toast 1' });
      const id2 = wrapper.vm.show({ type: 'info', message: 'Toast 2' });
      const id3 = wrapper.vm.show({ type: 'info', message: 'Toast 3' });

      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(3);

      wrapper.vm.removeToast(id2);
      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(2);
      expect(wrapper.text()).not.toContain('Toast 2');
    });

    it('should clear all toasts', async () => {
      wrapper.vm.show({ type: 'info', message: 'Toast 1' });
      wrapper.vm.show({ type: 'info', message: 'Toast 2' });
      wrapper.vm.show({ type: 'info', message: 'Toast 3' });

      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(3);

      wrapper.vm.clear();
      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(0);
    });
  });

  describe('Toast Interaction', () => {
    beforeEach(() => {
      wrapper = mount(ToastSystem);
    });

    it('should handle toast click', async () => {
      const clickHandler = vi.fn();
      
      wrapper.vm.show({
        type: 'info',
        message: 'Clickable toast',
        onClick: clickHandler
      });

      await wrapper.vm.$nextTick();

      await wrapper.find('.toast-item').trigger('click');
      expect(clickHandler).toHaveBeenCalled();
      expect(wrapper.emitted('click')).toBeTruthy();
    });

    it('should handle close button click', async () => {
      wrapper.vm.show({
        type: 'info',
        message: 'Closable toast',
        closable: true
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.find('.toast-close').exists()).toBe(true);

      await wrapper.find('.toast-close').trigger('click');
      await wrapper.vm.$nextTick();
      expect(wrapper.findAll('.toast-item')).toHaveLength(0);
    });

    it('should call onClose callback when toast is removed', async () => {
      const closeHandler = vi.fn();
      
      const toastId = wrapper.vm.show({
        type: 'info',
        message: 'Toast with callback',
        onClose: closeHandler
      });

      await wrapper.vm.$nextTick();

      wrapper.vm.removeToast(toastId);
      await wrapper.vm.$nextTick();
      
      expect(closeHandler).toHaveBeenCalled();
      expect(wrapper.emitted('close')).toBeTruthy();
    });
  });

  describe('Toast Customization', () => {
    beforeEach(() => {
      wrapper = mount(ToastSystem);
    });

    it('should apply different sizes', async () => {
      wrapper.vm.show({
        type: 'info',
        message: 'Small toast',
        size: 'small'
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.find('.toast-small').exists()).toBe(true);
    });

    it('should show/hide icon based on showIcon prop', async () => {
      wrapper.vm.show({
        type: 'success',
        message: 'No icon toast',
        showIcon: false
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.find('.toast-icon').exists()).toBe(false);
    });

    it('should show/hide progress bar based on showProgress prop', async () => {
      wrapper.vm.show({
        type: 'info',
        message: 'Toast with progress',
        showProgress: true,
        duration: 2000
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.find('.toast-progress').exists()).toBe(true);
    });

    it('should show/hide close button based on closable prop', async () => {
      wrapper.vm.show({
        type: 'info',
        message: 'Non-closable toast',
        closable: false
      });

      await wrapper.vm.$nextTick();
      expect(wrapper.find('.toast-close').exists()).toBe(false);
    });
  });

  describe('Haptic Feedback', () => {
    beforeEach(() => {
      wrapper = mount(ToastSystem);
    });

    it('should trigger light haptic feedback for success', async () => {
      wrapper.vm.success('Success message');
      await wrapper.vm.$nextTick();
      
      expect(global.uni.vibrateShort).toHaveBeenCalledWith({ type: 'light' });
    });

    it('should trigger heavy haptic feedback for error', async () => {
      wrapper.vm.error('Error message');
      await wrapper.vm.$nextTick();
      
      expect(global.uni.vibrateShort).toHaveBeenCalledWith({ type: 'heavy' });
    });

    it('should trigger medium haptic feedback for warning', async () => {
      wrapper.vm.warning('Warning message');
      await wrapper.vm.$nextTick();
      
      expect(global.uni.vibrateShort).toHaveBeenCalledWith({ type: 'medium' });
    });

    it('should handle haptic feedback errors gracefully', async () => {
      global.uni.vibrateShort.mockImplementation(() => {
        throw new Error('Haptic not supported');
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      wrapper.vm.success('Success message');
      await wrapper.vm.$nextTick();

      expect(consoleSpy).toHaveBeenCalledWith('Haptic feedback not supported:', expect.any(Error));
      consoleSpy.mockRestore();
    });
  });

  describe('Position Variants', () => {
    const positions = [
      'top', 'bottom', 'center', 
      'top-left', 'top-right', 
      'bottom-left', 'bottom-right'
    ];

    positions.forEach(position => {
      it(`should apply correct class for ${position} position`, () => {
        wrapper = mount(ToastSystem, {
          props: { position }
        });

        expect(wrapper.find('.toast-container').classes())
          .toContain(`toast-position-${position}`);
      });
    });
  });

  describe('Icon and Color Mapping', () => {
    beforeEach(() => {
      wrapper = mount(ToastSystem);
    });

    it('should use correct icon types for different toast types', async () => {
      const types = ['success', 'error', 'warning', 'info', 'loading'];
      
      for (const type of types) {
        wrapper.vm.show({ type, message: `${type} message` });
      }

      await wrapper.vm.$nextTick();

      const iconType = wrapper.vm.getIconType('success');
      const iconSize = wrapper.vm.getIconSize('medium');
      const iconColor = wrapper.vm.getIconColor('success');

      expect(typeof iconType).toBe('string');
      expect(typeof iconSize).toBe('number');
      expect(typeof iconColor).toBe('string');
    });
  });

  describe('Exposed Methods', () => {
    beforeEach(() => {
      wrapper = mount(ToastSystem);
    });

    it('should expose all required methods', () => {
      expect(typeof wrapper.vm.show).toBe('function');
      expect(typeof wrapper.vm.success).toBe('function');
      expect(typeof wrapper.vm.error).toBe('function');
      expect(typeof wrapper.vm.warning).toBe('function');
      expect(typeof wrapper.vm.info).toBe('function');
      expect(typeof wrapper.vm.loading).toBe('function');
      expect(typeof wrapper.vm.removeToast).toBe('function');
      expect(typeof wrapper.vm.clear).toBe('function');
    });
  });
});

// 性能测试
describe('ToastSystem Performance', () => {
  it('should handle many toasts efficiently', async () => {
    const wrapper = mount(ToastSystem, {
      props: { maxCount: 10 }
    });

    const startTime = performance.now();

    // 创建大量 toast
    for (let i = 0; i < 50; i++) {
      wrapper.vm.show({
        type: 'info',
        message: `Toast ${i}`,
        duration: 0
      });
    }

    await wrapper.vm.$nextTick();

    const endTime = performance.now();
    const createTime = endTime - startTime;

    // 创建时间应该在合理范围内
    expect(createTime).toBeLessThan(100);
    expect(wrapper.findAll('.toast-item')).toHaveLength(10); // 受 maxCount 限制

    wrapper.unmount();
  });

  it('should clean up timers properly', () => {
    vi.useFakeTimers();
    
    const wrapper = mount(ToastSystem);
    
    // 创建多个有定时器的 toast
    for (let i = 0; i < 5; i++) {
      wrapper.vm.show({
        type: 'info',
        message: `Toast ${i}`,
        duration: 1000
      });
    }

    // 在定时器完成前卸载组件
    wrapper.unmount();

    // 推进时间，确保没有错误
    expect(() => {
      vi.advanceTimersByTime(2000);
    }).not.toThrow();

    vi.useRealTimers();
  });
});