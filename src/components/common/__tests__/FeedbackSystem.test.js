import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import FeedbackSystem from '../FeedbackSystem.vue';

// Mock uni-app APIs
global.uni = {
  vibrateShort: vi.fn(),
  vibrateLong: vi.fn()
};

// Mock uni-app components
vi.mock('uni-icons', () => ({
  default: {
    name: 'uni-icons',
    template: '<div class="uni-icons-mock" :data-type="type" :data-size="size" :data-color="color"></div>',
    props: ['type', 'size', 'color']
  }
}));

describe('FeedbackSystem', () => {
  let wrapper;

  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('Loading State', () => {
    it('should render spinner loading variant', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'loading',
          variant: 'spinner',
          message: 'Loading...'
        }
      });

      expect(wrapper.find('.feedback-loading').exists()).toBe(true);
      expect(wrapper.find('.spinner-container').exists()).toBe(true);
      expect(wrapper.find('.spinner').exists()).toBe(true);
      expect(wrapper.text()).toContain('Loading...');
    });

    it('should render dots loading variant', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'loading',
          variant: 'dots',
          message: 'Processing...'
        }
      });

      expect(wrapper.find('.dots-container').exists()).toBe(true);
      expect(wrapper.findAll('.dot')).toHaveLength(3);
      expect(wrapper.text()).toContain('Processing...');
    });

    it('should render pulse loading variant', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'loading',
          variant: 'pulse',
          size: 'large'
        }
      });

      expect(wrapper.find('.pulse-container').exists()).toBe(true);
      expect(wrapper.find('.pulse-circle').exists()).toBe(true);
      expect(wrapper.find('.pulse-circle').classes()).toContain('size-large');
    });

    it('should render skeleton loading variant', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'loading',
          variant: 'skeleton',
          skeletonLines: 4
        }
      });

      expect(wrapper.find('.skeleton-container').exists()).toBe(true);
      expect(wrapper.findAll('.skeleton-line')).toHaveLength(4);
    });

    it('should render progress loading variant', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'loading',
          variant: 'progress',
          progress: 50,
          message: 'Uploading...'
        }
      });

      expect(wrapper.find('.progress-container').exists()).toBe(true);
      expect(wrapper.find('progress').exists()).toBe(true);
      expect(wrapper.find('progress').attributes('percent')).toBe('50');
      expect(wrapper.text()).toContain('Uploading...');
    });
  });

  describe('Success State', () => {
    it('should render success feedback', async () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'success',
          message: 'Operation completed successfully',
          size: 'medium'
        }
      });

      await wrapper.vm.$nextTick();

      expect(wrapper.find('.feedback-success').exists()).toBe(true);
      expect(wrapper.find('.success-icon').exists()).toBe(true);
      expect(wrapper.find('.checkmark').exists()).toBe(true);
      expect(wrapper.text()).toContain('Operation completed successfully');
      expect(wrapper.find('.success-icon').classes()).toContain('size-medium');
    });

    it('should trigger haptic feedback on success', async () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'success',
          message: 'Success!',
          hapticFeedback: true
        }
      });

      await wrapper.vm.$nextTick();
      expect(global.uni.vibrateShort).toHaveBeenCalledWith({ type: 'light' });
    });
  });

  describe('Error State', () => {
    it('should render error feedback with retry button', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'error',
          message: 'Something went wrong',
          showRetry: true
        }
      });

      expect(wrapper.find('.feedback-error').exists()).toBe(true);
      expect(wrapper.find('.error-icon').exists()).toBe(true);
      expect(wrapper.find('.error-cross').exists()).toBe(true);
      expect(wrapper.find('.retry-button').exists()).toBe(true);
      expect(wrapper.text()).toContain('Something went wrong');
      expect(wrapper.text()).toContain('重试');
    });

    it('should emit retry event when retry button is clicked', async () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'error',
          message: 'Error occurred',
          showRetry: true
        }
      });

      await wrapper.find('.retry-button').trigger('click');
      expect(wrapper.emitted('retry')).toBeTruthy();
    });

    it('should trigger heavy haptic feedback on error', async () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'error',
          message: 'Error!',
          hapticFeedback: true
        }
      });

      await wrapper.vm.$nextTick();
      expect(global.uni.vibrateShort).toHaveBeenCalledWith({ type: 'heavy' });
    });
  });

  describe('Warning State', () => {
    it('should render warning feedback', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'warning',
          message: 'Please check your input'
        }
      });

      expect(wrapper.find('.feedback-warning').exists()).toBe(true);
      expect(wrapper.find('.warning-icon').exists()).toBe(true);
      expect(wrapper.find('.warning-symbol').exists()).toBe(true);
      expect(wrapper.text()).toContain('Please check your input');
    });

    it('should trigger medium haptic feedback on warning', async () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'warning',
          message: 'Warning!',
          hapticFeedback: true
        }
      });

      await wrapper.vm.$nextTick();
      expect(global.uni.vibrateShort).toHaveBeenCalledWith({ type: 'medium' });
    });
  });

  describe('Info State', () => {
    it('should render info feedback', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'info',
          message: 'Here is some information'
        }
      });

      expect(wrapper.find('.feedback-info').exists()).toBe(true);
      expect(wrapper.find('.info-icon').exists()).toBe(true);
      expect(wrapper.find('.info-symbol').exists()).toBe(true);
      expect(wrapper.text()).toContain('Here is some information');
    });
  });

  describe('Empty State', () => {
    it('should render empty feedback with action button', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'empty',
          message: 'No data available',
          showAction: true,
          actionText: 'Refresh'
        }
      });

      expect(wrapper.find('.feedback-empty').exists()).toBe(true);
      expect(wrapper.find('.empty-icon').exists()).toBe(true);
      expect(wrapper.find('.action-button').exists()).toBe(true);
      expect(wrapper.text()).toContain('No data available');
      expect(wrapper.text()).toContain('Refresh');
    });

    it('should emit action event when action button is clicked', async () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'empty',
          showAction: true
        }
      });

      await wrapper.find('.action-button').trigger('click');
      expect(wrapper.emitted('action')).toBeTruthy();
    });

    it('should show default empty message when no message provided', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'empty'
        }
      });

      expect(wrapper.text()).toContain('暂无数据');
    });
  });

  describe('Props and Customization', () => {
    it('should apply correct size classes', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'success',
          size: 'large'
        }
      });

      expect(wrapper.find('.success-icon').classes()).toContain('size-large');
    });

    it('should apply correct animation classes', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'info',
          animation: 'bounce'
        }
      });

      expect(wrapper.find('.feedback-info').classes()).toContain('animation-bounce');
    });

    it('should validate type prop', () => {
      const validator = FeedbackSystem.props.type.validator;
      expect(validator('loading')).toBe(true);
      expect(validator('success')).toBe(true);
      expect(validator('error')).toBe(true);
      expect(validator('warning')).toBe(true);
      expect(validator('info')).toBe(true);
      expect(validator('empty')).toBe(true);
      expect(validator('invalid')).toBe(false);
    });

    it('should validate variant prop', () => {
      const validator = FeedbackSystem.props.variant.validator;
      expect(validator('spinner')).toBe(true);
      expect(validator('dots')).toBe(true);
      expect(validator('pulse')).toBe(true);
      expect(validator('progress')).toBe(true);
      expect(validator('skeleton')).toBe(true);
      expect(validator('invalid')).toBe(false);
    });

    it('should validate size prop', () => {
      const validator = FeedbackSystem.props.size.validator;
      expect(validator('small')).toBe(true);
      expect(validator('medium')).toBe(true);
      expect(validator('large')).toBe(true);
      expect(validator('invalid')).toBe(false);
    });

    it('should validate animation prop', () => {
      const validator = FeedbackSystem.props.animation.validator;
      expect(validator('fade')).toBe(true);
      expect(validator('slide')).toBe(true);
      expect(validator('bounce')).toBe(true);
      expect(validator('zoom')).toBe(true);
      expect(validator('invalid')).toBe(false);
    });
  });

  describe('Auto Hide Functionality', () => {
    it('should auto hide after specified duration', async () => {
      vi.useFakeTimers();
      
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'success',
          message: 'Auto hide test',
          autoHide: 1000
        }
      });

      expect(wrapper.emitted('hide')).toBeFalsy();

      vi.advanceTimersByTime(1000);
      await wrapper.vm.$nextTick();

      expect(wrapper.emitted('hide')).toBeTruthy();

      vi.useRealTimers();
    });

    it('should not auto hide when autoHide is 0', async () => {
      vi.useFakeTimers();
      
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'loading',
          autoHide: 0
        }
      });

      vi.advanceTimersByTime(5000);
      await wrapper.vm.$nextTick();

      expect(wrapper.emitted('hide')).toBeFalsy();

      vi.useRealTimers();
    });
  });

  describe('Exposed Methods', () => {
    it('should expose hide method', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'info',
          message: 'Test'
        }
      });

      expect(typeof wrapper.vm.hide).toBe('function');
      
      wrapper.vm.hide();
      expect(wrapper.emitted('hide')).toBeTruthy();
    });

    it('should expose triggerHapticFeedback method', () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'info',
          message: 'Test'
        }
      });

      expect(typeof wrapper.vm.triggerHapticFeedback).toBe('function');
      
      wrapper.vm.triggerHapticFeedback('medium');
      expect(global.uni.vibrateShort).toHaveBeenCalledWith({ type: 'medium' });
    });
  });

  describe('Haptic Feedback', () => {
    it('should not trigger haptic feedback when disabled', async () => {
      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'success',
          message: 'Success!',
          hapticFeedback: false
        }
      });

      await wrapper.vm.$nextTick();
      expect(global.uni.vibrateShort).not.toHaveBeenCalled();
    });

    it('should handle haptic feedback errors gracefully', async () => {
      global.uni.vibrateShort.mockImplementation(() => {
        throw new Error('Haptic not supported');
      });

      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});

      wrapper = mount(FeedbackSystem, {
        props: {
          type: 'error',
          message: 'Error!',
          hapticFeedback: true
        }
      });

      await wrapper.vm.$nextTick();
      expect(consoleSpy).toHaveBeenCalledWith('Haptic feedback not supported:', expect.any(Error));

      consoleSpy.mockRestore();
    });
  });
});

// 性能测试
describe('FeedbackSystem Performance', () => {
  it('should handle rapid state changes efficiently', async () => {
    const wrapper = mount(FeedbackSystem, {
      props: {
        type: 'loading',
        variant: 'spinner'
      }
    });

    const startTime = performance.now();

    // 快速切换状态
    const states = ['loading', 'success', 'error', 'warning', 'info', 'empty'];
    for (let i = 0; i < 20; i++) {
      await wrapper.setProps({ type: states[i % states.length] });
    }

    const endTime = performance.now();
    const updateTime = endTime - startTime;

    // 更新时间应该在合理范围内
    expect(updateTime).toBeLessThan(100);

    wrapper.unmount();
  });

  it('should not cause memory leaks with timers', () => {
    vi.useFakeTimers();
    
    const wrapper = mount(FeedbackSystem, {
      props: {
        type: 'success',
        autoHide: 1000
      }
    });

    // 在定时器完成前卸载组件
    wrapper.unmount();

    // 推进时间，确保没有错误
    expect(() => {
      vi.advanceTimersByTime(2000);
    }).not.toThrow();

    vi.useRealTimers();
  });
});