import { describe, it, expect, vi, beforeEach } from 'vitest';
import { mount } from '@vue/test-utils';
import OptimizedImageGrid from '../OptimizedImageGrid.vue';

// Mock FavoriteButton component
vi.mock('../FavoriteButton.vue', () => ({
  default: {
    name: 'FavoriteButton',
    template: '<div class="favorite-button-mock"></div>',
    props: ['itemId', 'itemType', 'size', 'variant']
  }
}));

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn()
}));

describe('OptimizedImageGrid', () => {
  const mockItems = [
    {
      id: '1',
      name: '测试衣物1',
      categoryDisplay: '上衣',
      imageUrls: ['https://example.com/image1.jpg']
    },
    {
      id: '2',
      name: '测试衣物2',
      categoryDisplay: '裤子',
      imageUrls: ['https://example.com/image2.jpg']
    }
  ];

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders items correctly', () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: mockItems,
        itemType: 'clothing'
      }
    });

    expect(wrapper.findAll('.grid-item')).toHaveLength(2);
    expect(wrapper.find('.item-title').text()).toBe('测试衣物1');
  });

  it('shows empty state when no items', () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: [],
        emptyText: '暂无数据'
      }
    });

    expect(wrapper.find('.empty-state').exists()).toBe(true);
    expect(wrapper.find('.empty-text').text()).toBe('暂无数据');
  });

  it('applies correct grid class based on columns config', () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: mockItems,
        columns: { mobile: 2, tablet: 3, desktop: 4 }
      }
    });

    expect(wrapper.find('.optimized-image-grid').classes()).toContain('grid-columns-2-3-4');
  });

  it('emits item-click event when item is clicked', async () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: mockItems,
        itemType: 'clothing'
      }
    });

    await wrapper.find('.grid-item').trigger('click');
    expect(wrapper.emitted('item-click')).toBeTruthy();
    expect(wrapper.emitted('item-click')[0][0]).toEqual({
      item: mockItems[0],
      index: 0
    });
  });

  it('handles image load correctly', async () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: mockItems,
        enableLazyLoad: false // Disable lazy load for testing
      }
    });

    const vm = wrapper.vm;
    vm.handleImageLoad(0);
    
    expect(vm.loadedImages.has(0)).toBe(true);
    expect(vm.errorImages.has(0)).toBe(false);
  });

  it('handles image error correctly', async () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: mockItems,
        enableLazyLoad: false
      }
    });

    const vm = wrapper.vm;
    const mockEvent = { target: { src: '' } };
    
    vm.handleImageError(mockEvent, 0);
    
    expect(vm.errorImages.has(0)).toBe(true);
    expect(vm.loadedImages.has(0)).toBe(false);
    expect(mockEvent.target.src).toBe('/static/images/empty-collection.png');
  });

  it('shows favorite button for clothing and outfit types', () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: mockItems,
        itemType: 'clothing',
        showFavoriteButton: true
      }
    });

    expect(wrapper.find('.favorite-button-mock').exists()).toBe(true);
  });

  it('hides favorite button for collection type', () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: mockItems,
        itemType: 'collection',
        showFavoriteButton: true
      }
    });

    expect(wrapper.find('.favorite-button-mock').exists()).toBe(false);
  });

  it('displays correct subtitle for different item types', () => {
    const clothingWrapper = mount(OptimizedImageGrid, {
      props: {
        items: [{ id: '1', name: '衣物', categoryDisplay: '上衣' }],
        itemType: 'clothing'
      }
    });

    const outfitWrapper = mount(OptimizedImageGrid, {
      props: {
        items: [{ id: '1', name: '搭配', occasion: '正式' }],
        itemType: 'outfit'
      }
    });

    const collectionWrapper = mount(OptimizedImageGrid, {
      props: {
        items: [{ id: '1', name: '收藏集', stats: { clothingCount: 5, outfitCount: 3 } }],
        itemType: 'collection'
      }
    });

    expect(clothingWrapper.find('.item-subtitle').text()).toBe('上衣');
    expect(outfitWrapper.find('.item-subtitle').text()).toBe('正式');
    expect(collectionWrapper.find('.item-subtitle').text()).toBe('5 衣物 · 3 搭配');
  });

  it('maintains 1:1.2 aspect ratio for images', () => {
    const wrapper = mount(OptimizedImageGrid, {
      props: {
        items: mockItems
      }
    });

    const imageWrapper = wrapper.find('.image-wrapper');
    const styles = getComputedStyle(imageWrapper.element);
    
    // The component should have padding-bottom: 120% for 1:1.2 aspect ratio
    expect(imageWrapper.classes()).toContain('image-wrapper');
  });
});