import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import EnhancedVirtualList from '../EnhancedVirtualList.vue';

// Mock uni-app components
global.uni = {
  createIntersectionObserver: vi.fn(() => ({
    relativeToViewport: vi.fn(() => ({
      observe: vi.fn()
    })),
    disconnect: vi.fn()
  }))
};

describe('EnhancedVirtualList', () => {
  let wrapper;

  const mockItems = Array.from({ length: 100 }, (_, index) => ({
    id: `item-${index}`,
    name: `Item ${index}`,
    content: `Content for item ${index}`
  }));

  beforeEach(() => {
    wrapper = mount(EnhancedVirtualList, {
      props: {
        items: mockItems,
        itemHeight: 200,
        containerHeight: 600,
        bufferSize: 5
      },
      slots: {
        default: ({ item, index }) => `<div class="test-item">${item.name}</div>`
      }
    });
  });

  afterEach(() => {
    if (wrapper) {
      wrapper.unmount();
    }
  });

  describe('基本渲染', () => {
    it('应该正确渲染虚拟列表容器', () => {
      expect(wrapper.find('.enhanced-virtual-list').exists()).toBe(true);
      expect(wrapper.find('.virtual-scroll').exists()).toBe(true);
    });

    it('应该设置正确的容器高度', () => {
      const container = wrapper.find('.enhanced-virtual-list');
      expect(container.attributes('style')).toContain('height: 600px');
    });

    it('应该渲染可见项目', () => {
      expect(wrapper.findAll('.virtual-item').length).toBeGreaterThan(0);
    });
  });

  describe('虚拟化计算', () => {
    it('应该正确计算可见项目数量', () => {
      // 容器高度600px，项目高度200px，缓冲区5，应该渲染约13个项目
      const visibleItems = wrapper.vm.visibleItems;
      expect(visibleItems.length).toBeGreaterThan(0);
      expect(visibleItems.length).toBeLessThanOrEqual(20); // 3 + 5*2 + 余量
    });

    it('应该正确计算开始和结束索引', () => {
      expect(wrapper.vm.startIndex).toBeGreaterThanOrEqual(0);
      expect(wrapper.vm.endIndex).toBeGreaterThan(wrapper.vm.startIndex);
      expect(wrapper.vm.endIndex).toBeLessThanOrEqual(mockItems.length);
    });

    it('应该正确计算偏移量', () => {
      expect(wrapper.vm.startOffset).toBeGreaterThanOrEqual(0);
      expect(wrapper.vm.endOffset).toBeGreaterThanOrEqual(0);
    });
  });

  describe('滚动处理', () => {
    it('应该处理滚动事件', async () => {
      const scrollEvent = {
        detail: { scrollTop: 400 }
      };

      await wrapper.find('.virtual-scroll').trigger('scroll', scrollEvent);

      expect(wrapper.emitted('scroll')).toBeTruthy();
      expect(wrapper.emitted('scroll')[0][0]).toEqual(scrollEvent);
    });

    it('应该更新当前滚动位置', async () => {
      const scrollEvent = {
        detail: { scrollTop: 400 }
      };

      wrapper.vm.handleScroll(scrollEvent);
      await nextTick();

      expect(wrapper.vm.currentScrollTop).toBe(400);
    });

    it('应该在滚动到底部时触发加载更多', async () => {
      await wrapper.setProps({ hasMore: true });

      await wrapper.find('.virtual-scroll').trigger('scrolltolower');

      expect(wrapper.emitted('load-more')).toBeTruthy();
    });
  });

  describe('动态高度支持', () => {
    beforeEach(async () => {
      await wrapper.setProps({
        dynamicHeight: true,
        minItemHeight: 100,
        maxItemHeight: 300
      });
    });

    it('应该支持动态高度模式', () => {
      expect(wrapper.props('dynamicHeight')).toBe(true);
    });

    it('应该更新项目高度', () => {
      wrapper.vm.updateItemHeight(0, 250);
      expect(wrapper.vm.itemHeights.get(0)).toBe(250);
    });

    it('应该限制项目高度在最小和最大值之间', () => {
      wrapper.vm.updateItemHeight(0, 50); // 小于最小值
      expect(wrapper.vm.itemHeights.get(0)).toBe(100);

      wrapper.vm.updateItemHeight(1, 400); // 大于最大值
      expect(wrapper.vm.itemHeights.get(1)).toBe(300);
    });
  });

  describe('键值处理', () => {
    it('应该使用默认键值字段', () => {
      const item = mockItems[0];
      const key = wrapper.vm.getItemKey(item, 0);
      expect(key).toBe('item-0');
    });

    it('应该支持自定义键值字段', async () => {
      await wrapper.setProps({ keyField: 'name' });
      
      const item = mockItems[0];
      const key = wrapper.vm.getItemKey(item, 0);
      expect(key).toBe('Item 0');
    });

    it('应该支持键值函数', async () => {
      const keyFunction = (item, index) => `custom-${index}`;
      await wrapper.setProps({ keyField: keyFunction });
      
      const item = mockItems[0];
      const key = wrapper.vm.getItemKey(item, 0);
      expect(key).toBe('custom-0');
    });

    it('应该回退到索引作为键值', () => {
      const item = { noId: true };
      const key = wrapper.vm.getItemKey(item, 5);
      expect(key).toBe(5);
    });
  });

  describe('滚动控制', () => {
    it('应该支持滚动到顶部', async () => {
      const scrollToSpy = vi.spyOn(wrapper.vm, 'scrollTo');
      
      wrapper.vm.scrollToTop();
      
      expect(scrollToSpy).toHaveBeenCalledWith(0);
    });

    it('应该支持滚动到指定位置', async () => {
      wrapper.vm.scrollTo(500);
      
      expect(wrapper.vm.scrollTop).toBe(500);
      expect(wrapper.vm.currentScrollTop).toBe(500);
    });

    it('应该支持滚动到指定项目', () => {
      wrapper.vm.scrollToItem(10);
      
      // 项目10应该在位置 10 * 200 = 2000
      expect(wrapper.vm.currentScrollTop).toBe(2000);
    });

    it('应该处理无效的项目索引', () => {
      const initialScrollTop = wrapper.vm.currentScrollTop;
      
      wrapper.vm.scrollToItem(-1);
      expect(wrapper.vm.currentScrollTop).toBe(initialScrollTop);
      
      wrapper.vm.scrollToItem(1000);
      expect(wrapper.vm.currentScrollTop).toBe(initialScrollTop);
    });
  });

  describe('回到顶部功能', () => {
    it('应该在滚动超过阈值时显示回到顶部按钮', async () => {
      await wrapper.setProps({ showBackToTop: true, backToTopThreshold: 300 });
      
      // 模拟滚动
      wrapper.vm.currentScrollTop = 400;
      await nextTick();
      
      expect(wrapper.find('.back-to-top').exists()).toBe(true);
    });

    it('应该在滚动未超过阈值时隐藏回到顶部按钮', async () => {
      await wrapper.setProps({ showBackToTop: true, backToTopThreshold: 300 });
      
      wrapper.vm.currentScrollTop = 200;
      await nextTick();
      
      expect(wrapper.find('.back-to-top').exists()).toBe(false);
    });

    it('应该在点击回到顶部按钮时滚动到顶部', async () => {
      await wrapper.setProps({ showBackToTop: true });
      wrapper.vm.currentScrollTop = 500;
      await nextTick();
      
      const scrollToTopSpy = vi.spyOn(wrapper.vm, 'scrollToTop');
      
      await wrapper.find('.back-to-top').trigger('click');
      
      expect(scrollToTopSpy).toHaveBeenCalled();
    });
  });

  describe('滚动指示器', () => {
    it('应该在启用时显示滚动指示器', async () => {
      await wrapper.setProps({ showScrollIndicator: true });
      
      expect(wrapper.find('.scroll-indicator').exists()).toBe(true);
    });

    it('应该计算正确的指示器样式', async () => {
      await wrapper.setProps({ showScrollIndicator: true });
      
      wrapper.vm.currentScrollTop = 1000;
      await nextTick();
      
      const indicatorStyle = wrapper.vm.indicatorStyle;
      expect(indicatorStyle.height).toBeDefined();
      expect(indicatorStyle.top).toBeDefined();
    });
  });

  describe('空状态处理', () => {
    it('应该在没有数据时显示空状态', async () => {
      await wrapper.setProps({ items: [], loading: false });
      
      expect(wrapper.find('.empty-state').exists()).toBe(true);
      expect(wrapper.text()).toContain('暂无数据');
    });

    it('应该支持自定义空状态', async () => {
      const wrapperWithCustomEmpty = mount(EnhancedVirtualList, {
        props: {
          items: [],
          loading: false
        },
        slots: {
          empty: '<div class="custom-empty">自定义空状态</div>'
        }
      });
      
      expect(wrapperWithCustomEmpty.find('.custom-empty').exists()).toBe(true);
      expect(wrapperWithCustomEmpty.text()).toContain('自定义空状态');
      
      wrapperWithCustomEmpty.unmount();
    });

    it('应该在加载时隐藏空状态', async () => {
      await wrapper.setProps({ items: [], loading: true });
      
      expect(wrapper.find('.empty-state').exists()).toBe(false);
    });
  });

  describe('预加载功能', () => {
    it('应该在接近底部时触发预加载', async () => {
      await wrapper.setProps({ 
        hasMore: true, 
        loading: false,
        preloadThreshold: 200
      });

      // 模拟滚动到接近底部
      const scrollEvent = {
        detail: { scrollTop: 19800 } // 接近总高度20000
      };

      wrapper.vm.handleScroll(scrollEvent);

      expect(wrapper.emitted('load-more')).toBeTruthy();
    });

    it('应该在加载中时不触发预加载', async () => {
      await wrapper.setProps({ 
        hasMore: true, 
        loading: true,
        preloadThreshold: 200
      });

      const scrollEvent = {
        detail: { scrollTop: 19800 }
      };

      wrapper.vm.handleScroll(scrollEvent);

      expect(wrapper.emitted('load-more')).toBeFalsy();
    });
  });

  describe('可见性跟踪', () => {
    it('应该跟踪可见项目', () => {
      expect(wrapper.vm.visibleIndexes.size).toBeGreaterThan(0);
    });

    it('应该在项目变为可见时触发事件', async () => {
      // 模拟滚动使新项目可见
      wrapper.vm.currentScrollTop = 1000;
      wrapper.vm.updateVisibleItems();
      await nextTick();

      expect(wrapper.emitted('item-visible')).toBeTruthy();
    });
  });

  describe('重置和刷新', () => {
    it('应该支持重置状态', () => {
      wrapper.vm.currentScrollTop = 1000;
      wrapper.vm.itemHeights.set(0, 300);
      wrapper.vm.visibleIndexes.add(5);

      wrapper.vm.reset();

      expect(wrapper.vm.currentScrollTop).toBe(0);
      expect(wrapper.vm.scrollTop).toBe(0);
      expect(wrapper.vm.itemHeights.size).toBe(0);
      expect(wrapper.vm.visibleIndexes.size).toBe(0);
    });

    it('应该支持刷新列表', async () => {
      const resetSpy = vi.spyOn(wrapper.vm, 'reset');
      const updateVisibleItemsSpy = vi.spyOn(wrapper.vm, 'updateVisibleItems');

      await wrapper.vm.refresh();

      expect(resetSpy).toHaveBeenCalled();
      expect(updateVisibleItemsSpy).toHaveBeenCalled();
    });
  });

  describe('暴露的方法', () => {
    it('应该暴露所有必要的方法', () => {
      const exposedMethods = [
        'scrollTo',
        'scrollToItem',
        'scrollToTop',
        'updateItemHeight',
        'reset',
        'refresh',
        'getVisibleItems',
        'getVisibleIndexes'
      ];

      exposedMethods.forEach(method => {
        expect(typeof wrapper.vm[method]).toBe('function');
      });
    });

    it('应该返回正确的可见项目', () => {
      const visibleItems = wrapper.vm.getVisibleItems();
      expect(Array.isArray(visibleItems)).toBe(true);
      expect(visibleItems.length).toBeGreaterThan(0);
    });

    it('应该返回正确的可见索引', () => {
      const visibleIndexes = wrapper.vm.getVisibleIndexes();
      expect(Array.isArray(visibleIndexes)).toBe(true);
      expect(visibleIndexes.length).toBeGreaterThan(0);
    });
  });
});

// 性能测试
describe('EnhancedVirtualList Performance', () => {
  it('应该高效处理大量数据', () => {
    const startTime = performance.now();

    const largeDataset = Array.from({ length: 10000 }, (_, index) => ({
      id: `item-${index}`,
      name: `Item ${index}`
    }));

    const wrapper = mount(EnhancedVirtualList, {
      props: {
        items: largeDataset,
        itemHeight: 200,
        containerHeight: 600
      },
      slots: {
        default: ({ item }) => `<div>${item.name}</div>`
      }
    });

    const endTime = performance.now();
    const renderTime = endTime - startTime;

    // 渲染时间应该在合理范围内
    expect(renderTime).toBeLessThan(200);

    // 只应该渲染可见项目
    const renderedItems = wrapper.findAll('.virtual-item');
    expect(renderedItems.length).toBeLessThan(50); // 远少于总数据量

    wrapper.unmount();
  });

  it('应该在滚动时保持性能', async () => {
    const largeDataset = Array.from({ length: 5000 }, (_, index) => ({
      id: `item-${index}`,
      name: `Item ${index}`
    }));

    const wrapper = mount(EnhancedVirtualList, {
      props: {
        items: largeDataset,
        itemHeight: 200,
        containerHeight: 600
      }
    });

    // 模拟快速滚动
    const scrollPositions = [0, 1000, 2000, 3000, 4000, 5000];
    
    for (const position of scrollPositions) {
      const startTime = performance.now();
      
      wrapper.vm.handleScroll({ detail: { scrollTop: position } });
      await nextTick();
      
      const scrollTime = performance.now() - startTime;
      expect(scrollTime).toBeLessThan(50); // 每次滚动处理应该很快
    }

    wrapper.unmount();
  });
});