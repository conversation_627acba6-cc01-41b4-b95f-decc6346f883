import { describe, it, expect, vi } from 'vitest';
import { mount } from '@vue/test-utils';
import CompactCard from '../CompactCard.vue';

// Mock FavoriteButton component
vi.mock('../FavoriteButton.vue', () => ({
  default: {
    name: 'FavoriteButton',
    template: '<div class="favorite-button-mock"></div>',
    props: ['itemId', 'itemType', 'size', 'variant']
  }
}));

describe('CompactCard', () => {
  const mockClothingItem = {
    id: '1',
    name: '测试衣物',
    categoryDisplay: '上衣',
    imageUrls: ['https://example.com/image.jpg']
  };

  const mockOutfitItem = {
    id: '2',
    name: '测试搭配',
    occasion: '日常',
    imageUrl: 'https://example.com/outfit.jpg'
  };

  const mockCollectionItem = {
    id: '3',
    name: '测试收藏集',
    cover_image_url: 'https://example.com/collection.jpg',
    stats: {
      clothingCount: 5,
      outfitCount: 3
    }
  };

  it('renders clothing item correctly', () => {
    const wrapper = mount(CompactCard, {
      props: {
        item: mockClothingItem,
        type: 'clothing'
      }
    });

    expect(wrapper.find('.item-title').text()).toBe('测试衣物');
    expect(wrapper.find('.item-subtitle').text()).toBe('上衣');
    expect(wrapper.find('.favorite-button-mock').exists()).toBe(true);
  });

  it('renders outfit item correctly', () => {
    const wrapper = mount(CompactCard, {
      props: {
        item: mockOutfitItem,
        type: 'outfit'
      }
    });

    expect(wrapper.find('.item-title').text()).toBe('测试搭配');
    expect(wrapper.find('.item-subtitle').text()).toBe('日常');
    expect(wrapper.find('.favorite-button-mock').exists()).toBe(true);
  });

  it('renders collection item correctly', () => {
    const wrapper = mount(CompactCard, {
      props: {
        item: mockCollectionItem,
        type: 'collection'
      }
    });

    expect(wrapper.find('.item-title').text()).toBe('测试收藏集');
    expect(wrapper.find('.item-subtitle').text()).toBe('5 衣物 · 3 搭配');
    expect(wrapper.find('.favorite-button-mock').exists()).toBe(false);
  });

  it('emits click event when clicked', async () => {
    const wrapper = mount(CompactCard, {
      props: {
        item: mockClothingItem,
        type: 'clothing'
      }
    });

    await wrapper.find('.compact-card').trigger('click');
    expect(wrapper.emitted('click')).toBeTruthy();
    expect(wrapper.emitted('click')[0]).toEqual([mockClothingItem]);
  });

  it('handles image load correctly', async () => {
    const wrapper = mount(CompactCard, {
      props: {
        item: mockClothingItem,
        type: 'clothing'
      }
    });

    const image = wrapper.find('.card-image');
    await image.trigger('load');
    
    expect(image.classes()).toContain('image-loaded');
  });

  it('handles image error correctly', async () => {
    const wrapper = mount(CompactCard, {
      props: {
        item: mockClothingItem,
        type: 'clothing'
      }
    });

    const image = wrapper.find('.card-image');
    const mockEvent = { target: { src: '' } };
    
    await wrapper.vm.handleImageError(mockEvent);
    expect(mockEvent.target.src).toBe('/static/images/empty-collection.png');
  });

  it('shows correct card height (280rpx)', () => {
    const wrapper = mount(CompactCard, {
      props: {
        item: mockClothingItem,
        type: 'clothing'
      }
    });

    const card = wrapper.find('.compact-card');
    const styles = getComputedStyle(card.element);
    // Note: In test environment, rpx units might not be converted
    // This test verifies the CSS class is applied correctly
    expect(card.classes()).toContain('compact-card');
  });
});