<!--
/**
 * CompactCard Component
 * 
 * A compact card component with unified dimensions for better layout consistency:
 * - Card height: 280rpx (reduced from 400rpx+)
 * - Image height: 180rpx (reduced from 250rpx+)
 * - Info section: 100rpx
 * - Responsive layout support: 2-3 columns on mobile, 4 columns on tablet
 * 
 * @component CompactCard
 * @example
 * <CompactCard
 *   :item="itemData"
 *   type="clothing"
 *   @click="handleCardClick"
 * />
 */
-->
<template>
  <view class="compact-card" @click="handleClick">
    <view class="image-container">
      <image
        :src="imageUrl"
        mode="aspectFill"
        class="card-image"
        :class="{ 'image-loaded': imageLoaded }"
        @load="handleImageLoad"
        @error="handleImageError"
      />
      <view v-if="!imageLoaded" class="image-placeholder">
        <uni-icons type="image" size="24" color="#ddd" />
      </view>

      <!-- 收藏按钮 (仅衣物和搭配类型显示) -->
      <FavoriteButton
        v-if="showFavoriteButton"
        :item-id="item.id"
        :item-type="type"
        size="small"
        variant="minimal"
        class="favorite-btn"
      />

      <!-- 状态标识 -->
      <view v-if="item.isPublic" class="status-badge public">
        <uni-icons type="eye" size="10" color="#fff" />
        <text class="badge-text">公开</text>
      </view>
    </view>

    <view class="info-section">
      <text class="item-title">{{ item.name || item.title }}</text>
      <text class="item-subtitle">{{ subtitle }}</text>
    </view>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue';
import FavoriteButton from '@/components/common/FavoriteButton.vue';

/**
 * Component Props
 * @typedef {Object} CompactCardProps
 * @property {Object} item - Item data object (required)
 * @property {string} type - Item type: 'clothing', 'outfit', 'collection' (default: 'clothing')
 * @property {boolean} showFavorite - Whether to show favorite button (default: true)
 */
const props = defineProps({
  item: {
    type: Object,
    required: true
  },
  type: {
    type: String,
    default: 'clothing',
    validator: value => ['clothing', 'outfit', 'collection'].includes(value)
  },
  showFavorite: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['click']);

// 状态
const imageLoaded = ref(false);

// 计算属性
const imageUrl = computed(() => {
  if (props.item.imageUrls && props.item.imageUrls.length > 0) {
    return props.item.imageUrls[0];
  }
  if (props.item.imageUrl) {
    return props.item.imageUrl;
  }
  if (props.item.cover_image_url) {
    return props.item.cover_image_url;
  }
  return '/static/images/empty-collection.png';
});

const subtitle = computed(() => {
  switch (props.type) {
    case 'clothing': {
      return props.item.categoryDisplay || props.item.category || '未分类';
    }
    case 'outfit': {
      return props.item.occasion || '日常搭配';
    }
    case 'collection': {
      const clothingCount = props.item.stats?.clothingCount || 0;
      const outfitCount = props.item.stats?.outfitCount || 0;
      return `${clothingCount} 衣物 · ${outfitCount} 搭配`;
    }
    default:
      return '';
  }
});

const showFavoriteButton = computed(() => {
  return props.showFavorite && ['clothing', 'outfit'].includes(props.type);
});

// 事件处理
const handleClick = () => {
  emit('click', props.item);
};

const handleImageLoad = () => {
  imageLoaded.value = true;
};

const handleImageError = e => {
  e.target.src = '/static/images/empty-collection.png';
  imageLoaded.value = true;
};
</script>

<style lang="scss" scoped>
.compact-card {
  height: 280rpx;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;

  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.12);
  }

  &:active {
    transform: translateY(0);
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  }
}

.image-container {
  position: relative;
  height: 180rpx;
  width: 100%;
  background-color: #f5f5f5;
  overflow: hidden;

  .card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    opacity: 0;
    transition: opacity 0.3s ease;

    &.image-loaded {
      opacity: 1;
    }
  }

  .image-placeholder {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    color: #ddd;
  }

  .favorite-btn {
    position: absolute;
    top: 8rpx;
    right: 8rpx;
    z-index: 2;
  }

  .status-badge {
    position: absolute;
    top: 8rpx;
    left: 8rpx;
    padding: 4rpx 8rpx;
    border-radius: 12rpx;
    font-size: 20rpx;
    display: flex;
    align-items: center;
    gap: 4rpx;
    z-index: 1;

    &.public {
      background-color: rgba(0, 0, 0, 0.6);
      color: #fff;
    }

    .badge-text {
      font-size: 18rpx;
      line-height: 1;
    }
  }
}

.info-section {
  height: 100rpx;
  padding: 12rpx 16rpx;
  display: flex;
  flex-direction: column;
  justify-content: center;

  .item-title {
    font-size: 26rpx;
    font-weight: 500;
    color: #333;
    line-height: 1.2;
    margin-bottom: 6rpx;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .item-subtitle {
    font-size: 22rpx;
    color: #666;
    line-height: 1.2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}

// 响应式布局
@media (max-width: 480px) {
  // 移动端小屏幕
  .compact-card {
    // 在网格中会自动适应2-3列
  }
}

@media (min-width: 481px) and (max-width: 768px) {
  // 移动端大屏幕
  .compact-card {
    // 在网格中会自动适应3列
  }
}

@media (min-width: 769px) {
  // 平板及以上
  .compact-card {
    // 在网格中会自动适应4列
  }
}
</style>
