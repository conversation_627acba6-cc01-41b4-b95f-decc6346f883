<template>
  <view class="error-boundary">
    <!-- 正常渲染子组件 -->
    <slot v-if="!hasError" />
    
    <!-- 错误状态显示 -->
    <view v-else class="error-fallback" :class="fallbackClass">
      <!-- 默认错误界面 -->
      <view v-if="!$slots.fallback" class="default-error-ui">
        <view class="error-icon">
          <uni-icons type="info-filled" size="48" color="#ff3b30" />
        </view>
        
        <view class="error-content">
          <text class="error-title">{{ errorTitle }}</text>
          <text class="error-message">{{ errorMessage }}</text>
          
          <!-- 错误详情（开发环境） -->
          <view v-if="showDetails && isDevelopment" class="error-details">
            <text class="error-stack">{{ errorInfo.stack }}</text>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="error-actions">
          <button 
            class="retry-button" 
            @click="handleRetry"
            :disabled="retrying"
          >
            {{ retrying ? '重试中...' : '重试' }}
          </button>
          
          <button 
            v-if="showReportButton" 
            class="report-button" 
            @click="handleReport"
          >
            反馈问题
          </button>
          
          <button 
            v-if="showDetailsToggle && isDevelopment" 
            class="details-button" 
            @click="toggleDetails"
          >
            {{ showDetails ? '隐藏详情' : '显示详情' }}
          </button>
        </view>
      </view>
      
      <!-- 自定义错误界面 -->
      <slot v-else name="fallback" :error="errorInfo" :retry="handleRetry" />
    </view>
  </view>
</template><scr
ipt setup>
import { ref, computed, onErrorCaptured, nextTick } from 'vue';
import { handleError } from '@/utils/errorHandler';

const props = defineProps({
  // 错误标题
  title: {
    type: String,
    default: '出现了一些问题'
  },
  // 错误消息
  message: {
    type: String,
    default: '页面加载失败，请稍后重试'
  },
  // 是否显示重试按钮
  showRetry: {
    type: Boolean,
    default: true
  },
  // 是否显示反馈按钮
  showReport: {
    type: Boolean,
    default: true
  },
  // 是否显示详情切换按钮
  showDetailsToggle: {
    type: Boolean,
    default: true
  },
  // 自动重试次数
  maxRetries: {
    type: Number,
    default: 3
  },
  // 重试延迟（毫秒）
  retryDelay: {
    type: Number,
    default: 1000
  },
  // 错误级别
  level: {
    type: String,
    default: 'error',
    validator: value => ['low', 'medium', 'high', 'critical'].includes(value)
  },
  // 自定义样式类
  fallbackClass: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['error', 'retry', 'report']);

// 响应式数据
const hasError = ref(false);
const errorInfo = ref(null);
const retryCount = ref(0);
const retrying = ref(false);
const showDetails = ref(false);

// 计算属性
const isDevelopment = computed(() => process.env.NODE_ENV === 'development');

const errorTitle = computed(() => {
  if (errorInfo.value?.title) return errorInfo.value.title;
  return props.title;
});

const errorMessage = computed(() => {
  if (errorInfo.value?.message) return errorInfo.value.message;
  return props.message;
});

const showReportButton = computed(() => {
  return props.showReport && !isDevelopment.value;
});

// 错误捕获
onErrorCaptured((error, instance, info) => {
  console.error('ErrorBoundary caught error:', error, info);
  
  // 处理错误
  const processedError = handleError(error, {
    component: instance?.$options.name || 'Unknown',
    errorInfo: info,
    level: props.level,
    timestamp: new Date().toISOString()
  });
  
  // 设置错误状态
  hasError.value = true;
  errorInfo.value = {
    ...processedError.errorInfo,
    originalError: error,
    componentInfo: info
  };
  
  // 触发错误事件
  emit('error', errorInfo.value);
  
  // 阻止错误继续向上传播
  return false;
});

// 方法
const handleRetry = async () => {
  if (retrying.value || retryCount.value >= props.maxRetries) {
    return;
  }
  
  retrying.value = true;
  retryCount.value++;
  
  try {
    // 延迟重试
    if (props.retryDelay > 0) {
      await new Promise(resolve => setTimeout(resolve, props.retryDelay));
    }
    
    // 重置错误状态
    hasError.value = false;
    errorInfo.value = null;
    showDetails.value = false;
    
    // 等待下一个tick确保组件重新渲染
    await nextTick();
    
    emit('retry', retryCount.value);
    
  } catch (error) {
    console.error('Retry failed:', error);
    
    // 重试失败，恢复错误状态
    hasError.value = true;
    
  } finally {
    retrying.value = false;
  }
};

const handleReport = () => {
  const reportData = {
    error: errorInfo.value,
    userAgent: navigator.userAgent,
    url: window.location.href,
    timestamp: new Date().toISOString(),
    retryCount: retryCount.value
  };
  
  emit('report', reportData);
  
  // 显示反馈提示
  if (typeof uni !== 'undefined') {
    uni.showToast({
      title: '问题已反馈，感谢您的支持',
      icon: 'success',
      duration: 2000
    });
  }
};

const toggleDetails = () => {
  showDetails.value = !showDetails.value;
};

// 重置错误状态的方法
const reset = () => {
  hasError.value = false;
  errorInfo.value = null;
  retryCount.value = 0;
  retrying.value = false;
  showDetails.value = false;
};

// 手动触发错误的方法（用于测试）
const triggerError = (error) => {
  const testError = error || new Error('Manual error trigger');
  onErrorCaptured(testError, null, 'Manual trigger');
};

// 暴露方法
defineExpose({
  reset,
  triggerError,
  hasError: computed(() => hasError.value),
  errorInfo: computed(() => errorInfo.value)
});
</script><style 
lang="scss" scoped>
@import '@/styles/responsive.scss';

.error-boundary {
  width: 100%;
  height: 100%;
}

.error-fallback {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 400rpx;
  padding: 40rpx;
  text-align: center;
  
  &.full-height {
    min-height: 100vh;
  }
}

.default-error-ui {
  display: flex;
  flex-direction: column;
  align-items: center;
  max-width: 600rpx;
  width: 100%;
}

.error-icon {
  margin-bottom: 32rpx;
  opacity: 0.8;
}

.error-content {
  margin-bottom: 40rpx;
  
  .error-title {
    display: block;
    font-size: 36rpx;
    font-weight: 600;
    color: #333;
    margin-bottom: 16rpx;
    line-height: 1.4;
  }
  
  .error-message {
    display: block;
    font-size: 28rpx;
    color: #666;
    line-height: 1.5;
    margin-bottom: 24rpx;
  }
}

.error-details {
  margin-top: 24rpx;
  padding: 20rpx;
  background: #f5f5f5;
  border-radius: 8rpx;
  text-align: left;
  
  .error-stack {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
    font-size: 22rpx;
    color: #666;
    line-height: 1.4;
    word-break: break-all;
    white-space: pre-wrap;
  }
}

.error-actions {
  display: flex;
  flex-direction: column;
  gap: 16rpx;
  width: 100%;
  max-width: 400rpx;
  
  @include uni-media-up(md) {
    flex-direction: row;
    justify-content: center;
  }
}

.retry-button,
.report-button,
.details-button {
  padding: 24rpx 48rpx;
  border-radius: 12rpx;
  font-size: 28rpx;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
  
  &:active {
    transform: scale(0.98);
  }
  
  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
  }
}

.retry-button {
  background: #007aff;
  color: white;
  
  &:hover:not(:disabled) {
    background: #0056cc;
  }
  
  &:active:not(:disabled) {
    background: #004499;
  }
}

.report-button {
  background: #34c759;
  color: white;
  
  &:hover {
    background: #28a745;
  }
  
  &:active {
    background: #1e7e34;
  }
}

.details-button {
  background: #f2f2f7;
  color: #666;
  font-size: 24rpx;
  padding: 16rpx 32rpx;
  
  &:hover {
    background: #e5e5ea;
  }
  
  &:active {
    background: #d1d1d6;
  }
}

// 响应式适配
@include uni-media-up(md) {
  .error-fallback {
    padding: 60rpx;
  }
  
  .error-content {
    .error-title {
      font-size: 42rpx;
    }
    
    .error-message {
      font-size: 32rpx;
    }
  }
  
  .retry-button,
  .report-button {
    font-size: 32rpx;
    padding: 28rpx 56rpx;
  }
}

// 暗色模式支持
@media (prefers-color-scheme: dark) {
  .error-content {
    .error-title {
      color: #fff;
    }
    
    .error-message {
      color: #ccc;
    }
  }
  
  .error-details {
    background: #2c2c2e;
    
    .error-stack {
      color: #ccc;
    }
  }
  
  .details-button {
    background: #2c2c2e;
    color: #ccc;
    
    &:hover {
      background: #3a3a3c;
    }
    
    &:active {
      background: #48484a;
    }
  }
}
</style>