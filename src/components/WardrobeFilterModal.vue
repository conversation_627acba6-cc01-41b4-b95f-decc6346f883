<template>
  <view v-if="isVisible" class="filter-modal-overlay" @click.self="closeModal">
    <view class="filter-modal-content">
      <text class="modal-title">{{
        MISC_TEXT.FILTER_TITLE || '筛选衣物'
      }}</text>

      <!-- Category Filters -->
      <view class="filter-section">
        <text class="section-title">{{
          MISC_TEXT.CATEGORY_FILTER_TITLE || '分类筛选'
        }}</text>
        <!-- Main Category Picker -->
        <picker
          mode="selector"
          :range="categoryOptions"
          range-key="name"
          @change="handleCategoryChange"
        >
          <view class="picker-view">
            {{
              localFilters.category
                ? getCategoryName(localFilters.category)
                : PLACEHOLDERS.SELECT_CATEGORY || '选择主分类'
            }}
            <uni-icons
              type="bottom"
              size="16"
              color="#999"
              class="picker-icon"
            ></uni-icons>
          </view>
        </picker>
        <!-- SubCategory Picker (conditionally rendered) -->
        <picker
          v-if="localFilters.category && subCategoryOptions.length > 0"
          mode="selector"
          :range="subCategoryOptions"
          range-key="name"
          @change="handleSubCategoryChange"
        >
          <view class="picker-view">
            {{
              localFilters.subcategory
                ? getSubCategoryName(
                    localFilters.category,
                    localFilters.subcategory
                  )
                : PLACEHOLDERS.SELECT_SUBCATEGORY || '选择子分类'
            }}
            <uni-icons
              type="bottom"
              size="16"
              color="#999"
              class="picker-icon"
            ></uni-icons>
          </view>
        </picker>
        <!-- Type Picker (conditionally rendered) -->
        <picker
          v-if="localFilters.subcategory && typeOptions.length > 0"
          mode="selector"
          :range="typeOptions"
          range-key="name"
          @change="handleTypeChange"
        >
          <view class="picker-view">
            {{
              localFilters.type || PLACEHOLDERS.SELECT_TYPE || '选择具体类型'
            }}
            <uni-icons
              type="bottom"
              size="16"
              color="#999"
              class="picker-icon"
            ></uni-icons>
          </view>
        </picker>
      </view>

      <!-- Season Filter -->
      <view class="filter-section">
        <text class="section-title">{{
          MISC_TEXT.SEASON_FILTER_TITLE || '季节筛选'
        }}</text>
        <view class="checkbox-group">
          <label
            v-for="season in seasonOptions"
            :key="season.value"
            class="checkbox-label"
          >
            <checkbox
              :value="season.value"
              :checked="localFilters.seasons.includes(season.value)"
              @click="toggleSeason(season.value)"
            />
            <text>{{ season.name }}</text>
          </label>
        </view>
      </view>

      <!-- Color System Filter -->
      <view class="filter-section">
        <text class="section-title">{{
          MISC_TEXT.COLOR_SYSTEM_FILTER_TITLE || '色系筛选'
        }}</text>
        <picker
          mode="selector"
          :range="colorSystemOptions"
          range-key="name"
          @change="handleColorSystemChange"
        >
          <view class="picker-view">
            {{
              localFilters.colorSystem
                ? getColorSystemName(localFilters.colorSystem)
                : PLACEHOLDERS.SELECT_COLOR_SYSTEM || '选择色系'
            }}
            <uni-icons
              type="bottom"
              size="16"
              color="#999"
              class="picker-icon"
            ></uni-icons>
          </view>
        </picker>
      </view>

      <view class="modal-actions">
        <button class="action-button reset-button" @click="resetFilters">
          {{ BUTTON_TEXTS.RESET_FILTERS || '重置' }}
        </button>
        <button class="action-button apply-button" @click="applyFilters">
          {{ BUTTON_TEXTS.APPLY_FILTERS || '应用' }}
        </button>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, watch, computed, onMounted } from 'vue';
import { useWardrobeStore } from '@/stores/wardrobeStore.js';
import uniIcons from '@dcloudio/uni-ui/lib/uni-icons/uni-icons.vue';
// Assuming constants are set up for MISC_TEXT, PLACEHOLDERS, BUTTON_TEXTS
import { MISC_TEXT, PLACEHOLDERS, BUTTON_TEXTS } from '@/constants/strings.js';

const props = defineProps({
  isVisible: Boolean,
  initialFilters: Object // To initialize localFilters when modal opens
});

const emit = defineEmits(['close', 'apply']);

const wardrobeStore = useWardrobeStore();

const localFilters = ref({
  category: null,
  subcategory: null,
  type: null,
  seasons: [],
  colorSystem: null
});

// Populate options
const categoryOptions = computed(() => {
  return Object.entries(wardrobeStore.categorySystem).map(([key, value]) => ({
    value: key,
    name: value.name
  }));
});
const subCategoryOptions = ref([]);
const typeOptions = ref([]);

const seasonOptions = ref([
  { value: 'spring', name: '春季' },
  { value: 'summer', name: '夏季' },
  { value: 'autumn', name: '秋季' },
  { value: 'winter', name: '冬季' }
]);

const colorSystemOptions = computed(() => {
  return Object.entries(wardrobeStore.colorSystems).map(([key, value]) => ({
    value: key,
    name: value.name
  }));
});

// Watchers to update dependent pickers and localFilters when props.initialFilters changes
watch(
  () => props.isVisible,
  newVal => {
    if (newVal && props.initialFilters) {
      localFilters.value = JSON.parse(JSON.stringify(props.initialFilters)); // Deep copy
      updateDependentSubCategoryOptions();
      updateDependentTypeOptions();
    }
  }
);

const updateDependentSubCategoryOptions = () => {
  if (
    localFilters.value.category &&
    wardrobeStore.categorySystem[localFilters.value.category]
  ) {
    subCategoryOptions.value = Object.entries(
      wardrobeStore.categorySystem[localFilters.value.category].subcategories
    ).map(([key, value]) => ({ value: key, name: value.name }));
  } else {
    subCategoryOptions.value = [];
  }
};

const updateDependentTypeOptions = () => {
  if (
    localFilters.value.category &&
    localFilters.value.subcategory &&
    wardrobeStore.categorySystem[localFilters.value.category]?.subcategories[
      localFilters.value.subcategory
    ]
  ) {
    typeOptions.value = wardrobeStore.categorySystem[
      localFilters.value.category
    ].subcategories[localFilters.value.subcategory].types.map(type => ({
      value: type,
      name: type
    }));
  } else {
    typeOptions.value = [];
  }
};

// Picker change handlers
const handleCategoryChange = event => {
  const selectedIndex = event.detail.value;
  localFilters.value.category = categoryOptions.value[selectedIndex].value;
  localFilters.value.subcategory = null; // Reset subcategory and type
  localFilters.value.type = null;
  updateDependentSubCategoryOptions();
  updateDependentTypeOptions();
};

const handleSubCategoryChange = event => {
  const selectedIndex = event.detail.value;
  localFilters.value.subcategory =
    subCategoryOptions.value[selectedIndex].value;
  localFilters.value.type = null; // Reset type
  updateDependentTypeOptions();
};

const handleTypeChange = event => {
  const selectedIndex = event.detail.value;
  localFilters.value.type = typeOptions.value[selectedIndex].value;
};

const toggleSeason = seasonValue => {
  const index = localFilters.value.seasons.indexOf(seasonValue);
  if (index === -1) {
    localFilters.value.seasons.push(seasonValue);
  } else {
    localFilters.value.seasons.splice(index, 1);
  }
};

const handleColorSystemChange = event => {
  const selectedIndex = event.detail.value;
  localFilters.value.colorSystem =
    colorSystemOptions.value[selectedIndex].value;
};

// Methods to get names for display in pickers
const getCategoryName = key =>
  categoryOptions.value.find(opt => opt.value === key)?.name || '';
const getSubCategoryName = (catKey, subCatKey) => {
  if (!catKey || !subCatKey) return '';
  // Need to re-populate subCategoryOptions if not already matching current catKey for direct name lookup
  // Or, access store directly:
  return (
    wardrobeStore.categorySystem[catKey]?.subcategories[subCatKey]?.name || ''
  );
};
const getColorSystemName = key =>
  colorSystemOptions.value.find(opt => opt.value === key)?.name || '';

const closeModal = () => {
  emit('close');
};

const applyFilters = () => {
  const filters = JSON.parse(JSON.stringify(localFilters.value)); // 创建深拷贝
  emit('apply', filters);
};

const resetFilters = () => {
  localFilters.value = {
    category: null,
    subcategory: null,
    type: null,
    seasons: [],
    colorSystem: null
  };
  subCategoryOptions.value = []; // Reset dependent options
  typeOptions.value = [];
  // Optionally emit apply with reset filters immediately or let user click apply
  // emit('apply', JSON.parse(JSON.stringify(localFilters.value)));
};

onMounted(() => {
  if (props.isVisible && props.initialFilters) {
    localFilters.value = JSON.parse(JSON.stringify(props.initialFilters));
    updateDependentSubCategoryOptions();
    updateDependentTypeOptions();
  } else {
    // Ensure options are populated if no initial category
    updateDependentSubCategoryOptions(); // Handles case where category might be null
    updateDependentTypeOptions();
  }
});
</script>

<style scoped lang="scss">
.filter-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 999; // Adjusted z-index
  pointer-events: none; // Allow clicks to pass through
}

.filter-modal-content {
  background-color: white;
  padding: 30rpx;
  border-radius: 16rpx;
  width: 85%;
  // Removed max-height: 80vh;
  // Removed overflow-y: auto;
  display: flex;
  flex-direction: column;
  box-shadow: 0 5rpx 15rpx rgba(0, 0, 0, 0.1);
  z-index: 1000; // Adjusted z-index, higher than overlay
  pointer-events: auto; // Ensure content receives clicks
}

.modal-title {
  font-size: 34rpx;
  font-weight: 600;
  text-align: center;
  margin-bottom: 30rpx;
  color: #333;
}

.filter-section {
  margin-bottom: 25rpx;
  .section-title {
    font-size: 28rpx;
    font-weight: 500;
    color: #555;
    margin-bottom: 15rpx;
    display: block;
  }
}

.picker-view {
  background-color: #f7f7f7;
  padding: 18rpx 25rpx;
  border-radius: 10rpx;
  font-size: 28rpx;
  color: #333;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx; // Space between dependent pickers
  // Removed z-index: 10001;
  // Removed pointer-events: auto;
}

.picker-icon {
  // margin-left: auto; // Pushes icon to the right if text is short
}

.checkbox-group {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.checkbox-label {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
  // checkbox { transform: scale(0.8); } // Adjust checkbox size if needed
  text {
    margin-left: 8rpx;
  }
}

.modal-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;

  .action-button {
    flex: 1;
    padding: 15rpx;
    font-size: 30rpx;
    border-radius: 10rpx;
    text-align: center;
    &:active {
      opacity: 0.8;
    }
  }

  .reset-button {
    background-color: #e0e0e0;
    color: #333;
    margin-right: 20rpx;
  }

  .apply-button {
    background-color: #fda085; // Theme color
    color: white;
  }
}
</style>
