import { mount } from '@vue/test-utils'
import { describe, it, expect, vi } from 'vitest'
import SelectedItemsPreview from '../SelectedItemsPreview.vue'

// Mock uni-app components
vi.mock('@dcloudio/uni-app', () => ({
  onLoad: vi.fn(),
  onShow: vi.fn(),
  onHide: vi.fn()
}))

// Mock global uni object
global.uni = {
  showModal: vi.fn((options) => {
    if (options.success) {
      options.success({ confirm: true })
    }
  })
}

describe('SelectedItemsPreview', () => {
  const mockItems = [
    {
      id: '1',
      name: '白色T恤',
      category: 'tops',
      categoryDisplay: '上装',
      imageUrls: ['https://example.com/image1.jpg'],
      description: '舒适的白色T恤'
    },
    {
      id: '2',
      name: '牛仔裤',
      category: 'bottoms',
      categoryDisplay: '下装',
      imageUrls: ['https://example.com/image2.jpg']
    }
  ]

  it('renders empty state when no items selected', () => {
    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: []
      }
    })

    expect(wrapper.find('.empty-state').exists()).toBe(true)
    expect(wrapper.find('.empty-text').text()).toBe('还没有选择衣物')
  })

  it('renders selected items correctly', () => {
    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: mockItems
      }
    })

    expect(wrapper.find('.preview-header').exists()).toBe(true)
    expect(wrapper.find('.preview-title').text()).toBe('已选择的衣物 (2)')
    expect(wrapper.findAll('.preview-item')).toHaveLength(2)
  })

  it('displays item information correctly', () => {
    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: [mockItems[0]]
      }
    })

    const item = wrapper.find('.preview-item')
    expect(item.find('.item-name').text()).toBe('白色T恤')
    expect(item.find('.item-category').text()).toBe('上装')
  })

  it('emits remove-item event when remove button clicked', async () => {
    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: mockItems
      }
    })

    await wrapper.find('.remove-button').trigger('click')
    expect(wrapper.emitted('remove-item')).toBeTruthy()
    expect(wrapper.emitted('remove-item')[0][0]).toEqual(mockItems[0])
  })

  it('emits clear-all event when clear all button clicked', async () => {
    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: mockItems
      }
    })

    await wrapper.find('.clear-all').trigger('click')
    expect(wrapper.emitted('clear-all')).toBeTruthy()
  })

  it('emits item-click event when item is clicked', async () => {
    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: mockItems
      }
    })

    await wrapper.find('.preview-item').trigger('click')
    expect(wrapper.emitted('item-click')).toBeTruthy()
    expect(wrapper.emitted('item-click')[0][0]).toEqual(mockItems[0])
  })

  it('hides remove buttons and clear all in readonly mode', () => {
    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: mockItems,
        readonly: true
      }
    })

    expect(wrapper.find('.clear-all').exists()).toBe(false)
    expect(wrapper.find('.remove-button').exists()).toBe(false)
  })

  it('handles image error correctly', async () => {
    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: [mockItems[0]]
      }
    })

    const image = wrapper.find('.item-image')
    await image.trigger('error')
    expect(image.element.src).toBe('/static/images/empty-collection.png')
  })

  it('uses fallback image when no imageUrls provided', () => {
    const itemWithoutImage = {
      id: '3',
      name: '测试衣物',
      category: 'tops'
    }

    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: [itemWithoutImage]
      }
    })

    const image = wrapper.find('.item-image')
    expect(image.attributes('src')).toBe('/static/images/empty-collection.png')
  })

  it('displays fallback category when no categoryDisplay provided', () => {
    const itemWithoutCategoryDisplay = {
      id: '4',
      name: '测试衣物',
      category: 'tops',
      imageUrls: ['https://example.com/image.jpg']
    }

    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: [itemWithoutCategoryDisplay]
      }
    })

    expect(wrapper.find('.item-category').text()).toBe('tops')
  })

  it('displays "未分类" when no category information available', () => {
    const itemWithoutCategory = {
      id: '5',
      name: '测试衣物',
      imageUrls: ['https://example.com/image.jpg']
    }

    const wrapper = mount(SelectedItemsPreview, {
      props: {
        selectedItems: [itemWithoutCategory]
      }
    })

    expect(wrapper.find('.item-category').text()).toBe('未分类')
  })
})