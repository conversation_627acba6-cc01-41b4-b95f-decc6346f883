<template>
  <view class="selected-items-preview">
    <view class="preview-header" v-if="selectedItems.length > 0">
      <text class="preview-title">已选择的衣物 ({{ selectedItems.length }})</text>
      <text class="clear-all" @click="handleClearAll" v-if="!readonly">清空</text>
    </view>
    
    <scroll-view 
      class="preview-scroll" 
      scroll-x 
      show-scrollbar="false"
      v-if="selectedItems.length > 0"
    >
      <view class="preview-list">
        <view 
          v-for="item in selectedItems" 
          :key="item.id" 
          class="preview-item"
          @click="handleItemClick(item)"
        >
          <view class="item-image-container">
            <image 
              class="item-image" 
              :src="getItemImage(item)" 
              mode="aspectFill"
              @error="handleImageError"
            />
            <view 
              v-if="!readonly" 
              class="remove-button" 
              @click.stop="handleRemoveItem(item)"
            >
              <uni-icons type="clear" size="16" color="#fff" />
            </view>
          </view>
          <view class="item-info">
            <text class="item-name">{{ item.name }}</text>
            <text class="item-category">{{ getItemCategory(item) }}</text>
          </view>
        </view>
      </view>
    </scroll-view>
    
    <view class="empty-state" v-else>
      <image class="empty-icon" src="/static/images/empty-collection.png" mode="aspectFit" />
      <text class="empty-text">还没有选择衣物</text>
      <text class="empty-subtext">点击下方选择器添加衣物到搭配中</text>
    </view>

    <!-- 图片预览弹窗 -->
    <uni-popup ref="imagePreviewPopup" type="center">
      <view class="image-preview-modal">
        <view class="preview-modal-header">
          <text class="preview-modal-title">{{ previewItem?.name }}</text>
          <view class="preview-modal-close" @click="closeImagePreview">
            <uni-icons type="clear" size="20" color="#666" />
          </view>
        </view>
        <view class="preview-modal-content">
          <image 
            class="preview-large-image" 
            :src="getItemImage(previewItem)" 
            mode="aspectFit"
          />
          <view class="preview-item-details">
            <text class="preview-item-name">{{ previewItem?.name }}</text>
            <text class="preview-item-category">{{ getItemCategory(previewItem) }}</text>
            <text class="preview-item-description" v-if="previewItem?.description">
              {{ previewItem.description }}
            </text>
          </view>
        </view>
        <view class="preview-modal-actions" v-if="!readonly">
          <button class="remove-from-outfit-btn" @click="handleRemoveFromPreview">
            从搭配中移除
          </button>
        </view>
      </view>
    </uni-popup>
  </view>
</template>

<script setup>
import { ref, computed } from 'vue'

// Props
const props = defineProps({
  selectedItems: {
    type: Array,
    default: () => []
  },
  readonly: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['remove-item', 'clear-all', 'item-click'])

// Refs
const imagePreviewPopup = ref(null)
const previewItem = ref(null)

// Methods
const getItemImage = (item) => {
  if (item?.imageUrls && item.imageUrls.length > 0) {
    return item.imageUrls[0]
  }
  return '/static/images/empty-collection.png'
}

const getItemCategory = (item) => {
  return item?.categoryDisplay || item?.category || '未分类'
}

const handleImageError = (e) => {
  e.target.src = '/static/images/empty-collection.png'
}

const handleItemClick = (item) => {
  previewItem.value = item
  imagePreviewPopup.value?.open()
  emit('item-click', item)
}

const handleRemoveItem = (item) => {
  emit('remove-item', item)
}

const handleClearAll = () => {
  uni.showModal({
    title: '确认清空',
    content: '确定要清空所有已选择的衣物吗？',
    success: (res) => {
      if (res.confirm) {
        emit('clear-all')
      }
    }
  })
}

const closeImagePreview = () => {
  imagePreviewPopup.value?.close()
  previewItem.value = null
}

const handleRemoveFromPreview = () => {
  if (previewItem.value) {
    emit('remove-item', previewItem.value)
    closeImagePreview()
  }
}
</script>

<style lang="scss" scoped>
.selected-items-preview {
  width: 100%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;

  .preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .preview-title {
      font-size: 28rpx;
      font-weight: 500;
      color: #333;
    }

    .clear-all {
      font-size: 26rpx;
      color: #fda085;
      padding: 8rpx 16rpx;
      border-radius: 16rpx;
      background-color: rgba(253, 160, 133, 0.1);
    }
  }

  .preview-scroll {
    width: 100%;
    white-space: nowrap;

    .preview-list {
      display: inline-flex;
      padding: 20rpx;
      gap: 20rpx;

      .preview-item {
        display: inline-block;
        width: 200rpx;
        cursor: pointer;

        .item-image-container {
          position: relative;
          width: 200rpx;
          height: 200rpx;
          border-radius: 12rpx;
          overflow: hidden;
          background-color: #f5f5f5;

          .item-image {
            width: 100%;
            height: 100%;
          }

          .remove-button {
            position: absolute;
            top: 8rpx;
            right: 8rpx;
            width: 32rpx;
            height: 32rpx;
            background-color: rgba(0, 0, 0, 0.6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.3s ease;

            &:active {
              transform: scale(0.9);
            }
          }
        }

        .item-info {
          padding: 12rpx 0;
          text-align: center;

          .item-name {
            display: block;
            font-size: 26rpx;
            color: #333;
            font-weight: 500;
            line-height: 1.3;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          .item-category {
            display: block;
            font-size: 22rpx;
            color: #999;
            margin-top: 4rpx;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        &:active {
          transform: scale(0.95);
          transition: transform 0.1s ease;
        }
      }
    }
  }

  .empty-state {
    padding: 60rpx 30rpx;
    text-align: center;

    .empty-icon {
      width: 120rpx;
      height: 120rpx;
      margin-bottom: 20rpx;
      opacity: 0.6;
    }

    .empty-text {
      display: block;
      font-size: 28rpx;
      color: #666;
      margin-bottom: 12rpx;
    }

    .empty-subtext {
      display: block;
      font-size: 24rpx;
      color: #999;
      line-height: 1.4;
    }
  }
}

// 图片预览弹窗样式
.image-preview-modal {
  width: 90vw;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;

  .preview-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 24rpx 30rpx;
    border-bottom: 1px solid #f0f0f0;

    .preview-modal-title {
      font-size: 30rpx;
      font-weight: 500;
      color: #333;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      margin-right: 20rpx;
    }

    .preview-modal-close {
      width: 40rpx;
      height: 40rpx;
      display: flex;
      align-items: center;
      justify-content: center;
      border-radius: 50%;
      background-color: #f5f5f5;
    }
  }

  .preview-modal-content {
    padding: 30rpx;

    .preview-large-image {
      width: 100%;
      height: 400rpx;
      border-radius: 12rpx;
      background-color: #f5f5f5;
      margin-bottom: 24rpx;
    }

    .preview-item-details {
      .preview-item-name {
        display: block;
        font-size: 32rpx;
        font-weight: 500;
        color: #333;
        margin-bottom: 8rpx;
      }

      .preview-item-category {
        display: block;
        font-size: 26rpx;
        color: #666;
        margin-bottom: 16rpx;
      }

      .preview-item-description {
        display: block;
        font-size: 24rpx;
        color: #999;
        line-height: 1.5;
      }
    }
  }

  .preview-modal-actions {
    padding: 20rpx 30rpx 30rpx;

    .remove-from-outfit-btn {
      width: 100%;
      height: 80rpx;
      line-height: 80rpx;
      background-color: #ff4757;
      color: #fff;
      border: none;
      border-radius: 40rpx;
      font-size: 28rpx;
      font-weight: 500;

      &:active {
        background-color: #ff3742;
      }
    }
  }
}
</style>