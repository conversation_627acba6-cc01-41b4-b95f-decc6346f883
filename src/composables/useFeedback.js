import { ref, reactive } from 'vue';

// 全局反馈状态
const globalFeedbackState = reactive({
  toasts: [],
  loading: false,
  loadingMessage: '',
  modal: null
});

// Toast 实例引用
let toastInstance = null;

// 设置 Toast 实例
export const setToastInstance = (instance) => {
  toastInstance = instance;
};

// 反馈类型常量
export const FEEDBACK_TYPES = {
  SUCCESS: 'success',
  ERROR: 'error',
  WARNING: 'warning',
  INFO: 'info',
  LOADING: 'loading'
};

// 反馈动画类型
export const ANIMATION_TYPES = {
  FADE: 'fade',
  SLIDE: 'slide',
  BOUNCE: 'bounce',
  ZOOM: 'zoom'
};

// 反馈尺寸
export const FEEDBACK_SIZES = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
};

/**
 * 反馈系统 Composable
 */
export function useFeedback() {
  // Toast 相关方法
  const showToast = (options) => {
    if (!toastInstance) {
      console.warn('Toast instance not initialized');
      return;
    }
    
    return toastInstance.show(options);
  };

  const toast = {
    success: (message, options = {}) => {
      return showToast({
        type: FEEDBACK_TYPES.SUCCESS,
        message,
        ...options
      });
    },
    
    error: (message, options = {}) => {
      return showToast({
        type: FEEDBACK_TYPES.ERROR,
        message,
        duration: options.duration || 4000, // 错误消息显示更久
        ...options
      });
    },
    
    warning: (message, options = {}) => {
      return showToast({
        type: FEEDBACK_TYPES.WARNING,
        message,
        ...options
      });
    },
    
    info: (message, options = {}) => {
      return showToast({
        type: FEEDBACK_TYPES.INFO,
        message,
        ...options
      });
    },
    
    loading: (message, options = {}) => {
      return showToast({
        type: FEEDBACK_TYPES.LOADING,
        message,
        duration: 0, // 加载消息不自动消失
        closable: false,
        ...options
      });
    }
  };

  // 全局加载状态
  const showLoading = (message = '加载中...', options = {}) => {
    globalFeedbackState.loading = true;
    globalFeedbackState.loadingMessage = message;
    
    // 可选：显示 Toast 加载提示
    if (options.showToast) {
      return toast.loading(message, options);
    }
  };

  const hideLoading = () => {
    globalFeedbackState.loading = false;
    globalFeedbackState.loadingMessage = '';
  };

  // 模态反馈
  const showModal = (options) => {
    return new Promise((resolve) => {
      globalFeedbackState.modal = {
        ...options,
        onConfirm: () => {
          globalFeedbackState.modal = null;
          resolve(true);
        },
        onCancel: () => {
          globalFeedbackState.modal = null;
          resolve(false);
        }
      };
    });
  };

  const hideModal = () => {
    globalFeedbackState.modal = null;
  };

  // 确认对话框
  const confirm = (message, title = '确认', options = {}) => {
    return showModal({
      type: 'confirm',
      title,
      message,
      confirmText: '确认',
      cancelText: '取消',
      ...options
    });
  };

  // 警告对话框
  const alert = (message, title = '提示', options = {}) => {
    return showModal({
      type: 'alert',
      title,
      message,
      confirmText: '确定',
      ...options
    });
  };

  // 操作反馈
  const handleAsyncOperation = async (operation, options = {}) => {
    const {
      loadingMessage = '处理中...',
      successMessage = '操作成功',
      errorMessage = '操作失败',
      showLoadingToast = false,
      showSuccessToast = true,
      showErrorToast = true
    } = options;

    let loadingToastId = null;

    try {
      // 显示加载状态
      if (showLoadingToast) {
        loadingToastId = toast.loading(loadingMessage);
      } else {
        showLoading(loadingMessage);
      }

      // 执行操作
      const result = await operation();

      // 隐藏加载状态
      if (loadingToastId) {
        toastInstance?.removeToast(loadingToastId);
      } else {
        hideLoading();
      }

      // 显示成功提示
      if (showSuccessToast) {
        toast.success(successMessage);
      }

      return { success: true, data: result };
    } catch (error) {
      // 隐藏加载状态
      if (loadingToastId) {
        toastInstance?.removeToast(loadingToastId);
      } else {
        hideLoading();
      }

      // 显示错误提示
      if (showErrorToast) {
        const message = error.message || errorMessage;
        toast.error(message);
      }

      return { success: false, error };
    }
  };

  // 网络请求反馈
  const handleApiRequest = async (requestFn, options = {}) => {
    const {
      loadingMessage = '请求中...',
      successMessage,
      errorMessage = '请求失败',
      silent = false
    } = options;

    return handleAsyncOperation(requestFn, {
      loadingMessage,
      successMessage,
      errorMessage,
      showLoadingToast: !silent,
      showSuccessToast: !!successMessage && !silent,
      showErrorToast: !silent
    });
  };

  // 表单提交反馈
  const handleFormSubmit = async (submitFn, options = {}) => {
    const {
      loadingMessage = '提交中...',
      successMessage = '提交成功',
      errorMessage = '提交失败'
    } = options;

    return handleAsyncOperation(submitFn, {
      loadingMessage,
      successMessage,
      errorMessage,
      showLoadingToast: true,
      showSuccessToast: true,
      showErrorToast: true
    });
  };

  // 文件上传反馈
  const handleFileUpload = async (uploadFn, options = {}) => {
    const {
      loadingMessage = '上传中...',
      successMessage = '上传成功',
      errorMessage = '上传失败'
    } = options;

    return handleAsyncOperation(uploadFn, {
      loadingMessage,
      successMessage,
      errorMessage,
      showLoadingToast: true,
      showSuccessToast: true,
      showErrorToast: true
    });
  };

  // 删除操作反馈
  const handleDelete = async (deleteFn, options = {}) => {
    const {
      confirmMessage = '确定要删除吗？',
      confirmTitle = '确认删除',
      loadingMessage = '删除中...',
      successMessage = '删除成功',
      errorMessage = '删除失败'
    } = options;

    // 先确认
    const confirmed = await confirm(confirmMessage, confirmTitle);
    if (!confirmed) {
      return { success: false, cancelled: true };
    }

    // 执行删除
    return handleAsyncOperation(deleteFn, {
      loadingMessage,
      successMessage,
      errorMessage,
      showLoadingToast: true,
      showSuccessToast: true,
      showErrorToast: true
    });
  };

  // 批量操作反馈
  const handleBatchOperation = async (operations, options = {}) => {
    const {
      loadingMessage = '批量处理中...',
      successMessage = '批量操作完成',
      errorMessage = '批量操作失败',
      showProgress = true
    } = options;

    let loadingToastId = null;
    
    try {
      if (showProgress) {
        loadingToastId = toast.loading(`${loadingMessage} (0/${operations.length})`);
      } else {
        showLoading(loadingMessage);
      }

      const results = [];
      let successCount = 0;
      let errorCount = 0;

      for (let i = 0; i < operations.length; i++) {
        try {
          const result = await operations[i]();
          results.push({ success: true, data: result });
          successCount++;
        } catch (error) {
          results.push({ success: false, error });
          errorCount++;
        }

        // 更新进度
        if (showProgress && loadingToastId) {
          const progress = i + 1;
          toastInstance?.removeToast(loadingToastId);
          loadingToastId = toast.loading(`${loadingMessage} (${progress}/${operations.length})`);
        }
      }

      // 隐藏加载状态
      if (loadingToastId) {
        toastInstance?.removeToast(loadingToastId);
      } else {
        hideLoading();
      }

      // 显示结果
      if (errorCount === 0) {
        toast.success(successMessage);
      } else if (successCount === 0) {
        toast.error(errorMessage);
      } else {
        toast.warning(`操作完成：成功 ${successCount} 个，失败 ${errorCount} 个`);
      }

      return {
        success: errorCount === 0,
        results,
        successCount,
        errorCount
      };
    } catch (error) {
      if (loadingToastId) {
        toastInstance?.removeToast(loadingToastId);
      } else {
        hideLoading();
      }
      
      toast.error(errorMessage);
      return { success: false, error };
    }
  };

  // 清除所有反馈
  const clearAll = () => {
    toastInstance?.clear();
    hideLoading();
    hideModal();
  };

  return {
    // 状态
    feedbackState: globalFeedbackState,
    
    // Toast 方法
    toast,
    
    // 加载状态
    showLoading,
    hideLoading,
    
    // 模态框
    showModal,
    hideModal,
    confirm,
    alert,
    
    // 操作反馈
    handleAsyncOperation,
    handleApiRequest,
    handleFormSubmit,
    handleFileUpload,
    handleDelete,
    handleBatchOperation,
    
    // 工具方法
    clearAll,
    
    // 常量
    FEEDBACK_TYPES,
    ANIMATION_TYPES,
    FEEDBACK_SIZES
  };
}

// 全局反馈实例（用于在非组件中使用）
export const globalFeedback = useFeedback();

// 便捷的全局方法
export const toast = globalFeedback.toast;
export const showLoading = globalFeedback.showLoading;
export const hideLoading = globalFeedback.hideLoading;
export const confirm = globalFeedback.confirm;
export const alert = globalFeedback.alert;