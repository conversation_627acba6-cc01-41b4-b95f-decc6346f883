import { describe, it, expect, vi, beforeEach } from 'vitest';
import { useErrorRecovery, createRetryableApiCall, batchRetryOperations } from '../useErrorRecovery';

// Mock error handler
vi.mock('@/utils/errorHandler', () => ({
  handleError: vi.fn((error, context) => ({
    errorInfo: {
      id: 'test-error-id',
      message: error.message,
      type: 'UNKNOWN_ERROR',
      level: 'high',
      timestamp: '2023-01-01T00:00:00.000Z',
      context
    }
  }))
}));

describe('useErrorRecovery', () => {
  let recovery;

  beforeEach(() => {
    vi.clearAllMocks();
    recovery = useErrorRecovery();
  });

  describe('基本功能', () => {
    it('应该成功执行正常操作', async () => {
      const operation = vi.fn().mockResolvedValue('success');

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(true);
      expect(result.data).toBe('success');
      expect(operation).toHaveBeenCalledTimes(1);
    });

    it('应该返回初始状态', () => {
      expect(recovery.isRecovering.value).toBe(false);
      expect(recovery.retryCount.value).toBe(0);
      expect(recovery.lastError.value).toBe(null);
      expect(recovery.canRetry.value).toBe(true);
    });
  });

  describe('错误处理', () => {
    it('应该处理操作失败', async () => {
      const error = new Error('Operation failed');
      const operation = vi.fn().mockRejectedValue(error);

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(recovery.lastError.value).toBe(error);
    });

    it('应该在错误后自动重试', async () => {
      const error = new Error('Network error');
      error.name = 'NetworkError';
      
      const operation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(true);
      expect(result.data).toBe('success');
      expect(operation).toHaveBeenCalledTimes(2);
      expect(recovery.retryCount.value).toBe(0); // 成功后重置
    });

    it('应该限制最大重试次数', async () => {
      const recovery = useErrorRecovery({ maxRetries: 2 });
      const error = new Error('Persistent error');
      error.name = 'NetworkError';
      
      const operation = vi.fn().mockRejectedValue(error);

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(false);
      expect(operation).toHaveBeenCalledTimes(3); // 1 + 2 retries
      expect(recovery.canRetry.value).toBe(false);
    });

    it('应该应用指数退避延迟', async () => {
      const recovery = useErrorRecovery({ 
        retryDelay: 100, 
        exponentialBackoff: true 
      });

      expect(recovery.nextRetryDelay.value).toBe(100);
      
      // 模拟重试计数增加
      await recovery.executeWithRecovery(
        vi.fn().mockRejectedValue(new Error('Network error'))
      );
      
      // 第二次重试的延迟应该是 100 * 2^1 = 200
      expect(recovery.nextRetryDelay.value).toBe(200);
    });
  });

  describe('重试策略', () => {
    it('应该重试网络错误', async () => {
      const error = new Error('Network failed');
      error.name = 'NetworkError';
      
      const operation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(true);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('应该重试超时错误', async () => {
      const error = new Error('Timeout');
      error.name = 'TimeoutError';
      
      const operation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(true);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('应该重试5xx服务器错误', async () => {
      const error = new Error('Server error');
      error.status = 500;
      
      const operation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(true);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('应该重试429限流错误', async () => {
      const error = new Error('Too many requests');
      error.status = 429;
      
      const operation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(true);
      expect(operation).toHaveBeenCalledTimes(2);
    });

    it('不应该重试4xx客户端错误', async () => {
      const error = new Error('Bad request');
      error.status = 400;
      
      const operation = vi.fn().mockRejectedValue(error);

      const result = await recovery.executeWithRecovery(operation);

      expect(result.success).toBe(false);
      expect(operation).toHaveBeenCalledTimes(1); // 不重试
    });
  });

  describe('回调函数', () => {
    it('应该调用错误回调', async () => {
      const onError = vi.fn();
      const recovery = useErrorRecovery({ onError });
      
      const error = new Error('Test error');
      const operation = vi.fn().mockRejectedValue(error);

      await recovery.executeWithRecovery(operation);

      expect(onError).toHaveBeenCalledWith(error, 0, {});
    });

    it('应该调用重试回调', async () => {
      const onRetry = vi.fn();
      const recovery = useErrorRecovery({ onRetry, retryDelay: 0 });
      
      const error = new Error('Network error');
      error.name = 'NetworkError';
      
      const operation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');

      await recovery.executeWithRecovery(operation);

      expect(onRetry).toHaveBeenCalledWith(1, error, {});
    });

    it('应该调用恢复回调', async () => {
      const onRecovery = vi.fn();
      const recovery = useErrorRecovery({ onRecovery, retryDelay: 0 });
      
      const error = new Error('Network error');
      error.name = 'NetworkError';
      
      const operation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');

      await recovery.executeWithRecovery(operation);

      expect(onRecovery).toHaveBeenCalledWith(
        expect.objectContaining({
          type: 'success',
          retryCount: 1
        })
      );
    });
  });

  describe('状态管理', () => {
    it('应该正确重置状态', () => {
      // 设置一些状态
      recovery.retryCount.value = 2;
      recovery.lastError.value = new Error('Test');

      recovery.resetState();

      expect(recovery.retryCount.value).toBe(0);
      expect(recovery.lastError.value).toBe(null);
      expect(recovery.isRecovering.value).toBe(false);
    });

    it('应该记录恢复历史', async () => {
      const error = new Error('Network error');
      error.name = 'NetworkError';
      
      const operation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');

      await recovery.executeWithRecovery(operation);

      expect(recovery.recoveryHistory.value.length).toBeGreaterThan(0);
      expect(recovery.recoveryHistory.value[0]).toMatchObject({
        type: 'success',
        retryCount: 1
      });
    });

    it('应该清除历史记录', async () => {
      const error = new Error('Test error');
      const operation = vi.fn().mockRejectedValue(error);

      await recovery.executeWithRecovery(operation);

      expect(recovery.recoveryHistory.value.length).toBeGreaterThan(0);

      recovery.clearHistory();

      expect(recovery.recoveryHistory.value.length).toBe(0);
    });

    it('应该限制历史记录数量', async () => {
      const error = new Error('Test error');
      const operation = vi.fn().mockRejectedValue(error);

      // 生成超过50条记录
      for (let i = 0; i < 55; i++) {
        await recovery.executeWithRecovery(operation);
      }

      expect(recovery.recoveryHistory.value.length).toBe(50);
    });
  });

  describe('统计信息', () => {
    it('应该提供恢复统计信息', async () => {
      const error = new Error('Network error');
      error.name = 'NetworkError';
      
      // 成功的恢复
      const successOperation = vi.fn()
        .mockRejectedValueOnce(error)
        .mockResolvedValue('success');
      
      await recovery.executeWithRecovery(successOperation);

      // 失败的操作
      const failOperation = vi.fn().mockRejectedValue(error);
      await recovery.executeWithRecovery(failOperation);

      const stats = recovery.getRecoveryStats();

      expect(stats.totalAttempts).toBe(2);
      expect(stats.successfulRecoveries).toBe(1);
      expect(stats.failedRecoveries).toBe(1);
      expect(stats.successRate).toBe(0.5);
      expect(stats.mostCommonErrors.NetworkError).toBe(2);
    });
  });
});

describe('createRetryableApiCall', () => {
  it('应该创建可重试的API调用', async () => {
    const apiCall = vi.fn()
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValue('success');

    const retryableCall = createRetryableApiCall(apiCall, { retryDelay: 0 });

    const result = await retryableCall('arg1', 'arg2');

    expect(result.success).toBe(true);
    expect(result.data).toBe('success');
    expect(apiCall).toHaveBeenCalledWith('arg1', 'arg2');
    expect(apiCall).toHaveBeenCalledTimes(2);
  });
});

describe('batchRetryOperations', () => {
  it('应该批量处理操作', async () => {
    const operations = [
      { name: 'op1', fn: vi.fn().mockResolvedValue('result1') },
      { name: 'op2', fn: vi.fn().mockResolvedValue('result2') },
      { name: 'op3', fn: vi.fn().mockResolvedValue('result3') }
    ];

    const result = await batchRetryOperations(operations, { retryDelay: 0 });

    expect(result.successCount).toBe(3);
    expect(result.errorCount).toBe(0);
    expect(result.totalCount).toBe(3);
    expect(result.results).toHaveLength(3);
  });

  it('应该处理批量操作中的错误', async () => {
    const operations = [
      { name: 'op1', fn: vi.fn().mockResolvedValue('result1') },
      { name: 'op2', fn: vi.fn().mockRejectedValue(new Error('Error 2')) },
      { name: 'op3', fn: vi.fn().mockResolvedValue('result3') }
    ];

    const result = await batchRetryOperations(operations, { 
      retryDelay: 0,
      maxRetries: 0 // 不重试以简化测试
    });

    expect(result.successCount).toBe(2);
    expect(result.errorCount).toBe(1);
    expect(result.totalCount).toBe(3);
  });

  it('应该支持failFast模式', async () => {
    const operations = [
      { name: 'op1', fn: vi.fn().mockResolvedValue('result1') },
      { name: 'op2', fn: vi.fn().mockRejectedValue(new Error('Error 2')) },
      { name: 'op3', fn: vi.fn().mockResolvedValue('result3') }
    ];

    await expect(
      batchRetryOperations(operations, { 
        failFast: true,
        maxRetries: 0
      })
    ).rejects.toThrow();
  });

  it('应该控制并发数量', async () => {
    const operations = Array.from({ length: 10 }, (_, i) => ({
      name: `op${i}`,
      fn: vi.fn().mockResolvedValue(`result${i}`)
    }));

    const result = await batchRetryOperations(operations, { 
      concurrency: 3,
      retryDelay: 0
    });

    expect(result.successCount).toBe(10);
    expect(result.totalCount).toBe(10);
  });
});