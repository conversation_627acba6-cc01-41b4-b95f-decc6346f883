import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { useFeedback, setToastInstance, FEEDBACK_TYPES, ANIMATION_TYPES, FEEDBACK_SIZES } from '../useFeedback.js';

// Mock toast instance
const mockToastInstance = {
  show: vi.fn(),
  success: vi.fn(),
  error: vi.fn(),
  warning: vi.fn(),
  info: vi.fn(),
  loading: vi.fn(),
  removeToast: vi.fn(),
  clear: vi.fn()
};

describe('useFeedback', () => {
  let feedback;

  beforeEach(() => {
    vi.clearAllMocks();
    setToastInstance(mockToastInstance);
    feedback = useFeedback();
  });

  describe('Constants', () => {
    it('should export feedback type constants', () => {
      expect(FEEDBACK_TYPES.SUCCESS).toBe('success');
      expect(FEEDBACK_TYPES.ERROR).toBe('error');
      expect(FEEDBACK_TYPES.WARNING).toBe('warning');
      expect(FEEDBACK_TYPES.INFO).toBe('info');
      expect(FEEDBACK_TYPES.LOADING).toBe('loading');
    });

    it('should export animation type constants', () => {
      expect(ANIMATION_TYPES.FADE).toBe('fade');
      expect(ANIMATION_TYPES.SLIDE).toBe('slide');
      expect(ANIMATION_TYPES.BOUNCE).toBe('bounce');
      expect(ANIMATION_TYPES.ZOOM).toBe('zoom');
    });

    it('should export feedback size constants', () => {
      expect(FEEDBACK_SIZES.SMALL).toBe('small');
      expect(FEEDBACK_SIZES.MEDIUM).toBe('medium');
      expect(FEEDBACK_SIZES.LARGE).toBe('large');
    });
  });

  describe('Toast Methods', () => {
    it('should call toast success method', () => {
      feedback.toast.success('Success message', { duration: 2000 });
      
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'success',
        message: 'Success message',
        duration: 2000
      });
    });

    it('should call toast error method with extended duration', () => {
      feedback.toast.error('Error message');
      
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'error',
        message: 'Error message',
        duration: 4000
      });
    });

    it('should call toast warning method', () => {
      feedback.toast.warning('Warning message', { size: 'large' });
      
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'warning',
        message: 'Warning message',
        size: 'large'
      });
    });

    it('should call toast info method', () => {
      feedback.toast.info('Info message');
      
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'info',
        message: 'Info message'
      });
    });

    it('should call toast loading method with no duration', () => {
      feedback.toast.loading('Loading message');
      
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'loading',
        message: 'Loading message',
        duration: 0,
        closable: false
      });
    });

    it('should handle missing toast instance gracefully', () => {
      setToastInstance(null);
      const consoleSpy = vi.spyOn(console, 'warn').mockImplementation(() => {});
      
      const newFeedback = useFeedback();
      newFeedback.toast.success('Test message');
      
      expect(consoleSpy).toHaveBeenCalledWith('Toast instance not initialized');
      consoleSpy.mockRestore();
    });
  });

  describe('Loading State', () => {
    it('should show loading state', () => {
      feedback.showLoading('Loading data...');
      
      expect(feedback.feedbackState.loading).toBe(true);
      expect(feedback.feedbackState.loadingMessage).toBe('Loading data...');
    });

    it('should show loading with toast option', () => {
      feedback.showLoading('Loading...', { showToast: true });
      
      expect(feedback.feedbackState.loading).toBe(true);
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'loading',
        message: 'Loading...',
        duration: 0,
        closable: false,
        showToast: true
      });
    });

    it('should hide loading state', () => {
      feedback.showLoading('Loading...');
      expect(feedback.feedbackState.loading).toBe(true);
      
      feedback.hideLoading();
      expect(feedback.feedbackState.loading).toBe(false);
      expect(feedback.feedbackState.loadingMessage).toBe('');
    });
  });

  describe('Modal Methods', () => {
    it('should show confirm modal', async () => {
      const confirmPromise = feedback.confirm('Are you sure?', 'Confirm Action');
      
      expect(feedback.feedbackState.modal).toEqual({
        type: 'confirm',
        title: 'Confirm Action',
        message: 'Are you sure?',
        confirmText: '确认',
        cancelText: '取消',
        onConfirm: expect.any(Function),
        onCancel: expect.any(Function)
      });

      // Simulate confirm
      feedback.feedbackState.modal.onConfirm();
      const result = await confirmPromise;
      
      expect(result).toBe(true);
      expect(feedback.feedbackState.modal).toBe(null);
    });

    it('should show alert modal', async () => {
      const alertPromise = feedback.alert('Information message', 'Info');
      
      expect(feedback.feedbackState.modal).toEqual({
        type: 'alert',
        title: 'Info',
        message: 'Information message',
        confirmText: '确定',
        onConfirm: expect.any(Function),
        onCancel: expect.any(Function)
      });

      // Simulate confirm
      feedback.feedbackState.modal.onConfirm();
      const result = await alertPromise;
      
      expect(result).toBe(true);
      expect(feedback.feedbackState.modal).toBe(null);
    });

    it('should handle modal cancel', async () => {
      const confirmPromise = feedback.confirm('Delete item?');
      
      // Simulate cancel
      feedback.feedbackState.modal.onCancel();
      const result = await confirmPromise;
      
      expect(result).toBe(false);
      expect(feedback.feedbackState.modal).toBe(null);
    });

    it('should hide modal', () => {
      feedback.showModal({ type: 'test', message: 'Test' });
      expect(feedback.feedbackState.modal).not.toBe(null);
      
      feedback.hideModal();
      expect(feedback.feedbackState.modal).toBe(null);
    });
  });

  describe('Async Operation Handling', () => {
    it('should handle successful async operation', async () => {
      const mockOperation = vi.fn().mockResolvedValue('success data');
      
      const result = await feedback.handleAsyncOperation(mockOperation, {
        loadingMessage: 'Processing...',
        successMessage: 'Done!',
        showLoadingToast: true
      });

      expect(mockOperation).toHaveBeenCalled();
      expect(result.success).toBe(true);
      expect(result.data).toBe('success data');
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'loading',
        message: 'Processing...',
        duration: 0,
        closable: false
      });
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'success',
        message: 'Done!'
      });
    });

    it('should handle failed async operation', async () => {
      const error = new Error('Operation failed');
      const mockOperation = vi.fn().mockRejectedValue(error);
      
      const result = await feedback.handleAsyncOperation(mockOperation, {
        errorMessage: 'Failed to process'
      });

      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'error',
        message: 'Operation failed',
        duration: 4000
      });
    });

    it('should handle operation without success toast', async () => {
      const mockOperation = vi.fn().mockResolvedValue('data');
      
      await feedback.handleAsyncOperation(mockOperation, {
        showSuccessToast: false
      });

      // Should not call success toast
      expect(mockToastInstance.show).not.toHaveBeenCalledWith(
        expect.objectContaining({ type: 'success' })
      );
    });
  });

  describe('API Request Handling', () => {
    it('should handle API request with default options', async () => {
      const mockRequest = vi.fn().mockResolvedValue({ data: 'api data' });
      
      const result = await feedback.handleApiRequest(mockRequest);

      expect(result.success).toBe(true);
      expect(result.data).toEqual({ data: 'api data' });
    });

    it('should handle API request with custom messages', async () => {
      const mockRequest = vi.fn().mockResolvedValue('data');
      
      await feedback.handleApiRequest(mockRequest, {
        loadingMessage: 'Fetching...',
        successMessage: 'Data loaded',
        errorMessage: 'Failed to fetch'
      });

      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'success',
        message: 'Data loaded'
      });
    });

    it('should handle silent API request', async () => {
      const mockRequest = vi.fn().mockRejectedValue(new Error('API Error'));
      
      await feedback.handleApiRequest(mockRequest, { silent: true });

      // Should not show any toasts in silent mode
      expect(mockToastInstance.show).not.toHaveBeenCalled();
    });
  });

  describe('Form Submit Handling', () => {
    it('should handle form submission', async () => {
      const mockSubmit = vi.fn().mockResolvedValue('form data');
      
      const result = await feedback.handleFormSubmit(mockSubmit);

      expect(result.success).toBe(true);
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'loading',
        message: '提交中...',
        duration: 0,
        closable: false
      });
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'success',
        message: '提交成功'
      });
    });
  });

  describe('File Upload Handling', () => {
    it('should handle file upload', async () => {
      const mockUpload = vi.fn().mockResolvedValue('upload result');
      
      const result = await feedback.handleFileUpload(mockUpload, {
        successMessage: 'File uploaded successfully'
      });

      expect(result.success).toBe(true);
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'success',
        message: 'File uploaded successfully'
      });
    });
  });

  describe('Delete Handling', () => {
    it('should handle delete with confirmation', async () => {
      const mockDelete = vi.fn().mockResolvedValue('deleted');
      
      // Mock confirm to return true
      feedback.feedbackState.modal = null;
      const confirmSpy = vi.spyOn(feedback, 'confirm').mockResolvedValue(true);
      
      const result = await feedback.handleDelete(mockDelete);

      expect(confirmSpy).toHaveBeenCalledWith('确定要删除吗？', '确认删除');
      expect(mockDelete).toHaveBeenCalled();
      expect(result.success).toBe(true);
    });

    it('should handle delete cancellation', async () => {
      const mockDelete = vi.fn();
      
      // Mock confirm to return false
      const confirmSpy = vi.spyOn(feedback, 'confirm').mockResolvedValue(false);
      
      const result = await feedback.handleDelete(mockDelete);

      expect(confirmSpy).toHaveBeenCalled();
      expect(mockDelete).not.toHaveBeenCalled();
      expect(result.success).toBe(false);
      expect(result.cancelled).toBe(true);
    });
  });

  describe('Batch Operation Handling', () => {
    it('should handle successful batch operations', async () => {
      const operations = [
        vi.fn().mockResolvedValue('result1'),
        vi.fn().mockResolvedValue('result2'),
        vi.fn().mockResolvedValue('result3')
      ];
      
      const result = await feedback.handleBatchOperation(operations, {
        showProgress: false
      });

      expect(result.success).toBe(true);
      expect(result.successCount).toBe(3);
      expect(result.errorCount).toBe(0);
      expect(operations[0]).toHaveBeenCalled();
      expect(operations[1]).toHaveBeenCalled();
      expect(operations[2]).toHaveBeenCalled();
    });

    it('should handle mixed batch operations', async () => {
      const operations = [
        vi.fn().mockResolvedValue('result1'),
        vi.fn().mockRejectedValue(new Error('error')),
        vi.fn().mockResolvedValue('result3')
      ];
      
      const result = await feedback.handleBatchOperation(operations);

      expect(result.success).toBe(false);
      expect(result.successCount).toBe(2);
      expect(result.errorCount).toBe(1);
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'warning',
        message: '操作完成：成功 2 个，失败 1 个'
      });
    });

    it('should show progress during batch operations', async () => {
      const operations = [
        vi.fn().mockResolvedValue('result1'),
        vi.fn().mockResolvedValue('result2')
      ];
      
      await feedback.handleBatchOperation(operations, {
        loadingMessage: 'Processing items...',
        showProgress: true
      });

      // Should show progress updates
      expect(mockToastInstance.show).toHaveBeenCalledWith({
        type: 'loading',
        message: 'Processing items... (0/2)',
        duration: 0,
        closable: false
      });
    });
  });

  describe('Clear All', () => {
    it('should clear all feedback states', () => {
      feedback.showLoading('Loading...');
      feedback.showModal({ type: 'test', message: 'Test' });
      
      feedback.clearAll();
      
      expect(feedback.feedbackState.loading).toBe(false);
      expect(feedback.feedbackState.modal).toBe(null);
      expect(mockToastInstance.clear).toHaveBeenCalled();
    });
  });

  describe('Feedback State', () => {
    it('should provide reactive feedback state', () => {
      expect(feedback.feedbackState).toBeDefined();
      expect(feedback.feedbackState.loading).toBe(false);
      expect(feedback.feedbackState.loadingMessage).toBe('');
      expect(feedback.feedbackState.modal).toBe(null);
    });
  });
});

// 集成测试
describe('useFeedback Integration', () => {
  it('should work with real-world scenario', async () => {
    setToastInstance(mockToastInstance);
    const feedback = useFeedback();

    // Simulate a complete user workflow
    const mockApiCall = vi.fn()
      .mockRejectedValueOnce(new Error('Network error'))
      .mockResolvedValueOnce('success data');

    // First attempt fails
    let result = await feedback.handleApiRequest(mockApiCall, {
      loadingMessage: 'Saving...',
      successMessage: 'Saved successfully',
      errorMessage: 'Failed to save'
    });

    expect(result.success).toBe(false);
    expect(mockToastInstance.show).toHaveBeenCalledWith({
      type: 'error',
      message: 'Network error',
      duration: 4000
    });

    // Second attempt succeeds
    result = await feedback.handleApiRequest(mockApiCall, {
      loadingMessage: 'Saving...',
      successMessage: 'Saved successfully',
      errorMessage: 'Failed to save'
    });

    expect(result.success).toBe(true);
    expect(mockToastInstance.show).toHaveBeenCalledWith({
      type: 'success',
      message: 'Saved successfully'
    });
  });
});