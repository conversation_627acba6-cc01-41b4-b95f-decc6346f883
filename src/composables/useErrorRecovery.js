/**
 * 错误恢复机制 Composable
 */
import { ref, computed } from 'vue';
import { handleError } from '@/utils/errorHandler';

export function useErrorRecovery(options = {}) {
  const {
    maxRetries = 3,
    retryDelay = 1000,
    exponentialBackoff = true,
    onError = null,
    onRetry = null,
    onRecovery = null
  } = options;

  // 响应式状态
  const isRecovering = ref(false);
  const retryCount = ref(0);
  const lastError = ref(null);
  const recoveryHistory = ref([]);

  // 计算属性
  const canRetry = computed(() => {
    return retryCount.value < maxRetries && !isRecovering.value;
  });

  const nextRetryDelay = computed(() => {
    if (!exponentialBackoff) return retryDelay;
    return retryDelay * Math.pow(2, retryCount.value);
  });

  /**
   * 执行带错误恢复的操作
   * @param {Function} operation 要执行的操作
   * @param {Object} context 操作上下文
   * @returns {Promise} 操作结果
   */
  const executeWithRecovery = async (operation, context = {}) => {
    try {
      const result = await operation();
      
      // 操作成功，重置状态
      if (retryCount.value > 0) {
        recordRecovery('success', context);
        resetState();
      }
      
      return { success: true, data: result };
      
    } catch (error) {
      return await handleOperationError(error, operation, context);
    }
  };

  /**
   * 处理操作错误
   * @param {Error} error 错误对象
   * @param {Function} operation 原始操作
   * @param {Object} context 操作上下文
   * @returns {Promise} 处理结果
   */
  const handleOperationError = async (error, operation, context) => {
    lastError.value = error;
    
    // 记录错误
    const errorResult = handleError(error, {
      ...context,
      retryCount: retryCount.value,
      operation: operation.name || 'anonymous'
    });

    // 触发错误回调
    if (onError) {
      await onError(error, retryCount.value, context);
    }

    // 检查是否可以重试
    if (canRetry.value && shouldRetry(error)) {
      return await attemptRetry(operation, context);
    }

    // 无法重试，记录失败
    recordRecovery('failed', context, error);
    
    return {
      success: false,
      error: errorResult.errorInfo,
      canRetry: false
    };
  };

  /**
   * 尝试重试操作
   * @param {Function} operation 要重试的操作
   * @param {Object} context 操作上下文
   * @returns {Promise} 重试结果
   */
  const attemptRetry = async (operation, context) => {
    isRecovering.value = true;
    retryCount.value++;

    try {
      // 触发重试回调
      if (onRetry) {
        await onRetry(retryCount.value, lastError.value, context);
      }

      // 等待重试延迟
      await delay(nextRetryDelay.value);

      // 递归重试
      return await executeWithRecovery(operation, context);

    } finally {
      isRecovering.value = false;
    }
  };

  /**
   * 判断错误是否应该重试
   * @param {Error} error 错误对象
   * @returns {boolean} 是否应该重试
   */
  const shouldRetry = (error) => {
    // 网络错误通常可以重试
    if (error.name === 'NetworkError' || error.code === 'NETWORK_ERROR') {
      return true;
    }

    // 超时错误可以重试
    if (error.name === 'TimeoutError' || error.code === 'TIMEOUT') {
      return true;
    }

    // 5xx 服务器错误可以重试
    if (error.status >= 500 && error.status < 600) {
      return true;
    }

    // 429 限流错误可以重试
    if (error.status === 429) {
      return true;
    }

    // 4xx 客户端错误通常不重试
    if (error.status >= 400 && error.status < 500) {
      return false;
    }

    // 其他错误默认可以重试
    return true;
  };

  /**
   * 记录恢复历史
   * @param {string} type 恢复类型
   * @param {Object} context 上下文
   * @param {Error} error 错误对象
   */
  const recordRecovery = (type, context, error = null) => {
    const record = {
      type,
      timestamp: new Date().toISOString(),
      retryCount: retryCount.value,
      context,
      error: error ? {
        message: error.message,
        name: error.name,
        stack: error.stack
      } : null
    };

    recoveryHistory.value.push(record);

    // 限制历史记录数量
    if (recoveryHistory.value.length > 50) {
      recoveryHistory.value.shift();
    }

    // 触发恢复回调
    if (onRecovery) {
      onRecovery(record);
    }
  };

  /**
   * 重置状态
   */
  const resetState = () => {
    isRecovering.value = false;
    retryCount.value = 0;
    lastError.value = null;
  };

  /**
   * 清除历史记录
   */
  const clearHistory = () => {
    recoveryHistory.value = [];
  };

  /**
   * 延迟函数
   * @param {number} ms 延迟毫秒数
   * @returns {Promise} 延迟Promise
   */
  const delay = (ms) => {
    return new Promise(resolve => setTimeout(resolve, ms));
  };

  /**
   * 获取恢复统计信息
   * @returns {Object} 统计信息
   */
  const getRecoveryStats = () => {
    const stats = {
      totalAttempts: recoveryHistory.value.length,
      successfulRecoveries: 0,
      failedRecoveries: 0,
      averageRetries: 0,
      mostCommonErrors: {}
    };

    let totalRetries = 0;

    recoveryHistory.value.forEach(record => {
      if (record.type === 'success') {
        stats.successfulRecoveries++;
      } else if (record.type === 'failed') {
        stats.failedRecoveries++;
      }

      totalRetries += record.retryCount;

      if (record.error) {
        const errorName = record.error.name;
        stats.mostCommonErrors[errorName] = (stats.mostCommonErrors[errorName] || 0) + 1;
      }
    });

    if (stats.totalAttempts > 0) {
      stats.averageRetries = totalRetries / stats.totalAttempts;
      stats.successRate = stats.successfulRecoveries / stats.totalAttempts;
    }

    return stats;
  };

  return {
    // 状态
    isRecovering: computed(() => isRecovering.value),
    retryCount: computed(() => retryCount.value),
    lastError: computed(() => lastError.value),
    canRetry,
    nextRetryDelay,
    recoveryHistory: computed(() => recoveryHistory.value),

    // 方法
    executeWithRecovery,
    resetState,
    clearHistory,
    getRecoveryStats
  };
}

/**
 * 创建带重试的API调用
 * @param {Function} apiCall API调用函数
 * @param {Object} options 选项
 * @returns {Function} 包装后的API调用
 */
export function createRetryableApiCall(apiCall, options = {}) {
  const recovery = useErrorRecovery(options);

  return async (...args) => {
    return await recovery.executeWithRecovery(
      () => apiCall(...args),
      { apiCall: apiCall.name, args }
    );
  };
}

/**
 * 批量重试操作
 * @param {Array} operations 操作列表
 * @param {Object} options 选项
 * @returns {Promise} 批量结果
 */
export async function batchRetryOperations(operations, options = {}) {
  const {
    concurrency = 3,
    failFast = false,
    ...recoveryOptions
  } = options;

  const results = [];
  const errors = [];

  // 分批处理
  for (let i = 0; i < operations.length; i += concurrency) {
    const batch = operations.slice(i, i + concurrency);
    
    const batchPromises = batch.map(async (operation, index) => {
      const recovery = useErrorRecovery(recoveryOptions);
      
      try {
        const result = await recovery.executeWithRecovery(operation.fn, {
          batchIndex: i + index,
          operationName: operation.name || `operation_${i + index}`
        });
        
        return { index: i + index, result };
        
      } catch (error) {
        const errorResult = { index: i + index, error };
        
        if (failFast) {
          throw errorResult;
        }
        
        return errorResult;
      }
    });

    try {
      const batchResults = await Promise.all(batchPromises);
      
      batchResults.forEach(item => {
        if (item.error) {
          errors.push(item);
        } else {
          results.push(item);
        }
      });
      
    } catch (error) {
      // failFast 模式下的错误
      throw error;
    }
  }

  return {
    results,
    errors,
    successCount: results.length,
    errorCount: errors.length,
    totalCount: operations.length
  };
}