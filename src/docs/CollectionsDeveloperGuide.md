# 收藏功能开发者文档

## 概述

本文档为开发者提供收藏功能的技术实现细节、架构设计和维护指南。

## 架构设计

### 前端架构

```
src/
├── stores/
│   └── collection.js          # 收藏状态管理
├── components/
│   ├── collections/           # 收藏相关组件
│   │   ├── MyCollectionsPage.vue
│   │   ├── CollectionCard.vue
│   │   ├── OutfitCollectionView.vue
│   │   ├── BatchOperationBar.vue
│   │   └── CollectionFilters.vue
├── api/
│   └── collection.js          # 收藏API接口
└── test/
    └── e2e/                   # 端到端测试
        ├── collections.e2e.test.js
        ├── collections-workflow.e2e.test.js
        └── collections-performance.e2e.test.js
```

### 后端架构

```
yigui-backend/
├── src/
│   ├── routes/
│   │   └── favorites.js       # 收藏路由
│   ├── models/
│   │   └── favorite.js        # 收藏数据模型
│   └── middleware/
│       └── auth.js            # 认证中间件
└── docs/
    └── api.md                 # API文档
```

## 核心组件

### 1. CollectionStore (状态管理)

**位置**: `src/stores/collection.js`

**主要功能**:
- 管理收藏状态
- 处理收藏操作
- 缓存收藏数据
- 错误处理和重试

**关键方法**:
```javascript
// 切换收藏状态
async toggleFavorite(itemType, itemId)

// 获取收藏列表
async fetchFavoriteClothings(params)
async fetchFavoriteOutfits(params)

// 批量操作
async batchToggleFavorite(action, items)

// 搜索和筛选
async searchFavorites(query, filters)
```

### 2. MyCollectionsPage (主页面)

**位置**: `src/components/collections/MyCollectionsPage.vue`

**功能特性**:
- 标签页切换 (衣物/搭配)
- 搜索和筛选
- 批量操作模式
- 虚拟滚动优化

### 3. 收藏API (接口层)

**位置**: `src/api/collection.js`

**主要接口**:
```javascript
// 基础收藏操作
toggleFavorite(itemId, itemType)
getFavoriteClothings(params)
getFavoriteOutfits(params)

// 高级功能
batchToggleFavorite(action, items)
searchFavorites(params)
getFavoriteStats()
```

## 数据库设计

### favorites 表

```sql
CREATE TABLE favorites (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  item_id TEXT NOT NULL,
  item_type TEXT NOT NULL CHECK (item_type IN ('clothing', 'outfit')),
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  UNIQUE(user_id, item_id, item_type)
);

CREATE INDEX idx_favorites_user_type ON favorites(user_id, item_type);
CREATE INDEX idx_favorites_created ON favorites(created_at);
```

### collections 表

```sql
CREATE TABLE collections (
  id TEXT PRIMARY KEY,
  user_id TEXT NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  is_public BOOLEAN DEFAULT FALSE,
  cover_image_url TEXT,
  created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
  updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

## API 接口规范

### 收藏操作

#### POST /api/favorites/toggle
切换收藏状态

**请求体**:
```json
{
  "itemType": "clothing|outfit",
  "itemId": "uuid"
}
```

**响应**:
```json
{
  "success": true,
  "data": {
    "isFavorited": true,
    "action": "added|removed"
  }
}
```

#### GET /api/favorites
获取收藏列表

**查询参数**:
- `type`: clothing|outfit
- `page`: 页码 (默认: 1)
- `pageSize`: 每页数量 (默认: 20)
- `category`: 分类筛选
- `sortBy`: 排序字段

#### POST /api/favorites/batch
批量收藏操作

**请求体**:
```json
{
  "action": "add|remove",
  "items": [
    {
      "itemType": "clothing",
      "itemId": "uuid"
    }
  ]
}
```

## 测试策略

### 单元测试

**覆盖范围**:
- Store 方法测试
- 组件逻辑测试
- API 接口测试
- 工具函数测试

**运行命令**:
```bash
npm run test
npm run test:coverage
```

### 端到端测试

**测试场景**:
- 完整收藏流程
- 批量操作流程
- 搜索筛选功能
- 性能测试

**运行命令**:
```bash
npm run test:e2e
npm run test:e2e:basic
npm run test:e2e:workflow
npm run test:e2e:performance
```

### 测试报告

测试报告自动生成在 `test-reports/` 目录:
- `test-report.html`: 详细HTML报告
- `test-results.json`: 完整测试数据
- `test-summary.json`: 测试摘要

## 性能优化

### 前端优化

1. **虚拟滚动**: 大列表使用虚拟滚动
2. **图片懒加载**: 延迟加载图片资源
3. **状态缓存**: 本地缓存收藏状态
4. **防抖搜索**: 搜索输入防抖处理

### 后端优化

1. **数据库索引**: 优化查询性能
2. **分页查询**: 避免大量数据传输
3. **缓存机制**: Redis缓存热点数据
4. **批量操作**: 减少数据库请求次数

## 错误处理

### 前端错误处理

```javascript
// 网络错误重试
async toggleFavorite(itemType, itemId) {
  try {
    const result = await collectionApi.toggleFavorite(itemId, itemType);
    return result;
  } catch (error) {
    // 乐观更新回滚
    this.rollbackOptimisticUpdate(itemType, itemId);
    // 显示错误提示
    this.showErrorMessage('收藏操作失败，请重试');
    throw error;
  }
}
```

### 后端错误处理

```javascript
// 统一错误响应格式
app.use((error, req, res, next) => {
  res.status(error.status || 500).json({
    success: false,
    message: error.message,
    code: error.code
  });
});
```

## 部署和监控

### 部署流程

1. **代码检查**: ESLint + Prettier
2. **测试验证**: 单元测试 + E2E测试
3. **构建打包**: 生产环境构建
4. **部署发布**: 自动化部署

### 监控指标

- **功能监控**: 收藏成功率、响应时间
- **性能监控**: 页面加载时间、API响应时间
- **错误监控**: 错误率、异常日志
- **用户行为**: 收藏使用频率、功能使用分布

## 维护指南

### 常见问题

1. **收藏状态不同步**
   - 检查网络连接
   - 清除本地缓存
   - 重新登录账户

2. **批量操作失败**
   - 检查选择项目数量
   - 验证用户权限
   - 查看错误日志

3. **搜索结果不准确**
   - 更新搜索索引
   - 检查筛选条件
   - 验证数据完整性

### 代码规范

1. **命名规范**: 使用语义化命名
2. **注释规范**: 关键逻辑添加注释
3. **提交规范**: 遵循 Conventional Commits
4. **代码审查**: 所有变更需要代码审查

### 版本管理

- **主分支**: main (生产环境)
- **开发分支**: develop (开发环境)
- **功能分支**: feature/* (新功能开发)
- **修复分支**: hotfix/* (紧急修复)

## 扩展开发

### 添加新的收藏类型

1. 更新数据模型
2. 扩展API接口
3. 修改前端组件
4. 添加相应测试

### 集成第三方服务

1. 社交媒体分享
2. 云存储服务
3. 推荐算法
4. 数据分析服务

---

**文档版本**: v1.0
**最后更新**: 2025-08-06
**维护者**: 开发团队