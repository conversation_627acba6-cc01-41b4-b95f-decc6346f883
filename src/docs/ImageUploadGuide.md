# 衣柜小程序图片上传使用指南

## 概述

本指南介绍如何在衣柜小程序中使用图片上传功能，包括直接使用API和使用封装好的组件两种方式。

## 方式一：直接使用上传API

### 1. 导入上传API

```javascript
import { uploadApi } from '@/api/upload.js';
```

### 2. 选择图片

```javascript
const choosePhoto = () => {
  uni.chooseImage({
    count: 5, // 最多可以选择的图片张数
    sizeType: ['compressed'], // 可以指定是原图还是压缩图，默认二者都有
    sourceType: ['album', 'camera'], // 可以指定来源是相册还是相机
    success: res => {
      // tempFilePaths是选择的图片的临时文件路径列表
      const tempFilePaths = res.tempFilePaths;
      // 可以在这里直接上传，或者保存路径后稍后上传
      // uploadImages(tempFilePaths);
    },
    fail: err => {
      if (err.errMsg !== 'chooseImage:fail cancel') {
        uni.showToast({
          title: '选择图片失败',
          icon: 'none'
        });
      }
    }
  });
};
```

### 3. 上传单张图片

```javascript
const uploadSingleImage = async tempFilePath => {
  try {
    uni.showLoading({ title: '上传中...' });

    // 第二个参数是进度回调函数
    const result = await uploadApi.uploadImage(tempFilePath, progress => {
      console.log('上传进度:', progress);
    });

    uni.hideLoading();

    if (result.success) {
      uni.showToast({
        title: '上传成功',
        icon: 'success'
      });
      return result.data.url; // 返回图片URL
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: error.message || '上传失败',
      icon: 'none'
    });
    return null;
  }
};
```

### 4. 批量上传图片

```javascript
const uploadMultipleImages = async tempFilePaths => {
  try {
    uni.showLoading({ title: '上传中...' });

    const result = await uploadApi.uploadImages(tempFilePaths, progress => {
      console.log('总体上传进度:', progress);
    });

    uni.hideLoading();

    if (result.success) {
      uni.showToast({
        title: '上传成功',
        icon: 'success'
      });
      return result.data; // 返回图片URL数组
    }
  } catch (error) {
    uni.hideLoading();
    uni.showToast({
      title: error.message || '上传失败',
      icon: 'none'
    });
    return [];
  }
};
```

### 5. 预览图片

```javascript
const previewImage = (current, urls) => {
  uploadApi.previewImage(current, urls);
};
```

## 方式二：使用ImageUploader组件

### 1. 导入组件

```javascript
import ImageUploader from '@/components/common/ImageUploader.vue';

// 在组件中注册
export default {
  components: {
    ImageUploader
  }
};
```

或者在setup语法中：

```javascript
import ImageUploader from '@/components/common/ImageUploader.vue';
```

### 2. 在模板中使用

```html
<template>
  <view>
    <image-uploader
      ref="imageUploader"
      :value="photos"
      @update:value="photos = $event"
      :maxCount="5"
      tipText="添加照片，最多5张"
      @choose="onImageChoose"
      @remove="onImageRemove"
      @success="onUploadSuccess"
      @fail="onUploadFail"
    />

    <button @click="handleUpload">上传图片</button>
  </view>
</template>
```

### 3. 在脚本中使用

```javascript
import { ref } from 'vue';
import ImageUploader from '@/components/common/ImageUploader.vue';

export default {
  components: {
    ImageUploader
  },
  setup() {
    const imageUploader = ref(null);
    const photos = ref([]);

    const handleUpload = async () => {
      try {
        const result = await imageUploader.value.uploadImages();

        if (result.success) {
          console.log('上传成功，图片URL列表:', result.data);
          // 可以将图片URL保存到表单数据中
          // formData.imageUrls = result.data;
        } else {
          uni.showToast({
            title: result.message || '上传失败',
            icon: 'none'
          });
        }
      } catch (error) {
        uni.showToast({
          title: error.message || '上传失败',
          icon: 'none'
        });
      }
    };

    const onImageChoose = tempFilePaths => {
      console.log('选择了新图片:', tempFilePaths);
    };

    const onImageRemove = ({ index, removed }) => {
      console.log('移除了图片:', index, removed);
    };

    const onUploadSuccess = urls => {
      console.log('上传成功，图片URL列表:', urls);
    };

    const onUploadFail = error => {
      console.error('上传失败:', error);
    };

    return {
      imageUploader,
      photos,
      handleUpload,
      onImageChoose,
      onImageRemove,
      onUploadSuccess,
      onUploadFail
    };
  }
};
```

在Composition API中：

```javascript
<script setup>
import { ref } from 'vue';
import ImageUploader from '@/components/common/ImageUploader.vue';

const imageUploader = ref(null);
const photos = ref([]);

const handleUpload = async () => {
  try {
    const result = await imageUploader.value.uploadImages();

    if (result.success) {
      console.log('上传成功，图片URL列表:', result.data);
      // 可以将图片URL保存到表单数据中
      // formData.imageUrls = result.data;
    } else {
      uni.showToast({
        title: result.message || '上传失败',
        icon: 'none'
      });
    }
  } catch (error) {
    uni.showToast({
      title: error.message || '上传失败',
      icon: 'none'
    });
  }
};

const onImageChoose = (tempFilePaths) => {
  console.log('选择了新图片:', tempFilePaths);
};

const onImageRemove = ({ index, removed }) => {
  console.log('移除了图片:', index, removed);
};

const onUploadSuccess = (urls) => {
  console.log('上传成功，图片URL列表:', urls);
};

const onUploadFail = (error) => {
  console.error('上传失败:', error);
};
</script>
```

### 4. 组件属性和事件

#### 属性

| 属性名     | 类型    | 默认值              | 说明                 |
| ---------- | ------- | ------------------- | -------------------- |
| value      | Array   | []                  | 已选择的图片路径数组 |
| maxCount   | Number  | 5                   | 最大图片数量         |
| showTip    | Boolean | true                | 是否显示提示文字     |
| tipText    | String  | '添加照片，最多5张' | 提示文字             |
| quality    | Number  | 80                  | 图片压缩质量(0-100)  |
| sourceType | Array   | ['album', 'camera'] | 图片来源             |
| sizeType   | Array   | ['compressed']      | 图片大小类型         |

#### 事件

| 事件名       | 说明           | 参数                            |
| ------------ | -------------- | ------------------------------- |
| update:value | 图片列表更新   | 更新后的图片路径数组            |
| choose       | 选择图片后触发 | 新选择的图片路径数组            |
| remove       | 移除图片后触发 | { index, removed }              |
| upload       | 开始上传时触发 | { status: 'start', photos: [] } |
| success      | 上传成功时触发 | 上传成功的图片URL数组           |
| fail         | 上传失败时触发 | 错误对象                        |

#### 方法

| 方法名       | 说明             | 参数 | 返回值                            |
| ------------ | ---------------- | ---- | --------------------------------- |
| uploadImages | 上传图片         | 无   | Promise<{success, data, message}> |
| clear        | 清空已选图片     | 无   | 无                                |
| getPhotos    | 获取当前已选图片 | 无   | 图片路径数组                      |

## 最佳实践

1. 使用ImageUploader组件可以大大简化图片上传流程，推荐在所有需要上传图片的页面中使用。
2. 在表单提交前，先调用uploadImages方法上传图片，然后将返回的URL数组保存到表单数据中。
3. 如果需要自定义上传UI或逻辑，可以直接使用uploadApi。
4. 图片上传前会自动进行压缩，以提高上传速度和节省流量。
5. 上传过程中会显示进度条，提升用户体验。

## 注意事项

1. 确保在上传图片前已经登录并获取了有效的token。
2. 上传失败时会自动显示错误提示，无需额外处理。
3. 图片上传API支持断点续传，但需要后端支持。
4. 图片预览功能支持放大、缩小和左右滑动切换。
5. 如果需要对图片进行裁剪，可以使用uni.chooseImage的crop参数。
