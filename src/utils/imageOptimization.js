/**
 * 图片优化工具
 */

// 支持的图片格式
export const SUPPORTED_FORMATS = {
  WEBP: 'webp',
  AVIF: 'avif',
  JPEG: 'jpeg',
  PNG: 'png'
};

// 图片质量配置
export const QUALITY_PRESETS = {
  LOW: 0.6,
  MEDIUM: 0.8,
  HIGH: 0.9,
  LOSSLESS: 1.0
};

// 设备像素比阈值
export const DPR_THRESHOLDS = {
  LOW: 1,
  MEDIUM: 1.5,
  HIGH: 2,
  ULTRA: 3
};

class ImageOptimizer {
  constructor() {
    this.canvas = null;
    this.ctx = null;
    this.supportedFormats = this.detectSupportedFormats();
    this.devicePixelRatio = this.getDevicePixelRatio();
    this.compressionWorker = null;
    this.initializeWorker();
  }

  /**
   * 检测浏览器支持的图片格式
   * @returns {Object} 支持的格式对象
   */
  detectSupportedFormats() {
    const canvas = document.createElement('canvas');
    canvas.width = 1;
    canvas.height = 1;
    
    const formats = {
      webp: false,
      avif: false,
      jpeg: true,
      png: true
    };

    try {
      // 检测WebP支持
      formats.webp = canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0;
      
      // 检测AVIF支持（较新的格式）
      formats.avif = canvas.toDataURL('image/avif').indexOf('data:image/avif') === 0;
    } catch (e) {
      console.warn('Format detection failed:', e);
    }

    return formats;
  }

  /**
   * 获取设备像素比
   * @returns {number} 设备像素比
   */
  getDevicePixelRatio() {
    return window.devicePixelRatio || 1;
  }

  /**
   * 初始化压缩Worker
   */
  initializeWorker() {
    if (typeof Worker !== 'undefined') {
      try {
        // 创建内联Worker用于图片压缩
        const workerCode = `
          self.onmessage = function(e) {
            const { imageData, quality, format } = e.data;
            
            // 在Worker中处理图片压缩
            const canvas = new OffscreenCanvas(imageData.width, imageData.height);
            const ctx = canvas.getContext('2d');
            ctx.putImageData(imageData, 0, 0);
            
            canvas.convertToBlob({
              type: 'image/' + format,
              quality: quality
            }).then(blob => {
              self.postMessage({ success: true, blob });
            }).catch(error => {
              self.postMessage({ success: false, error: error.message });
            });
          };
        `;
        
        const blob = new Blob([workerCode], { type: 'application/javascript' });
        this.compressionWorker = new Worker(URL.createObjectURL(blob));
      } catch (e) {
        console.warn('Worker initialization failed:', e);
      }
    }
  }

  /**
   * 获取最佳图片格式
   * @param {string} originalFormat 原始格式
   * @returns {string} 最佳格式
   */
  getBestFormat(originalFormat = 'jpeg') {
    // 优先级：AVIF > WebP > 原格式
    if (this.supportedFormats.avif) {
      return SUPPORTED_FORMATS.AVIF;
    }
    
    if (this.supportedFormats.webp) {
      return SUPPORTED_FORMATS.WEBP;
    }
    
    return originalFormat;
  }

  /**
   * 根据设备和网络条件获取最佳质量
   * @param {Object} options 选项
   * @returns {number} 质量值
   */
  getBestQuality(options = {}) {
    const {
      networkType = 'unknown',
      deviceMemory = 4,
      saveData = false
    } = options;

    // 省流模式
    if (saveData) {
      return QUALITY_PRESETS.LOW;
    }

    // 根据网络类型调整
    const networkQualityMap = {
      'slow-2g': QUALITY_PRESETS.LOW,
      '2g': QUALITY_PRESETS.LOW,
      '3g': QUALITY_PRESETS.MEDIUM,
      '4g': QUALITY_PRESETS.HIGH,
      '5g': QUALITY_PRESETS.HIGH
    };

    if (networkQualityMap[networkType]) {
      return networkQualityMap[networkType];
    }

    // 根据设备内存调整
    if (deviceMemory < 2) {
      return QUALITY_PRESETS.LOW;
    } else if (deviceMemory < 4) {
      return QUALITY_PRESETS.MEDIUM;
    }

    return QUALITY_PRESETS.HIGH;
  }

  /**
   * 计算最佳图片尺寸
   * @param {Object} options 选项
   * @returns {Object} 尺寸对象
   */
  calculateOptimalSize(options = {}) {
    const {
      originalWidth,
      originalHeight,
      containerWidth,
      containerHeight,
      maxWidth = 1920,
      maxHeight = 1080,
      maintainAspectRatio = true
    } = options;

    let targetWidth = containerWidth * this.devicePixelRatio;
    let targetHeight = containerHeight * this.devicePixelRatio;

    // 限制最大尺寸
    targetWidth = Math.min(targetWidth, maxWidth);
    targetHeight = Math.min(targetHeight, maxHeight);

    // 保持宽高比
    if (maintainAspectRatio && originalWidth && originalHeight) {
      const aspectRatio = originalWidth / originalHeight;
      
      if (targetWidth / targetHeight > aspectRatio) {
        targetWidth = targetHeight * aspectRatio;
      } else {
        targetHeight = targetWidth / aspectRatio;
      }
    }

    return {
      width: Math.round(targetWidth),
      height: Math.round(targetHeight)
    };
  }

  /**
   * 压缩图片
   * @param {File|Blob} file 图片文件
   * @param {Object} options 压缩选项
   * @returns {Promise<Blob>} 压缩后的图片
   */
  async compressImage(file, options = {}) {
    const {
      quality = QUALITY_PRESETS.HIGH,
      format = this.getBestFormat(),
      maxWidth = 1920,
      maxHeight = 1080,
      maintainAspectRatio = true
    } = options;

    return new Promise((resolve, reject) => {
      const img = new Image();
      
      img.onload = () => {
        try {
          // 计算目标尺寸
          const { width, height } = this.calculateOptimalSize({
            originalWidth: img.width,
            originalHeight: img.height,
            containerWidth: maxWidth,
            containerHeight: maxHeight,
            maxWidth,
            maxHeight,
            maintainAspectRatio
          });

          // 创建canvas
          if (!this.canvas) {
            this.canvas = document.createElement('canvas');
            this.ctx = this.canvas.getContext('2d');
          }

          this.canvas.width = width;
          this.canvas.height = height;

          // 清除画布
          this.ctx.clearRect(0, 0, width, height);

          // 绘制图片
          this.ctx.drawImage(img, 0, 0, width, height);

          // 使用Worker压缩（如果可用）
          if (this.compressionWorker) {
            const imageData = this.ctx.getImageData(0, 0, width, height);
            
            this.compressionWorker.onmessage = (e) => {
              const { success, blob, error } = e.data;
              if (success) {
                resolve(blob);
              } else {
                reject(new Error(error));
              }
            };

            this.compressionWorker.postMessage({
              imageData,
              quality,
              format
            });
          } else {
            // 回退到主线程压缩
            this.canvas.toBlob(resolve, `image/${format}`, quality);
          }

        } catch (error) {
          reject(error);
        }
      };

      img.onerror = () => {
        reject(new Error('Failed to load image'));
      };

      // 加载图片
      if (file instanceof File || file instanceof Blob) {
        img.src = URL.createObjectURL(file);
      } else {
        img.src = file;
      }
    });
  }

  /**
   * 生成响应式图片URL
   * @param {string} baseUrl 基础URL
   * @param {Object} options 选项
   * @returns {string} 优化后的URL
   */
  generateResponsiveUrl(baseUrl, options = {}) {
    const {
      width,
      height,
      quality = this.getBestQuality(),
      format = this.getBestFormat()
    } = options;

    // 如果是CDN URL，添加参数
    if (this.isCdnUrl(baseUrl)) {
      const params = new URLSearchParams();
      
      if (width) params.set('w', width);
      if (height) params.set('h', height);
      params.set('q', Math.round(quality * 100));
      params.set('f', format);
      
      const separator = baseUrl.includes('?') ? '&' : '?';
      return `${baseUrl}${separator}${params.toString()}`;
    }

    return baseUrl;
  }

  /**
   * 检查是否为CDN URL
   * @param {string} url URL
   * @returns {boolean} 是否为CDN
   */
  isCdnUrl(url) {
    const cdnPatterns = [
      /\.cloudfront\.net/,
      /\.fastly\.com/,
      /\.cloudflare\.com/,
      /\.jsdelivr\.net/,
      /\.unpkg\.com/,
      /qiniu\.com/,
      /aliyuncs\.com/,
      /myqcloud\.com/
    ];

    return cdnPatterns.some(pattern => pattern.test(url));
  }

  /**
   * 批量优化图片
   * @param {Array} files 图片文件数组
   * @param {Object} options 选项
   * @returns {Promise<Array>} 优化结果数组
   */
  async batchOptimize(files, options = {}) {
    const {
      concurrency = 3,
      onProgress = null
    } = options;

    const results = [];
    let completed = 0;

    // 分批处理
    for (let i = 0; i < files.length; i += concurrency) {
      const batch = files.slice(i, i + concurrency);
      
      const batchPromises = batch.map(async (file, index) => {
        try {
          const optimized = await this.compressImage(file, options);
          const result = {
            original: file,
            optimized,
            originalSize: file.size,
            optimizedSize: optimized.size,
            compressionRatio: (1 - optimized.size / file.size) * 100,
            index: i + index
          };
          
          completed++;
          if (onProgress) {
            onProgress(completed, files.length, result);
          }
          
          return result;
        } catch (error) {
          completed++;
          const errorResult = {
            original: file,
            error,
            index: i + index
          };
          
          if (onProgress) {
            onProgress(completed, files.length, errorResult);
          }
          
          return errorResult;
        }
      });

      const batchResults = await Promise.all(batchPromises);
      results.push(...batchResults);
    }

    return results;
  }

  /**
   * 获取网络信息
   * @returns {Object} 网络信息
   */
  getNetworkInfo() {
    const connection = navigator.connection || 
                     navigator.mozConnection || 
                     navigator.webkitConnection;

    if (connection) {
      return {
        effectiveType: connection.effectiveType,
        downlink: connection.downlink,
        rtt: connection.rtt,
        saveData: connection.saveData
      };
    }

    return {
      effectiveType: 'unknown',
      downlink: null,
      rtt: null,
      saveData: false
    };
  }

  /**
   * 获取设备信息
   * @returns {Object} 设备信息
   */
  getDeviceInfo() {
    return {
      deviceMemory: navigator.deviceMemory || 4,
      hardwareConcurrency: navigator.hardwareConcurrency || 4,
      devicePixelRatio: this.devicePixelRatio,
      supportedFormats: this.supportedFormats
    };
  }

  /**
   * 清理资源
   */
  cleanup() {
    if (this.canvas) {
      this.canvas = null;
      this.ctx = null;
    }

    if (this.compressionWorker) {
      this.compressionWorker.terminate();
      this.compressionWorker = null;
    }
  }
}

// 创建全局实例
const imageOptimizer = new ImageOptimizer();

// 导出便捷方法
export const compressImage = (file, options) => 
  imageOptimizer.compressImage(file, options);

export const generateResponsiveUrl = (url, options) => 
  imageOptimizer.generateResponsiveUrl(url, options);

export const batchOptimize = (files, options) => 
  imageOptimizer.batchOptimize(files, options);

export const getNetworkInfo = () => 
  imageOptimizer.getNetworkInfo();

export const getDeviceInfo = () => 
  imageOptimizer.getDeviceInfo();

export default imageOptimizer;