/**
 * 分页管理工具
 */

// 分页状态枚举
export const PAGINATION_STATUS = {
  IDLE: 'idle',
  LOADING: 'loading',
  LOADED: 'loaded',
  ERROR: 'error',
  NO_MORE: 'no_more'
};

// 加载策略枚举
export const LOAD_STRATEGIES = {
  REPLACE: 'replace',    // 替换数据
  APPEND: 'append',      // 追加数据
  PREPEND: 'prepend'     // 前置数据
};

class PaginationManager {
  constructor(options = {}) {
    this.pageSize = options.pageSize || 20;
    this.initialPage = options.initialPage || 1;
    this.maxPages = options.maxPages || Infinity;
    this.loadStrategy = options.loadStrategy || LOAD_STRATEGIES.APPEND;
    this.cachePages = options.cachePages !== false;
    this.maxCacheSize = options.maxCacheSize || 10;
    
    // 状态管理
    this.currentPage = this.initialPage;
    this.totalItems = 0;
    this.totalPages = 0;
    this.status = PAGINATION_STATUS.IDLE;
    this.error = null;
    
    // 数据缓存
    this.pageCache = new Map();
    this.data = [];
    
    // 加载函数
    this.loadFunction = options.loadFunction || null;
    
    // 回调函数
    this.onLoad = options.onLoad || null;
    this.onError = options.onError || null;
    this.onComplete = options.onComplete || null;
  }

  /**
   * 设置加载函数
   * @param {Function} loadFn 加载函数
   */
  setLoadFunction(loadFn) {
    this.loadFunction = loadFn;
  }

  /**
   * 加载指定页面
   * @param {number} page 页码
   * @param {Object} options 选项
   * @returns {Promise} 加载结果
   */
  async loadPage(page = this.currentPage, options = {}) {
    const {
      force = false,
      strategy = this.loadStrategy
    } = options;

    // 检查是否已经在加载
    if (this.status === PAGINATION_STATUS.LOADING && !force) {
      return { success: false, reason: 'already_loading' };
    }

    // 检查缓存
    if (!force && this.cachePages && this.pageCache.has(page)) {
      const cachedData = this.pageCache.get(page);
      this.applyData(cachedData.items, strategy, page);
      return { success: true, data: cachedData, fromCache: true };
    }

    // 检查是否有加载函数
    if (!this.loadFunction) {
      throw new Error('Load function not set');
    }

    this.status = PAGINATION_STATUS.LOADING;
    this.error = null;

    try {
      const result = await this.loadFunction({
        page,
        pageSize: this.pageSize,
        offset: (page - 1) * this.pageSize
      });

      // 验证结果格式
      if (!this.validateResult(result)) {
        throw new Error('Invalid result format');
      }

      const { items, total, hasMore } = result;

      // 更新状态
      this.totalItems = total || this.totalItems;
      this.totalPages = Math.ceil(this.totalItems / this.pageSize);
      this.currentPage = page;

      // 缓存数据
      if (this.cachePages) {
        this.cachePageData(page, { items, total, hasMore });
      }

      // 应用数据
      this.applyData(items, strategy, page);

      // 更新状态
      this.status = hasMore === false || page >= this.totalPages 
        ? PAGINATION_STATUS.NO_MORE 
        : PAGINATION_STATUS.LOADED;

      // 触发回调
      if (this.onLoad) {
        this.onLoad(result, page);
      }

      return { success: true, data: result };

    } catch (error) {
      this.status = PAGINATION_STATUS.ERROR;
      this.error = error;

      if (this.onError) {
        this.onError(error, page);
      }

      return { success: false, error };
    }
  }

  /**
   * 加载下一页
   * @param {Object} options 选项
   * @returns {Promise} 加载结果
   */
  async loadNext(options = {}) {
    if (this.hasNext()) {
      return await this.loadPage(this.currentPage + 1, options);
    }
    return { success: false, reason: 'no_more_pages' };
  }

  /**
   * 加载上一页
   * @param {Object} options 选项
   * @returns {Promise} 加载结果
   */
  async loadPrevious(options = {}) {
    if (this.hasPrevious()) {
      return await this.loadPage(this.currentPage - 1, {
        ...options,
        strategy: LOAD_STRATEGIES.PREPEND
      });
    }
    return { success: false, reason: 'no_previous_pages' };
  }

  /**
   * 刷新当前页
   * @param {Object} options 选项
   * @returns {Promise} 加载结果
   */
  async refresh(options = {}) {
    return await this.loadPage(this.currentPage, {
      ...options,
      force: true,
      strategy: LOAD_STRATEGIES.REPLACE
    });
  }

  /**
   * 重置到第一页
   * @param {Object} options 选项
   * @returns {Promise} 加载结果
   */
  async reset(options = {}) {
    this.currentPage = this.initialPage;
    this.data = [];
    this.clearCache();
    
    return await this.loadPage(this.initialPage, {
      ...options,
      strategy: LOAD_STRATEGIES.REPLACE
    });
  }

  /**
   * 应用数据到当前数据集
   * @param {Array} items 新数据
   * @param {string} strategy 应用策略
   * @param {number} page 页码
   */
  applyData(items, strategy, page) {
    switch (strategy) {
      case LOAD_STRATEGIES.REPLACE:
        this.data = [...items];
        break;
        
      case LOAD_STRATEGIES.APPEND:
        // 避免重复数据
        const existingIds = new Set(this.data.map(item => this.getItemId(item)));
        const newItems = items.filter(item => !existingIds.has(this.getItemId(item)));
        this.data = [...this.data, ...newItems];
        break;
        
      case LOAD_STRATEGIES.PREPEND:
        // 避免重复数据
        const currentIds = new Set(this.data.map(item => this.getItemId(item)));
        const prependItems = items.filter(item => !currentIds.has(this.getItemId(item)));
        this.data = [...prependItems, ...this.data];
        break;
    }
  }

  /**
   * 获取项目ID
   * @param {Object} item 数据项
   * @returns {string|number} ID
   */
  getItemId(item) {
    if (typeof item === 'object' && item !== null) {
      return item.id || item._id || item.key || JSON.stringify(item);
    }
    return item;
  }

  /**
   * 验证加载结果格式
   * @param {Object} result 结果
   * @returns {boolean} 是否有效
   */
  validateResult(result) {
    return (
      result &&
      typeof result === 'object' &&
      Array.isArray(result.items)
    );
  }

  /**
   * 缓存页面数据
   * @param {number} page 页码
   * @param {Object} data 数据
   */
  cachePageData(page, data) {
    // 限制缓存大小
    if (this.pageCache.size >= this.maxCacheSize) {
      const firstKey = this.pageCache.keys().next().value;
      this.pageCache.delete(firstKey);
    }

    this.pageCache.set(page, {
      ...data,
      timestamp: Date.now()
    });
  }

  /**
   * 清除缓存
   * @param {number} page 页码（可选，不传则清除所有）
   */
  clearCache(page = null) {
    if (page !== null) {
      this.pageCache.delete(page);
    } else {
      this.pageCache.clear();
    }
  }

  /**
   * 清除过期缓存
   * @param {number} maxAge 最大缓存时间（毫秒）
   */
  clearExpiredCache(maxAge = 5 * 60 * 1000) { // 5分钟
    const now = Date.now();
    
    for (const [page, data] of this.pageCache.entries()) {
      if (now - data.timestamp > maxAge) {
        this.pageCache.delete(page);
      }
    }
  }

  /**
   * 预加载页面
   * @param {Array} pages 页码数组
   * @returns {Promise} 预加载结果
   */
  async preloadPages(pages) {
    const promises = pages.map(async (page) => {
      try {
        if (!this.pageCache.has(page)) {
          await this.loadPage(page, { strategy: LOAD_STRATEGIES.REPLACE });
        }
        return { page, success: true };
      } catch (error) {
        return { page, success: false, error };
      }
    });

    return await Promise.allSettled(promises);
  }

  /**
   * 检查是否有下一页
   * @returns {boolean}
   */
  hasNext() {
    return this.currentPage < this.totalPages && 
           this.currentPage < this.maxPages &&
           this.status !== PAGINATION_STATUS.NO_MORE;
  }

  /**
   * 检查是否有上一页
   * @returns {boolean}
   */
  hasPrevious() {
    return this.currentPage > this.initialPage;
  }

  /**
   * 检查是否正在加载
   * @returns {boolean}
   */
  isLoading() {
    return this.status === PAGINATION_STATUS.LOADING;
  }

  /**
   * 检查是否有错误
   * @returns {boolean}
   */
  hasError() {
    return this.status === PAGINATION_STATUS.ERROR;
  }

  /**
   * 获取当前状态信息
   * @returns {Object}
   */
  getState() {
    return {
      currentPage: this.currentPage,
      totalPages: this.totalPages,
      totalItems: this.totalItems,
      pageSize: this.pageSize,
      status: this.status,
      error: this.error,
      hasNext: this.hasNext(),
      hasPrevious: this.hasPrevious(),
      isLoading: this.isLoading(),
      hasError: this.hasError(),
      dataLength: this.data.length,
      cacheSize: this.pageCache.size
    };
  }

  /**
   * 获取统计信息
   * @returns {Object}
   */
  getStats() {
    return {
      ...this.getState(),
      cacheHitRate: this.calculateCacheHitRate(),
      averageLoadTime: this.calculateAverageLoadTime(),
      memoryUsage: this.calculateMemoryUsage()
    };
  }

  /**
   * 计算缓存命中率
   * @returns {number}
   */
  calculateCacheHitRate() {
    // 这里可以实现更复杂的统计逻辑
    return this.pageCache.size / Math.max(this.currentPage, 1);
  }

  /**
   * 计算平均加载时间
   * @returns {number}
   */
  calculateAverageLoadTime() {
    // 这里可以实现加载时间统计
    return 0;
  }

  /**
   * 计算内存使用量
   * @returns {number}
   */
  calculateMemoryUsage() {
    const dataSize = JSON.stringify(this.data).length;
    const cacheSize = JSON.stringify([...this.pageCache.values()]).length;
    return dataSize + cacheSize;
  }
}

/**
 * 创建分页管理器
 * @param {Object} options 选项
 * @returns {PaginationManager}
 */
export function createPagination(options = {}) {
  return new PaginationManager(options);
}

/**
 * 创建无限滚动分页管理器
 * @param {Object} options 选项
 * @returns {PaginationManager}
 */
export function createInfiniteScroll(options = {}) {
  return new PaginationManager({
    ...options,
    loadStrategy: LOAD_STRATEGIES.APPEND,
    cachePages: true
  });
}

/**
 * 创建双向分页管理器（支持向前和向后加载）
 * @param {Object} options 选项
 * @returns {PaginationManager}
 */
export function createBidirectionalPagination(options = {}) {
  return new PaginationManager({
    ...options,
    cachePages: true,
    maxCacheSize: 20
  });
}

export default PaginationManager;