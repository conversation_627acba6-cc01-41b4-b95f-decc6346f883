/**
 * 智能图片缓存管理系统
 */
import { getNetworkInfo, getDeviceInfo } from './imageOptimization';

// 缓存策略枚举
export const CACHE_STRATEGIES = {
  AGGRESSIVE: 'aggressive',    // 积极缓存
  BALANCED: 'balanced',       // 平衡缓存
  CONSERVATIVE: 'conservative', // 保守缓存
  MINIMAL: 'minimal'          // 最小缓存
};

// 缓存优先级
export const CACHE_PRIORITIES = {
  CRITICAL: 5,    // 关键图片（用户头像、logo等）
  HIGH: 4,        // 高优先级（首屏图片）
  MEDIUM: 3,      // 中等优先级（可见区域图片）
  LOW: 2,         // 低优先级（预加载图片）
  MINIMAL: 1      // 最低优先级（远程图片）
};

class SmartImageCache {
  constructor(options = {}) {
    this.maxMemorySize = options.maxMemorySize || 50 * 1024 * 1024; // 50MB
    this.maxCacheItems = options.maxCacheItems || 200;
    this.strategy = options.strategy || CACHE_STRATEGIES.BALANCED;
    
    // 缓存存储
    this.memoryCache = new Map();
    this.diskCache = null;
    this.cacheStats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalSize: 0
    };

    // 访问频率跟踪
    this.accessFrequency = new Map();
    this.lastAccessTime = new Map();
    
    // 网络和设备信息
    this.networkInfo = getNetworkInfo();
    this.deviceInfo = getDeviceInfo();
    
    this.initializeDiskCache();
    this.setupNetworkListener();
    this.startCleanupTimer();
  }

  /**
   * 初始化磁盘缓存
   */
  async initializeDiskCache() {
    if ('caches' in window) {
      try {
        this.diskCache = await caches.open('smart-image-cache-v1');
      } catch (error) {
        console.warn('Failed to initialize disk cache:', error);
      }
    }
  }

  /**
   * 设置网络监听
   */
  setupNetworkListener() {
    if (navigator.connection) {
      navigator.connection.addEventListener('change', () => {
        this.networkInfo = getNetworkInfo();
        this.adjustCacheStrategy();
      });
    }
  }

  /**
   * 根据网络条件调整缓存策略
   */
  adjustCacheStrategy() {
    const { effectiveType, saveData } = this.networkInfo;
    
    if (saveData || effectiveType === 'slow-2g') {
      this.strategy = CACHE_STRATEGIES.MINIMAL;
    } else if (effectiveType === '2g' || effectiveType === '3g') {
      this.strategy = CACHE_STRATEGIES.CONSERVATIVE;
    } else if (effectiveType === '4g') {
      this.strategy = CACHE_STRATEGIES.BALANCED;
    } else {
      this.strategy = CACHE_STRATEGIES.AGGRESSIVE;
    }
  }

  /**
   * 获取缓存项
   * @param {string} key 缓存键
   * @returns {Promise<any>} 缓存值
   */
  async get(key) {
    // 首先检查内存缓存
    if (this.memoryCache.has(key)) {
      const item = this.memoryCache.get(key);
      
      // 更新访问统计
      this.updateAccessStats(key);
      this.cacheStats.hits++;
      
      return item.data;
    }

    // 检查磁盘缓存
    if (this.diskCache) {
      try {
        const response = await this.diskCache.match(key);
        if (response) {
          const blob = await response.blob();
          
          // 根据策略决定是否加载到内存
          if (this.shouldLoadToMemory(key)) {
            await this.setMemoryCache(key, blob, CACHE_PRIORITIES.MEDIUM);
          }
          
          this.updateAccessStats(key);
          this.cacheStats.hits++;
          
          return blob;
        }
      } catch (error) {
        console.warn('Disk cache read error:', error);
      }
    }

    this.cacheStats.misses++;
    return null;
  }

  /**
   * 设置缓存项
   * @param {string} key 缓存键
   * @param {any} data 数据
   * @param {number} priority 优先级
   * @param {Object} metadata 元数据
   * @returns {Promise<void>}
   */
  async set(key, data, priority = CACHE_PRIORITIES.MEDIUM, metadata = {}) {
    const size = this.calculateSize(data);
    
    // 根据策略决定缓存位置
    const shouldCacheInMemory = this.shouldCacheInMemory(size, priority);
    const shouldCacheOnDisk = this.shouldCacheOnDisk(size, priority);

    if (shouldCacheInMemory) {
      await this.setMemoryCache(key, data, priority, metadata);
    }

    if (shouldCacheOnDisk && this.diskCache) {
      await this.setDiskCache(key, data, metadata);
    }

    this.updateAccessStats(key);
  }

  /**
   * 设置内存缓存
   * @param {string} key 缓存键
   * @param {any} data 数据
   * @param {number} priority 优先级
   * @param {Object} metadata 元数据
   */
  async setMemoryCache(key, data, priority, metadata = {}) {
    const size = this.calculateSize(data);
    
    // 检查是否需要清理空间
    await this.ensureMemorySpace(size);

    const cacheItem = {
      data,
      size,
      priority,
      timestamp: Date.now(),
      accessCount: 1,
      lastAccess: Date.now(),
      metadata
    };

    this.memoryCache.set(key, cacheItem);
    this.cacheStats.totalSize += size;
  }

  /**
   * 设置磁盘缓存
   * @param {string} key 缓存键
   * @param {any} data 数据
   * @param {Object} metadata 元数据
   */
  async setDiskCache(key, data, metadata = {}) {
    if (!this.diskCache) return;

    try {
      const response = new Response(data, {
        headers: {
          'Cache-Control': 'max-age=31536000', // 1年
          'X-Cache-Metadata': JSON.stringify(metadata)
        }
      });

      await this.diskCache.put(key, response);
    } catch (error) {
      console.warn('Disk cache write error:', error);
    }
  }

  /**
   * 确保内存空间充足
   * @param {number} requiredSize 需要的空间
   */
  async ensureMemorySpace(requiredSize) {
    while (
      this.cacheStats.totalSize + requiredSize > this.maxMemorySize ||
      this.memoryCache.size >= this.maxCacheItems
    ) {
      const evictedKey = this.selectEvictionCandidate();
      if (evictedKey) {
        await this.evict(evictedKey);
      } else {
        break; // 无法找到可驱逐的项
      }
    }
  }

  /**
   * 选择驱逐候选项
   * @returns {string|null} 要驱逐的键
   */
  selectEvictionCandidate() {
    if (this.memoryCache.size === 0) return null;

    let candidate = null;
    let lowestScore = Infinity;

    for (const [key, item] of this.memoryCache.entries()) {
      const score = this.calculateEvictionScore(item);
      if (score < lowestScore) {
        lowestScore = score;
        candidate = key;
      }
    }

    return candidate;
  }

  /**
   * 计算驱逐分数（分数越低越容易被驱逐）
   * @param {Object} item 缓存项
   * @returns {number} 驱逐分数
   */
  calculateEvictionScore(item) {
    const now = Date.now();
    const age = now - item.timestamp;
    const timeSinceLastAccess = now - item.lastAccess;
    
    // 基础分数 = 优先级 * 访问频率
    let score = item.priority * item.accessCount;
    
    // 时间衰减因子
    const ageDecay = Math.exp(-age / (24 * 60 * 60 * 1000)); // 24小时衰减
    const accessDecay = Math.exp(-timeSinceLastAccess / (60 * 60 * 1000)); // 1小时衰减
    
    score *= (ageDecay + accessDecay) / 2;
    
    // 大小惩罚（大文件更容易被驱逐）
    const sizePenalty = Math.log(item.size / 1024) / 10; // KB为单位的对数惩罚
    score /= (1 + sizePenalty);
    
    return score;
  }

  /**
   * 驱逐缓存项
   * @param {string} key 要驱逐的键
   */
  async evict(key) {
    const item = this.memoryCache.get(key);
    if (item) {
      this.memoryCache.delete(key);
      this.cacheStats.totalSize -= item.size;
      this.cacheStats.evictions++;
    }
  }

  /**
   * 更新访问统计
   * @param {string} key 缓存键
   */
  updateAccessStats(key) {
    const now = Date.now();
    
    // 更新访问频率
    this.accessFrequency.set(key, (this.accessFrequency.get(key) || 0) + 1);
    this.lastAccessTime.set(key, now);
    
    // 更新内存缓存项的访问统计
    if (this.memoryCache.has(key)) {
      const item = this.memoryCache.get(key);
      item.accessCount++;
      item.lastAccess = now;
    }
  }

  /**
   * 判断是否应该缓存到内存
   * @param {number} size 数据大小
   * @param {number} priority 优先级
   * @returns {boolean}
   */
  shouldCacheInMemory(size, priority) {
    const maxItemSize = this.maxMemorySize * 0.1; // 单个项目不超过总内存的10%
    
    if (size > maxItemSize) return false;
    
    switch (this.strategy) {
      case CACHE_STRATEGIES.AGGRESSIVE:
        return priority >= CACHE_PRIORITIES.LOW;
      case CACHE_STRATEGIES.BALANCED:
        return priority >= CACHE_PRIORITIES.MEDIUM;
      case CACHE_STRATEGIES.CONSERVATIVE:
        return priority >= CACHE_PRIORITIES.HIGH;
      case CACHE_STRATEGIES.MINIMAL:
        return priority >= CACHE_PRIORITIES.CRITICAL;
      default:
        return priority >= CACHE_PRIORITIES.MEDIUM;
    }
  }

  /**
   * 判断是否应该缓存到磁盘
   * @param {number} size 数据大小
   * @param {number} priority 优先级
   * @returns {boolean}
   */
  shouldCacheOnDisk(size, priority) {
    if (!this.diskCache) return false;
    
    const maxDiskItemSize = 10 * 1024 * 1024; // 10MB
    if (size > maxDiskItemSize) return false;
    
    switch (this.strategy) {
      case CACHE_STRATEGIES.AGGRESSIVE:
        return priority >= CACHE_PRIORITIES.MINIMAL;
      case CACHE_STRATEGIES.BALANCED:
        return priority >= CACHE_PRIORITIES.LOW;
      case CACHE_STRATEGIES.CONSERVATIVE:
        return priority >= CACHE_PRIORITIES.MEDIUM;
      case CACHE_STRATEGIES.MINIMAL:
        return priority >= CACHE_PRIORITIES.HIGH;
      default:
        return priority >= CACHE_PRIORITIES.LOW;
    }
  }

  /**
   * 判断是否应该加载到内存
   * @param {string} key 缓存键
   * @returns {boolean}
   */
  shouldLoadToMemory(key) {
    const frequency = this.accessFrequency.get(key) || 0;
    const lastAccess = this.lastAccessTime.get(key) || 0;
    const timeSinceLastAccess = Date.now() - lastAccess;
    
    // 频繁访问或最近访问的项目加载到内存
    return frequency > 2 || timeSinceLastAccess < 5 * 60 * 1000; // 5分钟内
  }

  /**
   * 计算数据大小
   * @param {any} data 数据
   * @returns {number} 大小（字节）
   */
  calculateSize(data) {
    if (data instanceof Blob) {
      return data.size;
    } else if (data instanceof ArrayBuffer) {
      return data.byteLength;
    } else if (typeof data === 'string') {
      return new Blob([data]).size;
    } else {
      return JSON.stringify(data).length * 2; // 估算
    }
  }

  /**
   * 预热缓存
   * @param {Array} urls URL列表
   * @param {number} priority 优先级
   */
  async warmup(urls, priority = CACHE_PRIORITIES.LOW) {
    const promises = urls.map(async (url) => {
      try {
        const response = await fetch(url);
        const blob = await response.blob();
        await this.set(url, blob, priority, { preloaded: true });
      } catch (error) {
        console.warn(`Failed to preload ${url}:`, error);
      }
    });

    await Promise.allSettled(promises);
  }

  /**
   * 清理过期缓存
   */
  cleanup() {
    const now = Date.now();
    const maxAge = 24 * 60 * 60 * 1000; // 24小时
    
    for (const [key, item] of this.memoryCache.entries()) {
      if (now - item.timestamp > maxAge && item.priority < CACHE_PRIORITIES.HIGH) {
        this.evict(key);
      }
    }
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    setInterval(() => {
      this.cleanup();
    }, 30 * 60 * 1000); // 每30分钟清理一次
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.cacheStats,
      memoryItems: this.memoryCache.size,
      hitRate: this.cacheStats.hits / (this.cacheStats.hits + this.cacheStats.misses),
      averageItemSize: this.cacheStats.totalSize / this.memoryCache.size || 0,
      strategy: this.strategy
    };
  }

  /**
   * 清空缓存
   */
  async clear() {
    this.memoryCache.clear();
    this.accessFrequency.clear();
    this.lastAccessTime.clear();
    this.cacheStats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      totalSize: 0
    };

    if (this.diskCache) {
      try {
        await this.diskCache.clear();
      } catch (error) {
        console.warn('Failed to clear disk cache:', error);
      }
    }
  }
}

// 创建全局实例
const smartImageCache = new SmartImageCache();

export default smartImageCache;