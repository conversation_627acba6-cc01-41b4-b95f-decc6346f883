/**
 * 导航工具函数
 * 提供统一的导航管理，确保正确的导航栈处理
 */

/**
 * 安全的返回导航
 * 检查导航栈深度，如果只有一个页面则跳转到主页
 */
export const safeNavigateBack = (fallbackUrl = '/pages/main/home/<USER>') => {
  const pages = getCurrentPages();

  if (pages.length > 1) {
    // 有多个页面，可以安全返回
    uni.navigateBack();
  } else {
    // 只有一个页面，跳转到指定的fallback页面
    uni.reLaunch({ url: fallbackUrl });
  }
};

/**
 * 登录成功后的导航
 * 清除登录相关的导航历史，重新开始导航栈
 */
export const navigateAfterLogin = (targetUrl = '/pages/main/home/<USER>') => {
  // 使用 reLaunch 清除所有页面栈，重新开始
  uni.reLaunch({ url: targetUrl });
};

/**
 * 登出后的导航
 * 清除所有页面栈，跳转到登录页
 */
export const navigateAfterLogout = () => {
  uni.reLaunch({ url: '/pages/auth/login/LoginPage' });
};

/**
 * 处理认证错误的导航
 * 当API返回401时使用
 */
export const handleAuthError = (message = '登录已过期，请重新登录') => {
  uni.showToast({
    title: message,
    icon: 'none',
    duration: 2000
  });

  setTimeout(() => {
    navigateAfterLogout();
  }, 2000);
};

/**
 * 智能导航到详情页
 * 根据当前页面状态决定使用何种导航方式
 */
export const navigateToDetail = (url, params = {}) => {
  const queryString = Object.keys(params)
    .map(key => `${key}=${encodeURIComponent(params[key])}`)
    .join('&');

  const fullUrl = queryString ? `${url}?${queryString}` : url;

  uni.navigateTo({ url: fullUrl });
};

/**
 * 导航到Tab页面
 * 使用switchTab确保正确的Tab导航
 */
export const navigateToTab = tabUrl => {
  uni.switchTab({ url: tabUrl });
};

/**
 * 检查当前页面是否为Tab页面
 */
export const isTabPage = pagePath => {
  const tabPages = [
    'pages/main/home/<USER>',
    'pages/wardrobe/WardrobePage',
    'pages/user/ProfilePage'
  ];

  return tabPages.some(tabPage => pagePath.includes(tabPage));
};

/**
 * 获取当前页面路径
 */
export const getCurrentPagePath = () => {
  const pages = getCurrentPages();
  const currentPage = pages[pages.length - 1];
  return currentPage.route;
};

/**
 * 检查是否需要登录
 * 根据页面路径判断是否需要认证
 */
export const requiresAuth = pagePath => {
  const publicPages = [
    'pages/auth/login/LoginPage',
    'pages/auth/register/RegisterPage',
    'pages/auth/ForgotPasswordPage'
  ];

  return !publicPages.some(publicPage => pagePath.includes(publicPage));
};

/**
 * 全局导航拦截器
 * 在页面跳转前检查认证状态和导航逻辑
 */
export const setupNavigationGuard = () => {
  // 重写 uni.navigateBack 以提供更智能的返回逻辑
  const originalNavigateBack = uni.navigateBack;

  uni.navigateBack = (options = {}) => {
    const pages = getCurrentPages();

    // 如果只有一个页面，跳转到主页而不是退出应用
    if (pages.length <= 1) {
      console.log(
        'Navigation guard: Only one page in stack, redirecting to home'
      );
      uni.reLaunch({ url: '/pages/main/home/<USER>' });
      return;
    }

    // 检查前一个页面是否为登录页，如果是则跳转到主页
    if (pages.length >= 2) {
      const previousPage = pages[pages.length - 2];
      if (previousPage.route.includes('pages/auth/login/LoginPage')) {
        console.log(
          'Navigation guard: Previous page is login, redirecting to home'
        );
        uni.reLaunch({ url: '/pages/main/home/<USER>' });
        return;
      }
    }

    // 正常返回
    originalNavigateBack.call(uni, options);
  };
};
