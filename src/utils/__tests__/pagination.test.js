import { describe, it, expect, vi, beforeEach } from 'vitest';
import PaginationManager, { 
  createPagination, 
  createInfiniteScroll, 
  createBidirectionalPagination,
  PAGINATION_STATUS,
  LOAD_STRATEGIES
} from '../pagination';

describe('PaginationManager', () => {
  let pagination;
  let mockLoadFunction;

  beforeEach(() => {
    mockLoadFunction = vi.fn();
    pagination = new PaginationManager({
      pageSize: 10,
      loadFunction: mockLoadFunction
    });
  });

  describe('初始化', () => {
    it('应该使用默认配置初始化', () => {
      const defaultPagination = new PaginationManager();
      
      expect(defaultPagination.pageSize).toBe(20);
      expect(defaultPagination.initialPage).toBe(1);
      expect(defaultPagination.currentPage).toBe(1);
      expect(defaultPagination.status).toBe(PAGINATION_STATUS.IDLE);
    });

    it('应该使用自定义配置初始化', () => {
      const customPagination = new PaginationManager({
        pageSize: 15,
        initialPage: 2,
        maxPages: 50,
        cachePages: false
      });

      expect(customPagination.pageSize).toBe(15);
      expect(customPagination.initialPage).toBe(2);
      expect(customPagination.currentPage).toBe(2);
      expect(customPagination.maxPages).toBe(50);
      expect(customPagination.cachePages).toBe(false);
    });
  });

  describe('页面加载', () => {
    it('应该成功加载页面', async () => {
      const mockData = {
        items: [{ id: 1 }, { id: 2 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      const result = await pagination.loadPage(1);

      expect(result.success).toBe(true);
      expect(result.data).toEqual(mockData);
      expect(pagination.status).toBe(PAGINATION_STATUS.LOADED);
      expect(pagination.data).toEqual(mockData.items);
      expect(pagination.totalItems).toBe(100);
      expect(pagination.currentPage).toBe(1);
    });

    it('应该处理加载错误', async () => {
      const error = new Error('Load failed');
      mockLoadFunction.mockRejectedValue(error);

      const result = await pagination.loadPage(1);

      expect(result.success).toBe(false);
      expect(result.error).toBe(error);
      expect(pagination.status).toBe(PAGINATION_STATUS.ERROR);
      expect(pagination.error).toBe(error);
    });

    it('应该验证结果格式', async () => {
      mockLoadFunction.mockResolvedValue({ invalid: 'format' });

      const result = await pagination.loadPage(1);

      expect(result.success).toBe(false);
      expect(pagination.status).toBe(PAGINATION_STATUS.ERROR);
    });

    it('应该防止重复加载', async () => {
      mockLoadFunction.mockImplementation(() => 
        new Promise(resolve => setTimeout(resolve, 100))
      );

      const promise1 = pagination.loadPage(1);
      const promise2 = pagination.loadPage(1);

      const [result1, result2] = await Promise.all([promise1, promise2]);

      expect(result1.success).toBe(true);
      expect(result2.success).toBe(false);
      expect(result2.reason).toBe('already_loading');
    });
  });

  describe('缓存机制', () => {
    beforeEach(() => {
      pagination.cachePages = true;
    });

    it('应该缓存加载的数据', async () => {
      const mockData = {
        items: [{ id: 1 }, { id: 2 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      await pagination.loadPage(1);

      expect(pagination.pageCache.has(1)).toBe(true);
      expect(pagination.pageCache.get(1).items).toEqual(mockData.items);
    });

    it('应该从缓存返回数据', async () => {
      const mockData = {
        items: [{ id: 1 }, { id: 2 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      // 第一次加载
      await pagination.loadPage(1);
      expect(mockLoadFunction).toHaveBeenCalledTimes(1);

      // 第二次应该从缓存返回
      const result = await pagination.loadPage(1);
      expect(mockLoadFunction).toHaveBeenCalledTimes(1); // 没有再次调用
      expect(result.fromCache).toBe(true);
    });

    it('应该限制缓存大小', async () => {
      pagination.maxCacheSize = 3;

      const mockData = {
        items: [{ id: 1 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      // 加载超过缓存大小的页面
      for (let i = 1; i <= 5; i++) {
        await pagination.loadPage(i);
      }

      expect(pagination.pageCache.size).toBe(3);
      expect(pagination.pageCache.has(1)).toBe(false); // 最早的应该被移除
      expect(pagination.pageCache.has(5)).toBe(true);
    });

    it('应该清除缓存', async () => {
      const mockData = {
        items: [{ id: 1 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      await pagination.loadPage(1);
      await pagination.loadPage(2);

      expect(pagination.pageCache.size).toBe(2);

      pagination.clearCache();
      expect(pagination.pageCache.size).toBe(0);
    });

    it('应该清除指定页面的缓存', async () => {
      const mockData = {
        items: [{ id: 1 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      await pagination.loadPage(1);
      await pagination.loadPage(2);

      pagination.clearCache(1);

      expect(pagination.pageCache.has(1)).toBe(false);
      expect(pagination.pageCache.has(2)).toBe(true);
    });

    it('应该清除过期缓存', async () => {
      const mockData = {
        items: [{ id: 1 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      await pagination.loadPage(1);

      // 手动设置过期时间
      const cacheData = pagination.pageCache.get(1);
      cacheData.timestamp = Date.now() - 10 * 60 * 1000; // 10分钟前

      pagination.clearExpiredCache(5 * 60 * 1000); // 5分钟过期

      expect(pagination.pageCache.has(1)).toBe(false);
    });
  });

  describe('数据应用策略', () => {
    it('应该替换数据', () => {
      pagination.data = [{ id: 1 }, { id: 2 }];
      const newItems = [{ id: 3 }, { id: 4 }];

      pagination.applyData(newItems, LOAD_STRATEGIES.REPLACE, 1);

      expect(pagination.data).toEqual(newItems);
    });

    it('应该追加数据', () => {
      pagination.data = [{ id: 1 }, { id: 2 }];
      const newItems = [{ id: 3 }, { id: 4 }];

      pagination.applyData(newItems, LOAD_STRATEGIES.APPEND, 2);

      expect(pagination.data).toEqual([
        { id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }
      ]);
    });

    it('应该前置数据', () => {
      pagination.data = [{ id: 3 }, { id: 4 }];
      const newItems = [{ id: 1 }, { id: 2 }];

      pagination.applyData(newItems, LOAD_STRATEGIES.PREPEND, 1);

      expect(pagination.data).toEqual([
        { id: 1 }, { id: 2 }, { id: 3 }, { id: 4 }
      ]);
    });

    it('应该避免重复数据', () => {
      pagination.data = [{ id: 1 }, { id: 2 }];
      const newItems = [{ id: 2 }, { id: 3 }]; // id: 2 重复

      pagination.applyData(newItems, LOAD_STRATEGIES.APPEND, 2);

      expect(pagination.data).toEqual([
        { id: 1 }, { id: 2 }, { id: 3 }
      ]);
    });
  });

  describe('导航方法', () => {
    beforeEach(async () => {
      const mockData = {
        items: [{ id: 1 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);
      await pagination.loadPage(1);
    });

    it('应该加载下一页', async () => {
      const result = await pagination.loadNext();

      expect(result.success).toBe(true);
      expect(pagination.currentPage).toBe(2);
      expect(mockLoadFunction).toHaveBeenCalledWith({
        page: 2,
        pageSize: 10,
        offset: 10
      });
    });

    it('应该在没有更多页面时拒绝加载下一页', async () => {
      pagination.totalPages = 1;
      pagination.currentPage = 1;

      const result = await pagination.loadNext();

      expect(result.success).toBe(false);
      expect(result.reason).toBe('no_more_pages');
    });

    it('应该加载上一页', async () => {
      pagination.currentPage = 2;

      const result = await pagination.loadPrevious();

      expect(result.success).toBe(true);
      expect(pagination.currentPage).toBe(1);
    });

    it('应该在第一页时拒绝加载上一页', async () => {
      const result = await pagination.loadPrevious();

      expect(result.success).toBe(false);
      expect(result.reason).toBe('no_previous_pages');
    });

    it('应该刷新当前页', async () => {
      const result = await pagination.refresh();

      expect(result.success).toBe(true);
      expect(mockLoadFunction).toHaveBeenCalledTimes(2); // 初始加载 + 刷新
    });

    it('应该重置到第一页', async () => {
      pagination.currentPage = 3;
      pagination.data = [{ id: 1 }, { id: 2 }, { id: 3 }];

      const result = await pagination.reset();

      expect(result.success).toBe(true);
      expect(pagination.currentPage).toBe(1);
      expect(pagination.data.length).toBeGreaterThan(0); // 重新加载了数据
    });
  });

  describe('状态检查', () => {
    it('应该正确检查是否有下一页', () => {
      pagination.currentPage = 1;
      pagination.totalPages = 5;
      pagination.status = PAGINATION_STATUS.LOADED;

      expect(pagination.hasNext()).toBe(true);

      pagination.currentPage = 5;
      expect(pagination.hasNext()).toBe(false);
    });

    it('应该正确检查是否有上一页', () => {
      pagination.currentPage = 1;
      expect(pagination.hasPrevious()).toBe(false);

      pagination.currentPage = 2;
      expect(pagination.hasPrevious()).toBe(true);
    });

    it('应该正确检查加载状态', () => {
      pagination.status = PAGINATION_STATUS.LOADING;
      expect(pagination.isLoading()).toBe(true);

      pagination.status = PAGINATION_STATUS.LOADED;
      expect(pagination.isLoading()).toBe(false);
    });

    it('应该正确检查错误状态', () => {
      pagination.status = PAGINATION_STATUS.ERROR;
      expect(pagination.hasError()).toBe(true);

      pagination.status = PAGINATION_STATUS.LOADED;
      expect(pagination.hasError()).toBe(false);
    });
  });

  describe('预加载', () => {
    it('应该预加载指定页面', async () => {
      const mockData = {
        items: [{ id: 1 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      const results = await pagination.preloadPages([2, 3, 4]);

      expect(results).toHaveLength(3);
      expect(mockLoadFunction).toHaveBeenCalledTimes(3);
      expect(pagination.pageCache.has(2)).toBe(true);
      expect(pagination.pageCache.has(3)).toBe(true);
      expect(pagination.pageCache.has(4)).toBe(true);
    });

    it('应该处理预加载错误', async () => {
      mockLoadFunction
        .mockResolvedValueOnce({ items: [{ id: 1 }], total: 100 })
        .mockRejectedValueOnce(new Error('Load failed'));

      const results = await pagination.preloadPages([2, 3]);

      expect(results).toHaveLength(2);
      expect(results[0].value.success).toBe(true);
      expect(results[1].value.success).toBe(false);
    });
  });

  describe('统计信息', () => {
    it('应该返回当前状态', () => {
      pagination.currentPage = 2;
      pagination.totalPages = 10;
      pagination.totalItems = 100;
      pagination.status = PAGINATION_STATUS.LOADED;

      const state = pagination.getState();

      expect(state).toMatchObject({
        currentPage: 2,
        totalPages: 10,
        totalItems: 100,
        pageSize: 10,
        status: PAGINATION_STATUS.LOADED,
        hasNext: true,
        hasPrevious: true,
        isLoading: false,
        hasError: false
      });
    });

    it('应该返回统计信息', () => {
      const stats = pagination.getStats();

      expect(stats).toHaveProperty('cacheHitRate');
      expect(stats).toHaveProperty('averageLoadTime');
      expect(stats).toHaveProperty('memoryUsage');
    });
  });

  describe('回调函数', () => {
    it('应该调用加载成功回调', async () => {
      const onLoad = vi.fn();
      pagination.onLoad = onLoad;

      const mockData = {
        items: [{ id: 1 }],
        total: 100,
        hasMore: true
      };

      mockLoadFunction.mockResolvedValue(mockData);

      await pagination.loadPage(1);

      expect(onLoad).toHaveBeenCalledWith(mockData, 1);
    });

    it('应该调用错误回调', async () => {
      const onError = vi.fn();
      pagination.onError = onError;

      const error = new Error('Load failed');
      mockLoadFunction.mockRejectedValue(error);

      await pagination.loadPage(1);

      expect(onError).toHaveBeenCalledWith(error, 1);
    });
  });
});

describe('工厂函数', () => {
  it('应该创建基本分页管理器', () => {
    const pagination = createPagination({ pageSize: 15 });

    expect(pagination).toBeInstanceOf(PaginationManager);
    expect(pagination.pageSize).toBe(15);
  });

  it('应该创建无限滚动分页管理器', () => {
    const pagination = createInfiniteScroll({ pageSize: 25 });

    expect(pagination).toBeInstanceOf(PaginationManager);
    expect(pagination.pageSize).toBe(25);
    expect(pagination.loadStrategy).toBe(LOAD_STRATEGIES.APPEND);
    expect(pagination.cachePages).toBe(true);
  });

  it('应该创建双向分页管理器', () => {
    const pagination = createBidirectionalPagination({ pageSize: 30 });

    expect(pagination).toBeInstanceOf(PaginationManager);
    expect(pagination.pageSize).toBe(30);
    expect(pagination.cachePages).toBe(true);
    expect(pagination.maxCacheSize).toBe(20);
  });
});