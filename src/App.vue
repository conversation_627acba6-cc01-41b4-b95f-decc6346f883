<script>
import { useUserStore } from '@/stores/userStore';
import { setupNavigationGuard } from '@/utils/navigation';

export default {
  onLaunch() {
    // App initialization logic here
    console.log('App Launch');

    // 设置导航守卫
    setupNavigationGuard();

    // 初始化用户状态
    const userStore = useUserStore();

    // 检查本地存储的token
    const token = uni.getStorageSync('token');
    const userInfo = uni.getStorageSync('userInfo');

    if (token && userInfo) {
      // 恢复用户状态
      userStore.token = token;
      userStore.userInfo = userInfo;

      // 验证token是否仍然有效
      userStore.fetchUserInfo().catch(() => {
        // 如果token无效，会自动调用logout清除状态
        console.log('Token validation failed during app launch');
      });
    }
  },
  onShow() {
    // App show logic here
  },
  onHide() {
    // App hide logic here
  }
};
</script>

<style>
/*每个页面公共css */
</style>
