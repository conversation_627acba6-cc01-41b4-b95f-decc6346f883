// 响应式工具类
@import './responsive.scss';

// 容器类
.container {
  @include make-container();
  @include make-container-max-widths();
}

.container-fluid {
  @include make-container();
}

// 行和列
.row {
  @include make-row();
}

.col {
  @include make-col-ready();
  @include make-col-auto();
}

// 生成响应式列类
@each $breakpoint in map-keys($breakpoints) {
  $infix: if($breakpoint == xs, "", "-#{$breakpoint}");

  @include media-breakpoint-up($breakpoint) {
    // 自动列
    .col#{$infix}-auto {
      @include make-col-auto();
    }

    // 数字列
    @for $i from 1 through $grid-columns {
      .col#{$infix}-#{$i} {
        @include make-col($i, $grid-columns);
      }
    }

    // 偏移类
    @for $i from 0 through ($grid-columns - 1) {
      @if not ($infix == "" and $i == 0) {
        .offset#{$infix}-#{$i} {
          @include make-col-offset($i, $grid-columns);
        }
      }
    }
  }
}

// 显示/隐藏工具类
.d-none { display: none !important; }
.d-inline { display: inline !important; }
.d-inline-block { display: inline-block !important; }
.d-block { display: block !important; }
.d-flex { display: flex !important; }
.d-inline-flex { display: inline-flex !important; }

// 响应式显示类
@each $breakpoint in map-keys($uni-breakpoints) {
  $infix: if($breakpoint == xs, "", "-#{$breakpoint}");

  .d#{$infix}-none {
    @include uni-media-up($breakpoint) {
      display: none !important;
    }
  }
  .d#{$infix}-inline {
    @include uni-media-up($breakpoint) {
      display: inline !important;
    }
  }
  .d#{$infix}-inline-block {
    @include uni-media-up($breakpoint) {
      display: inline-block !important;
    }
  }
  .d#{$infix}-block {
    @include uni-media-up($breakpoint) {
      display: block !important;
    }
  }
  .d#{$infix}-flex {
    @include uni-media-up($breakpoint) {
      display: flex !important;
    }
  }
  .d#{$infix}-inline-flex {
    @include uni-media-up($breakpoint) {
      display: inline-flex !important;
    }
  }
}

// Flexbox 工具类
.flex-row { flex-direction: row !important; }
.flex-column { flex-direction: column !important; }
.flex-row-reverse { flex-direction: row-reverse !important; }
.flex-column-reverse { flex-direction: column-reverse !important; }

.flex-wrap { flex-wrap: wrap !important; }
.flex-nowrap { flex-wrap: nowrap !important; }
.flex-wrap-reverse { flex-wrap: wrap-reverse !important; }

.justify-content-start { justify-content: flex-start !important; }
.justify-content-end { justify-content: flex-end !important; }
.justify-content-center { justify-content: center !important; }
.justify-content-between { justify-content: space-between !important; }
.justify-content-around { justify-content: space-around !important; }
.justify-content-evenly { justify-content: space-evenly !important; }

.align-items-start { align-items: flex-start !important; }
.align-items-end { align-items: flex-end !important; }
.align-items-center { align-items: center !important; }
.align-items-baseline { align-items: baseline !important; }
.align-items-stretch { align-items: stretch !important; }

.align-content-start { align-content: flex-start !important; }
.align-content-end { align-content: flex-end !important; }
.align-content-center { align-content: center !important; }
.align-content-between { align-content: space-between !important; }
.align-content-around { align-content: space-around !important; }
.align-content-stretch { align-content: stretch !important; }

.align-self-auto { align-self: auto !important; }
.align-self-start { align-self: flex-start !important; }
.align-self-end { align-self: flex-end !important; }
.align-self-center { align-self: center !important; }
.align-self-baseline { align-self: baseline !important; }
.align-self-stretch { align-self: stretch !important; }

// 间距工具类
$spacer: 1rem !default;
$spacers: (
  0: 0,
  1: $spacer * 0.25,
  2: $spacer * 0.5,
  3: $spacer,
  4: $spacer * 1.5,
  5: $spacer * 3,
) !default;

@each $prop, $abbrev in (margin: m, padding: p) {
  @each $size, $length in $spacers {
    .#{$abbrev}-#{$size} { #{$prop}: $length !important; }
    .#{$abbrev}t-#{$size},
    .#{$abbrev}y-#{$size} { #{$prop}-top: $length !important; }
    .#{$abbrev}r-#{$size},
    .#{$abbrev}x-#{$size} { #{$prop}-right: $length !important; }
    .#{$abbrev}b-#{$size},
    .#{$abbrev}y-#{$size} { #{$prop}-bottom: $length !important; }
    .#{$abbrev}l-#{$size},
    .#{$abbrev}x-#{$size} { #{$prop}-left: $length !important; }
  }
}

// 文本对齐
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }
.text-center { text-align: center !important; }
.text-justify { text-align: justify !important; }

// 响应式文本对齐
@each $breakpoint in map-keys($uni-breakpoints) {
  $infix: if($breakpoint == xs, "", "-#{$breakpoint}");

  .text#{$infix}-left {
    @include uni-media-up($breakpoint) {
      text-align: left !important;
    }
  }
  .text#{$infix}-right {
    @include uni-media-up($breakpoint) {
      text-align: right !important;
    }
  }
  .text#{$infix}-center {
    @include uni-media-up($breakpoint) {
      text-align: center !important;
    }
  }
  .text#{$infix}-justify {
    @include uni-media-up($breakpoint) {
      text-align: justify !important;
    }
  }
}

// 字体大小
.fs-1 { font-size: 2.5rem !important; }
.fs-2 { font-size: 2rem !important; }
.fs-3 { font-size: 1.75rem !important; }
.fs-4 { font-size: 1.5rem !important; }
.fs-5 { font-size: 1.25rem !important; }
.fs-6 { font-size: 1rem !important; }

// 字体粗细
.fw-light { font-weight: 300 !important; }
.fw-normal { font-weight: 400 !important; }
.fw-medium { font-weight: 500 !important; }
.fw-semibold { font-weight: 600 !important; }
.fw-bold { font-weight: 700 !important; }

// 宽度和高度
.w-25 { width: 25% !important; }
.w-50 { width: 50% !important; }
.w-75 { width: 75% !important; }
.w-100 { width: 100% !important; }
.w-auto { width: auto !important; }

.h-25 { height: 25% !important; }
.h-50 { height: 50% !important; }
.h-75 { height: 75% !important; }
.h-100 { height: 100% !important; }
.h-auto { height: auto !important; }

// 最大宽度和高度
.mw-100 { max-width: 100% !important; }
.mh-100 { max-height: 100% !important; }

// 最小宽度和高度
.min-vw-100 { min-width: 100vw !important; }
.min-vh-100 { min-height: 100vh !important; }

// 位置
.position-static { position: static !important; }
.position-relative { position: relative !important; }
.position-absolute { position: absolute !important; }
.position-fixed { position: fixed !important; }
.position-sticky { position: sticky !important; }

// 边框
.border { border: 1px solid #dee2e6 !important; }
.border-0 { border: 0 !important; }
.border-top { border-top: 1px solid #dee2e6 !important; }
.border-top-0 { border-top: 0 !important; }
.border-right { border-right: 1px solid #dee2e6 !important; }
.border-right-0 { border-right: 0 !important; }
.border-bottom { border-bottom: 1px solid #dee2e6 !important; }
.border-bottom-0 { border-bottom: 0 !important; }
.border-left { border-left: 1px solid #dee2e6 !important; }
.border-left-0 { border-left: 0 !important; }

// 圆角
.rounded { border-radius: 0.375rem !important; }
.rounded-0 { border-radius: 0 !important; }
.rounded-1 { border-radius: 0.25rem !important; }
.rounded-2 { border-radius: 0.375rem !important; }
.rounded-3 { border-radius: 0.5rem !important; }
.rounded-circle { border-radius: 50% !important; }
.rounded-pill { border-radius: 50rem !important; }

// 阴影
.shadow-none { box-shadow: none !important; }
.shadow-sm { box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important; }
.shadow { box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15) !important; }
.shadow-lg { box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175) !important; }

// 溢出
.overflow-auto { overflow: auto !important; }
.overflow-hidden { overflow: hidden !important; }
.overflow-visible { overflow: visible !important; }
.overflow-scroll { overflow: scroll !important; }

// 响应式网格工具类
.grid-2 {
  @include responsive-grid(2, 3, 4);
}

.grid-3 {
  @include responsive-grid(2, 3, 5);
}

.grid-4 {
  @include responsive-grid(2, 4, 6);
}

// 响应式间距
.gap-1 { gap: 0.25rem; }
.gap-2 { gap: 0.5rem; }
.gap-3 { gap: 1rem; }
.gap-4 { gap: 1.5rem; }
.gap-5 { gap: 3rem; }

// 响应式卡片
.card {
  @include responsive-card();
}

.card-sm {
  @include responsive-card(16rpx, 24rpx, 8rpx);
}

.card-lg {
  @include responsive-card(32rpx, 48rpx, 16rpx);
}

// 响应式按钮
.btn {
  @include responsive-button();
}

.btn-sm {
  @include responsive-button(28rpx, 30rpx, 16rpx 32rpx, 20rpx 36rpx);
}

.btn-lg {
  @include responsive-button(36rpx, 40rpx, 24rpx 48rpx, 28rpx 56rpx);
}

// 响应式表单
.form-control {
  @include responsive-form-control();
}

.form-control-sm {
  @include responsive-form-control(64rpx, 72rpx, 16rpx);
}

.form-control-lg {
  @include responsive-form-control(96rpx, 104rpx, 24rpx);
}

// 响应式图片
.img-responsive {
  @include responsive-image();
}

// 宽高比工具类
.ratio-1x1 { @include aspect-ratio(1, 1); }
.ratio-4x3 { @include aspect-ratio(4, 3); }
.ratio-16x9 { @include aspect-ratio(16, 9); }
.ratio-21x9 { @include aspect-ratio(21, 9); }

// 响应式视频
.embed-responsive {
  @include responsive-video();
}

// 颜色工具类
.text-primary { color: #007aff !important; }
.text-secondary { color: #8e8e93 !important; }
.text-success { color: #34c759 !important; }
.text-danger { color: #ff3b30 !important; }
.text-warning { color: #ff9500 !important; }
.text-info { color: #5ac8fa !important; }
.text-light { color: #f2f2f7 !important; }
.text-dark { color: #1c1c1e !important; }
.text-muted { color: #8e8e93 !important; }
.text-white { color: #ffffff !important; }

.bg-primary { background-color: #007aff !important; }
.bg-secondary { background-color: #8e8e93 !important; }
.bg-success { background-color: #34c759 !important; }
.bg-danger { background-color: #ff3b30 !important; }
.bg-warning { background-color: #ff9500 !important; }
.bg-info { background-color: #5ac8fa !important; }
.bg-light { background-color: #f2f2f7 !important; }
.bg-dark { background-color: #1c1c1e !important; }
.bg-white { background-color: #ffffff !important; }
.bg-transparent { background-color: transparent !important; }