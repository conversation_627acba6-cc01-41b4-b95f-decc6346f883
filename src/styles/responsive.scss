// 响应式设计系统
// 统一的断点定义

// 断点配置
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
) !default;

// uni-app 特定断点 (rpx)
$uni-breakpoints: (
  xs: 0,
  sm: 750rpx,
  md: 1024rpx,
  lg: 1366rpx,
  xl: 1920rpx
) !default;

// 网格系统配置
$grid-columns: 12 !default;
$grid-gutter-width: 30rpx !default;
$grid-row-columns: 6 !default;

// 容器最大宽度
$container-max-widths: (
  sm: 540px,
  md: 720px,
  lg: 960px,
  xl: 1140px,
  xxl: 1320px
) !default;

// 响应式断点 Mixin
@mixin media-breakpoint-up($name, $breakpoints: $breakpoints) {
  $min: map-get($breakpoints, $name);
  @if $min != 0 {
    @media (min-width: $min) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin media-breakpoint-down($name, $breakpoints: $breakpoints) {
  $max: map-get($breakpoints, $name);
  @if $max != 0 {
    @media (max-width: $max - 1px) {
      @content;
    }
  }
}

@mixin media-breakpoint-between($lower, $upper, $breakpoints: $breakpoints) {
  $min: map-get($breakpoints, $lower);
  $max: map-get($breakpoints, $upper);

  @if $min != 0 and $max != 0 {
    @media (min-width: $min) and (max-width: $max - 1px) {
      @content;
    }
  } @else if $max == 0 {
    @include media-breakpoint-up($lower, $breakpoints) {
      @content;
    }
  } @else if $min == 0 {
    @include media-breakpoint-down($upper, $breakpoints) {
      @content;
    }
  }
}

@mixin media-breakpoint-only($name, $breakpoints: $breakpoints) {
  $min: map-get($breakpoints, $name);
  $next: map-get($breakpoints, $name);
  $max: if($next != null, $next - 1px, null);

  @if $min != 0 and $max != null {
    @media (min-width: $min) and (max-width: $max) {
      @content;
    }
  } @else if $max == null {
    @include media-breakpoint-up($name, $breakpoints) {
      @content;
    }
  } @else if $min == 0 {
    @include media-breakpoint-down($next, $breakpoints) {
      @content;
    }
  }
}

// uni-app 专用响应式 Mixin
@mixin uni-media-up($name) {
  $min: map-get($uni-breakpoints, $name);
  @if $min != 0 {
    @media (min-width: $min) {
      @content;
    }
  } @else {
    @content;
  }
}

@mixin uni-media-down($name) {
  $max: map-get($uni-breakpoints, $name);
  @if $max != 0 {
    @media (max-width: $max - 1rpx) {
      @content;
    }
  }
}

// 网格系统 Mixins
@mixin make-container($gutter: $grid-gutter-width) {
  width: 100%;
  padding-right: calc($gutter / 2);
  padding-left: calc($gutter / 2);
  margin-right: auto;
  margin-left: auto;
}

@mixin make-container-max-widths($max-widths: $container-max-widths, $breakpoints: $breakpoints) {
  @each $breakpoint, $container-max-width in $max-widths {
    @include media-breakpoint-up($breakpoint, $breakpoints) {
      max-width: $container-max-width;
    }
  }
}

@mixin make-row($gutter: $grid-gutter-width) {
  display: flex;
  flex-wrap: wrap;
  margin-right: calc($gutter / -2);
  margin-left: calc($gutter / -2);
}

@mixin make-col-ready($gutter: $grid-gutter-width) {
  position: relative;
  width: 100%;
  padding-right: calc($gutter / 2);
  padding-left: calc($gutter / 2);
}

@mixin make-col($size, $columns: $grid-columns) {
  flex: 0 0 percentage($size / $columns);
  max-width: percentage($size / $columns);
}

@mixin make-col-auto() {
  flex: 0 0 auto;
  width: auto;
  max-width: 100%;
}

@mixin make-col-offset($size, $columns: $grid-columns) {
  $num: $size / $columns;
  margin-left: if($num == 0, 0, percentage($num));
}

// 响应式字体大小
@mixin responsive-font-size($min-size, $max-size, $min-width: 320px, $max-width: 1200px) {
  font-size: $min-size;
  
  @media (min-width: $min-width) and (max-width: $max-width) {
    font-size: calc(#{$min-size} + #{strip-unit($max-size - $min-size)} * ((100vw - #{$min-width}) / #{strip-unit($max-width - $min-width)}));
  }
  
  @media (min-width: $max-width) {
    font-size: $max-size;
  }
}

// 辅助函数：移除单位
@function strip-unit($number) {
  @if type-of($number) == 'number' and not unitless($number) {
    @return $number / ($number * 0 + 1);
  }
  @return $number;
}

// 响应式间距
@mixin responsive-spacing($property, $min-value, $max-value, $min-width: 320px, $max-width: 1200px) {
  #{$property}: $min-value;
  
  @media (min-width: $min-width) and (max-width: $max-width) {
    #{$property}: calc(#{$min-value} + #{strip-unit($max-value - $min-value)} * ((100vw - #{$min-width}) / #{strip-unit($max-width - $min-width)}));
  }
  
  @media (min-width: $max-width) {
    #{$property}: $max-value;
  }
}

// 响应式网格布局
@mixin responsive-grid($columns-mobile: 2, $columns-tablet: 3, $columns-desktop: 4, $gap: 20rpx) {
  display: grid;
  gap: $gap;
  grid-template-columns: repeat($columns-mobile, 1fr);
  
  @include uni-media-up(md) {
    grid-template-columns: repeat($columns-tablet, 1fr);
  }
  
  @include uni-media-up(lg) {
    grid-template-columns: repeat($columns-desktop, 1fr);
  }
}

// 响应式 Flexbox 布局
@mixin responsive-flex($direction-mobile: column, $direction-desktop: row, $gap: 20rpx) {
  display: flex;
  gap: $gap;
  flex-direction: $direction-mobile;
  
  @include uni-media-up(md) {
    flex-direction: $direction-desktop;
  }
}

// 响应式隐藏/显示
@mixin hide-on($breakpoint) {
  @include uni-media-up($breakpoint) {
    display: none !important;
  }
}

@mixin show-on($breakpoint) {
  display: none !important;
  
  @include uni-media-up($breakpoint) {
    display: block !important;
  }
}

@mixin show-flex-on($breakpoint) {
  display: none !important;
  
  @include uni-media-up($breakpoint) {
    display: flex !important;
  }
}

// 响应式文本对齐
@mixin responsive-text-align($mobile: left, $desktop: center) {
  text-align: $mobile;
  
  @include uni-media-up(md) {
    text-align: $desktop;
  }
}

// 响应式宽高比
@mixin aspect-ratio($width, $height) {
  position: relative;
  
  &::before {
    content: '';
    display: block;
    padding-top: percentage($height / $width);
  }
  
  > * {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 响应式图片
@mixin responsive-image() {
  max-width: 100%;
  height: auto;
  display: block;
}

// 响应式视频
@mixin responsive-video() {
  position: relative;
  padding-bottom: 56.25%; // 16:9 aspect ratio
  height: 0;
  overflow: hidden;
  
  iframe,
  object,
  embed,
  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// 响应式卡片布局
@mixin responsive-card($padding-mobile: 20rpx, $padding-desktop: 40rpx, $border-radius: 12rpx) {
  padding: $padding-mobile;
  border-radius: $border-radius;
  background: #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  
  @include uni-media-up(md) {
    padding: $padding-desktop;
  }
}

// 响应式按钮
@mixin responsive-button($size-mobile: 32rpx, $size-desktop: 36rpx, $padding-mobile: 20rpx 40rpx, $padding-desktop: 24rpx 48rpx) {
  font-size: $size-mobile;
  padding: $padding-mobile;
  border: none;
  border-radius: 8rpx;
  cursor: pointer;
  transition: all 0.2s ease;
  
  @include uni-media-up(md) {
    font-size: $size-desktop;
    padding: $padding-desktop;
  }
  
  &:hover {
    transform: translateY(-2rpx);
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.15);
  }
  
  &:active {
    transform: translateY(0);
  }
}

// 响应式表单元素
@mixin responsive-form-control($height-mobile: 80rpx, $height-desktop: 88rpx, $padding: 20rpx) {
  width: 100%;
  height: $height-mobile;
  padding: 0 $padding;
  border: 2rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  transition: border-color 0.2s ease;
  
  @include uni-media-up(md) {
    height: $height-desktop;
    font-size: 30rpx;
  }
  
  &:focus {
    outline: none;
    border-color: #007aff;
    box-shadow: 0 0 0 4rpx rgba(0, 122, 255, 0.1);
  }
}