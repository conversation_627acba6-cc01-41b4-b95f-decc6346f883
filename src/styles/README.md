# 响应式设计系统

本项目的响应式设计系统提供了统一的断点、网格系统和工具类，确保应用在不同设备上都有良好的用户体验。

## 断点系统

### 标准断点 (CSS)
```scss
$breakpoints: (
  xs: 0,
  sm: 576px,
  md: 768px,
  lg: 992px,
  xl: 1200px,
  xxl: 1400px
);
```

### uni-app 断点 (rpx)
```scss
$uni-breakpoints: (
  xs: 0,
  sm: 750rpx,
  md: 1024rpx,
  lg: 1366rpx,
  xl: 1920rpx
);
```

## 使用方法

### 1. 导入样式文件

在你的组件或页面中导入响应式样式：

```scss
@import '@/styles/responsive.scss';
@import '@/styles/utilities.scss';
```

### 2. 使用 Mixins

#### 响应式断点
```scss
.my-component {
  font-size: 14px;
  
  @include media-breakpoint-up(md) {
    font-size: 16px;
  }
  
  @include media-breakpoint-up(lg) {
    font-size: 18px;
  }
}
```

#### uni-app 专用断点
```scss
.uni-component {
  padding: 20rpx;
  
  @include uni-media-up(md) {
    padding: 40rpx;
  }
}
```

#### 响应式网格
```scss
.product-grid {
  @include responsive-grid(2, 3, 4); // 移动端2列，平板3列，桌面4列
}
```

#### 响应式字体
```scss
.title {
  @include responsive-font-size(24rpx, 48rpx);
}
```

#### 响应式间距
```scss
.section {
  @include responsive-spacing(padding, 20rpx, 60rpx);
}
```

### 3. 使用工具类

#### 网格系统
```html
<view class="container">
  <view class="row">
    <view class="col-12 col-md-6 col-lg-4">内容1</view>
    <view class="col-12 col-md-6 col-lg-4">内容2</view>
    <view class="col-12 col-md-6 col-lg-4">内容3</view>
  </view>
</view>
```

#### 响应式显示/隐藏
```html
<view class="d-block d-md-none">只在移动端显示</view>
<view class="d-none d-md-block">只在桌面端显示</view>
```

#### Flexbox 布局
```html
<view class="d-flex justify-content-center align-items-center">
  <text>居中内容</text>
</view>
```

#### 响应式文本对齐
```html
<text class="text-center text-md-left">响应式对齐文本</text>
```

## 组件适配指南

### 1. 图片组件
```vue
<template>
  <view class="image-container">
    <image :src="src" class="img-responsive" />
  </view>
</template>

<style lang="scss" scoped>
.image-container {
  @include aspect-ratio(16, 9);
  
  @include uni-media-up(md) {
    @include aspect-ratio(4, 3);
  }
}
</style>
```

### 2. 卡片组件
```vue
<template>
  <view class="card">
    <view class="card-content">
      <slot />
    </view>
  </view>
</template>

<style lang="scss" scoped>
.card {
  @include responsive-card();
  
  .card-content {
    @include responsive-spacing(padding, 20rpx, 40rpx);
  }
}
</style>
```

### 3. 按钮组件
```vue
<template>
  <button class="btn" :class="sizeClass">
    <slot />
  </button>
</template>

<script setup>
const props = defineProps({
  size: {
    type: String,
    default: 'medium',
    validator: value => ['small', 'medium', 'large'].includes(value)
  }
});

const sizeClass = computed(() => {
  return {
    'btn-sm': props.size === 'small',
    'btn-lg': props.size === 'large'
  };
});
</script>

<style lang="scss" scoped>
.btn {
  @include responsive-button();
}
</style>
```

## 最佳实践

### 1. 移动优先设计
始终从移动端开始设计，然后向上扩展到更大的屏幕：

```scss
.component {
  // 移动端样式（默认）
  padding: 20rpx;
  font-size: 28rpx;
  
  // 平板样式
  @include uni-media-up(md) {
    padding: 40rpx;
    font-size: 32rpx;
  }
  
  // 桌面样式
  @include uni-media-up(lg) {
    padding: 60rpx;
    font-size: 36rpx;
  }
}
```

### 2. 使用语义化断点
使用有意义的断点名称，而不是具体的像素值：

```scss
// 好的做法
@include uni-media-up(md) { /* 平板样式 */ }

// 避免的做法
@media (min-width: 768px) { /* 样式 */ }
```

### 3. 保持一致性
在整个项目中使用相同的断点和间距系统：

```scss
// 统一的间距
.section { @include responsive-spacing(margin-bottom, 40rpx, 80rpx); }
.card { @include responsive-spacing(padding, 20rpx, 40rpx); }

// 统一的网格
.product-list { @include responsive-grid(2, 3, 4); }
.article-list { @include responsive-grid(1, 2, 3); }
```

### 4. 测试不同设备
确保在不同设备和屏幕尺寸上测试你的组件：

- 手机 (375px - 414px)
- 平板 (768px - 1024px)
- 桌面 (1200px+)

### 5. 性能考虑
- 使用 CSS Grid 和 Flexbox 进行布局
- 避免过多的媒体查询嵌套
- 使用 `transform` 而不是改变 `width/height` 进行动画

## 常用模式

### 1. 响应式导航
```scss
.navigation {
  @include responsive-flex(column, row);
  
  .nav-item {
    @include responsive-spacing(margin, 10rpx 0, 0 20rpx);
  }
}
```

### 2. 响应式表格
```scss
.table-responsive {
  overflow-x: auto;
  
  @include uni-media-up(md) {
    overflow-x: visible;
  }
  
  table {
    min-width: 600px;
    
    @include uni-media-up(md) {
      min-width: auto;
    }
  }
}
```

### 3. 响应式模态框
```scss
.modal {
  width: 90vw;
  max-width: 500px;
  
  @include uni-media-up(md) {
    width: 600px;
  }
  
  @include uni-media-up(lg) {
    width: 800px;
  }
}
```

## 调试技巧

### 1. 使用浏览器开发者工具
- 使用设备模拟器测试不同屏幕尺寸
- 检查媒体查询是否正确应用

### 2. 添加调试样式
```scss
// 开发环境下显示当前断点
body::before {
  content: 'XS';
  position: fixed;
  top: 0;
  right: 0;
  background: red;
  color: white;
  padding: 5px;
  z-index: 9999;
  
  @include uni-media-up(sm) { content: 'SM'; }
  @include uni-media-up(md) { content: 'MD'; }
  @include uni-media-up(lg) { content: 'LG'; }
  @include uni-media-up(xl) { content: 'XL'; }
}
```

### 3. 使用 CSS 自定义属性
```scss
:root {
  --container-padding: 20rpx;
  
  @include uni-media-up(md) {
    --container-padding: 40rpx;
  }
}

.container {
  padding: var(--container-padding);
}
```

这个响应式设计系统为项目提供了完整的解决方案，确保在所有设备上都能提供一致且优秀的用户体验。