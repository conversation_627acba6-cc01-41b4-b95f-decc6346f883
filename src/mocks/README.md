# Mock Service Worker (MSW) Setup

This directory contains mock handlers for API testing using Mock Service Worker.

## Installation Issue

During setup, MSW installation encountered dependency conflicts with the uni-app vite plugin. The installation failed with:

```
npm error ERESOLVE could not resolve
npm error peer vite@"^5.2.8" from @dcloudio/vite-plugin-uni
```

## Current Status

The mock structure has been created as placeholders:

- `handlers.js` - Contains mock API handlers
- `server.js` - Server setup for Node.js testing environment  
- `browser.js` - Browser setup for development
- `README.md` - This documentation

## To Complete MSW Setup

1. Resolve the vite version conflict with uni-app
2. Install MSW: `npm install msw --save-dev`
3. Uncomment the actual MSW code in the files above
4. Initialize MSW: `npx msw init public/ --save`

## Alternative Approaches

If MSW continues to have conflicts, consider:
- Using vitest's built-in mocking capabilities
- Creating manual fetch mocks in test files
- Using a different mocking library compatible with uni-app