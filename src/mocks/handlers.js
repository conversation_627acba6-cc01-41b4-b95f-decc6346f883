// Mock Service Worker handlers for API mocking
// This file contains mock handlers for testing API interactions

// Note: MSW installation encountered dependency conflicts
// This is a placeholder structure for when MSW is properly installed

export const handlers = [
  // Auth handlers
  // rest.post('/api/auth/login', (req, res, ctx) => {
  //   return res(ctx.json({ token: 'mock-token', user: { id: 1, username: 'testuser' } }))
  // }),
  // Clothing handlers
  // rest.get('/api/clothing', (req, res, ctx) => {
  //   return res(ctx.json([{ id: 1, name: 'Test Clothing', type: 'shirt' }]))
  // }),
  // Add more handlers as needed
];

// Export default for easy importing
export default handlers;
