// Mock Service Worker browser setup for development
// This file sets up the mock server for browser environment

// Note: MSW installation encountered dependency conflicts
// This is a placeholder structure for when MSW is properly installed

// import { setupWorker } from 'msw/browser'
// import { handlers } from './handlers'

// This configures a Service Worker with the given request handlers.
// export const worker = setupWorker(...handlers)

// Placeholder export for now
export const worker = {
  start: () => console.log('Mock worker placeholder - MSW not installed'),
  stop: () => console.log('Mock worker placeholder - MSW not installed'),
  resetHandlers: () =>
    console.log('Mock worker placeholder - MSW not installed')
};
