// src/stores/calendarStore.js
import { defineStore } from 'pinia';
import { useOutfitStore } from './outfitStore'; // To potentially fetch outfit details

export const useCalendarStore = defineStore('calendar', {
  state: () => ({
    // Example: { '2024-07-28': 'outfitId1', '2024-07-29': 'outfitId2' }
    // For simplicity, we'll store one outfit ID per date.
    // Could be extended to an array of IDs if multiple outfits per day are needed.
    outfitAssignments: {}
    // To store full outfit objects if needed, reducing lookups,
    // but could lead to data duplication if outfits are edited.
    // For now, we'll stick to IDs and fetch details on demand.
  }),

  getters: {
    /**
     * Gets the outfit ID assigned to a specific date.
     * @param {object} state
     * @returns {function(string): string | undefined}
     */
    getOutfitIdForDate: state => dateString => {
      return state.outfitAssignments[dateString];
    },

    /**
     * Gets all assignments for a given month (YYYY-MM).
     * @param {object} state
     * @returns {function(string): object} - { 'YYYY-MM-DD': 'outfitId', ... }
     */
    getAssignmentsForMonth: state => monthString => {
      // monthString e.g., "2024-07"
      const assignments = {};
      for (const date in state.outfitAssignments) {
        if (date.startsWith(monthString)) {
          assignments[date] = state.outfitAssignments[date];
        }
      }
      return assignments;
    }
  },

  actions: {
    /**
     * Assigns an outfit to a specific date.
     * If an outfit is already assigned, it will be replaced.
     * @param {string} dateString - Date in 'YYYY-MM-DD' format
     * @param {string} outfitId
     */
    assignOutfitToDate(dateString, outfitId) {
      if (!dateString || !outfitId) {
        console.warn(
          'assignOutfitToDate: dateString and outfitId are required.'
        );
        return;
      }
      this.outfitAssignments[dateString] = outfitId;
      // In a real app, you'd save this to a backend or local storage here.
    },

    /**
     * Removes an outfit assignment from a specific date.
     * @param {string} dateString - Date in 'YYYY-MM-DD' format
     */
    removeOutfitFromDate(dateString) {
      if (!this.outfitAssignments[dateString]) {
        console.warn(
          `removeOutfitFromDate: No outfit assigned to ${dateString}.`
        );
        return;
      }
      delete this.outfitAssignments[dateString];
      // In a real app, you'd update this in a backend or local storage here.
    },

    /**
     * Fetches the full outfit details for a given date.
     * (Illustrative - uses outfitStore, assumes outfitStore has mock data)
     * @param {string} dateString - Date in 'YYYY-MM-DD' format
     * @returns {object | null} - Outfit object or null if not found/assigned
     */
    async fetchOutfitForDate(dateString) {
      const outfitId = this.getOutfitIdForDate(dateString);
      if (!outfitId) {
        return null;
      }
      const outfitStore = useOutfitStore();
      // Assuming outfitStore.getOutfitById(id) exists or can be added.
      // If outfits are not fetched yet in outfitStore, you might need to fetch them.
      let outfit = outfitStore.getOutfitById(outfitId);
      if (!outfit && outfitStore.outfits.length === 0) {
        // Try fetching all outfits if the store is empty (mock scenario)
        await outfitStore.fetchUserOutfitsAction(); // This is an async action
        outfit = outfitStore.getOutfitById(outfitId);
      }
      return outfit;
    }
  }
});
