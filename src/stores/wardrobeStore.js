// src/stores/wardrobeStore.js
import { defineStore } from 'pinia';
import { clothingApi } from '@/api/clothing';
import { collectionApi } from '@/api/collection';
import { ERROR_MESSAGES } from '@/constants/errorMessages.js';
import { colorSystems, categorySystem } from '@/constants/options';
import { ref } from 'vue';

// 全局事件总线用于跨页面状态同步
const eventBus = ref(new Map());

export const useWardrobeStore = defineStore('wardrobe', {
  state: () => ({
    wardrobeItems: [], // 存放所有衣物对象
    isLoading: false,
    error: null,
    hasMore: true,
    currentPage: 1,
    pageSize: 20,
    totalItems: 0,
    colorSystems, // 导入自 @/constants/options
    categorySystem, // 导入自 @/constants/options
    // 实时更新相关状态
    lastUpdateTime: null,
    updateListeners: new Set(),
    syncStatus: 'idle', // idle, syncing, success, error
    pendingUpdates: []
  }),

  getters: {
    getAllItems: state => state.wardrobeItems,

    getItemById: state => id => {
      return state.wardrobeItems.find(item => item.id === id);
    },

    getItemsByCategory: state => category => {
      return state.wardrobeItems.filter(item => item.category === category);
    },

    getLoadingStatus: state => state.isLoading,

    getError: state => state.error,

    getHasMore: state => state.hasMore,

    getCurrentPage: state => state.currentPage,

    getPageSize: state => state.pageSize,

    getTotalItems: state => state.totalItems,

    getLastUpdateTime: state => state.lastUpdateTime,

    getSyncStatus: state => state.syncStatus,

    getPendingUpdates: state => state.pendingUpdates
  },

  actions: {
    resetState() {
      this.wardrobeItems = [];
      this.isLoading = false;
      this.error = null;
      this.hasMore = true;
      this.currentPage = 1;
      this.totalItems = 0;
      this.lastUpdateTime = null;
      this.syncStatus = 'idle';
      this.pendingUpdates = [];
    },

    // 实时更新通知机制
    addUpdateListener(callback) {
      this.updateListeners.add(callback);
      return () => this.updateListeners.delete(callback);
    },

    removeUpdateListener(callback) {
      this.updateListeners.delete(callback);
    },

    notifyUpdate(type, data) {
      this.lastUpdateTime = new Date();
      
      // 通知所有监听器
      this.updateListeners.forEach(callback => {
        try {
          callback({ type, data, timestamp: this.lastUpdateTime });
        } catch (error) {
          console.warn('更新监听器执行失败:', error);
        }
      });

      // 触发全局事件
      this.emitGlobalEvent('wardrobe:update', { type, data });
    },

    emitGlobalEvent(eventName, data) {
      const listeners = eventBus.value.get(eventName) || [];
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.warn('全局事件监听器执行失败:', error);
        }
      });
    },

    onGlobalEvent(eventName, callback) {
      if (!eventBus.value.has(eventName)) {
        eventBus.value.set(eventName, []);
      }
      eventBus.value.get(eventName).push(callback);
      
      // 返回取消监听的函数
      return () => {
        const listeners = eventBus.value.get(eventName) || [];
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      };
    },

    setSyncStatus(status) {
      this.syncStatus = status;
      this.notifyUpdate('sync_status_change', { status });
    },

    addPendingUpdate(update) {
      this.pendingUpdates.push({
        ...update,
        id: `update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date()
      });
    },

    removePendingUpdate(updateId) {
      this.pendingUpdates = this.pendingUpdates.filter(
        update => update.id !== updateId
      );
    },

    processPendingUpdates() {
      const updates = [...this.pendingUpdates];
      this.pendingUpdates = [];
      
      updates.forEach(update => {
        try {
          this.applyUpdate(update);
        } catch (error) {
          console.warn('处理待更新项失败:', error);
          // 重新添加到待更新列表
          this.addPendingUpdate(update);
        }
      });
    },

    applyUpdate(update) {
      switch (update.type) {
        case 'item_added':
          this.wardrobeItems.unshift(update.data);
          this.totalItems++;
          break;
        case 'item_updated':
          const updateIndex = this.wardrobeItems.findIndex(
            item => item.id === update.data.id
          );
          if (updateIndex !== -1) {
            this.wardrobeItems[updateIndex] = update.data;
          }
          break;
        case 'item_deleted':
          this.wardrobeItems = this.wardrobeItems.filter(
            item => item.id !== update.data.id
          );
          this.totalItems--;
          break;
        case 'favorite_toggled':
          const favoriteIndex = this.wardrobeItems.findIndex(
            item => item.id === update.data.id
          );
          if (favoriteIndex !== -1) {
            this.wardrobeItems[favoriteIndex].isFavorite = update.data.isFavorite;
          }
          break;
      }
    },

    async fetchItems(params = {}) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await clothingApi.getClothingList({
          page: this.currentPage,
          pageSize: this.pageSize,
          ...params
        });

        if (response.success) {
          const { items, total, page, pageSize } = response.data;

          if (page === 1) {
            this.wardrobeItems = items;
          } else {
            this.wardrobeItems = [...this.wardrobeItems, ...items];
          }

          this.totalItems = total;
          this.currentPage = page;
          this.pageSize = pageSize;
          this.hasMore = this.wardrobeItems.length < total;

          return items;
        }
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.FETCH_WARDROBE_FAILED;
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async loadMore(params = {}) {
      if (!this.hasMore || this.isLoading) return;

      this.currentPage += 1;
      await this.fetchItems(params);
    },

    async refreshItems(params = {}) {
      this.currentPage = 1;
      this.hasMore = true;
      await this.fetchItems(params);
    },

    async addItemAction(itemData) {
      this.isLoading = true;
      this.error = null;
      this.setSyncStatus('syncing');

      try {
        const response = await clothingApi.addClothing(itemData);

        if (response.success) {
          this.wardrobeItems.unshift(response.data);
          this.totalItems++;
          
          // 通知实时更新
          this.notifyUpdate('item_added', response.data);
          this.setSyncStatus('success');
          
          return response.data;
        }
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.ADD_ITEM_FAILED;
        this.setSyncStatus('error');
        
        // 添加到待更新列表，等待网络恢复后重试
        this.addPendingUpdate({
          type: 'item_added',
          data: itemData,
          operation: 'add'
        });
        
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async updateItemAction(itemId, updateData) {
      this.isLoading = true;
      this.error = null;
      this.setSyncStatus('syncing');

      try {
        const response = await clothingApi.updateClothing(itemId, updateData);

        if (response.success) {
          const index = this.wardrobeItems.findIndex(
            item => item.id === itemId
          );
          if (index !== -1) {
            this.wardrobeItems[index] = response.data;
          }
          
          // 通知实时更新
          this.notifyUpdate('item_updated', response.data);
          this.setSyncStatus('success');
          
          return response.data;
        }
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.UPDATE_ITEM_FAILED;
        this.setSyncStatus('error');
        
        // 添加到待更新列表
        this.addPendingUpdate({
          type: 'item_updated',
          data: { id: itemId, ...updateData },
          operation: 'update'
        });
        
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    async deleteItemAction(itemId) {
      this.isLoading = true;
      this.error = null;
      this.setSyncStatus('syncing');

      try {
        const response = await clothingApi.deleteClothing(itemId);

        if (response.success) {
          const index = this.wardrobeItems.findIndex(
            item => item.id === itemId
          );
          if (index !== -1) {
            const deletedItem = this.wardrobeItems[index];
            this.wardrobeItems.splice(index, 1);
            this.totalItems--;
            
            // 通知实时更新
            this.notifyUpdate('item_deleted', { id: itemId, item: deletedItem });
            this.setSyncStatus('success');
          }
          return true;
        }
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.DELETE_ITEM_FAILED;
        this.setSyncStatus('error');
        
        // 添加到待更新列表
        this.addPendingUpdate({
          type: 'item_deleted',
          data: { id: itemId },
          operation: 'delete'
        });
        
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 别名方法，用于向后兼容
    async deleteItem(itemId) {
      return await this.deleteItemAction(itemId);
    },

    // 获取衣橱物品的别名方法，用于向后兼容
    async fetchWardrobeItemsAction(params = {}) {
      return await this.fetchItems(params);
    },

    // 根据ID获取单个衣物详情（异步方法）
    async getItemByIdAsync(itemId) {
      this.isLoading = true;
      this.error = null;

      try {
        // 首先检查本地缓存
        const cachedItem = this.wardrobeItems.find(item => item.id === itemId);
        if (cachedItem) {
          return cachedItem;
        }

        // 如果本地没有，从API获取
        const response = await clothingApi.getClothingDetail(itemId);

        if (response.success) {
          const item = response.data;

          // 更新本地缓存
          const existingIndex = this.wardrobeItems.findIndex(
            existingItem => existingItem.id === itemId
          );

          if (existingIndex !== -1) {
            this.wardrobeItems[existingIndex] = item;
          } else {
            this.wardrobeItems.push(item);
          }

          return item;
        } else {
          throw new Error(response.message || '获取衣物详情失败');
        }
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.FETCH_WARDROBE_FAILED;
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 切换收藏状态
    async toggleFavorite(itemId) {
      this.isLoading = true;
      this.error = null;
      this.setSyncStatus('syncing');

      try {
        const response = await collectionApi.toggleFavorite(itemId);

        if (response.success) {
          // 更新本地缓存中的收藏状态
          const item = this.wardrobeItems.find(item => item.id === itemId);
          if (item) {
            item.isFavorite = !item.isFavorite;
            
            // 通知实时更新
            this.notifyUpdate('favorite_toggled', {
              id: itemId,
              isFavorite: item.isFavorite,
              item
            });
            this.setSyncStatus('success');
          }
          return { success: true, isFavorite: item?.isFavorite };
        } else {
          throw new Error(response.message || '切换收藏状态失败');
        }
      } catch (error) {
        this.error = error.message || '切换收藏状态失败';
        this.setSyncStatus('error');
        
        // 添加到待更新列表
        this.addPendingUpdate({
          type: 'favorite_toggled',
          data: { id: itemId },
          operation: 'toggle_favorite'
        });
        
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    getColorSystem(color) {
      if (!color) return '其他';

      for (const systemInfo of Object.values(this.colorSystems)) {
        if (systemInfo.colors.includes(color.toLowerCase())) {
          return systemInfo.name;
        }
      }

      return '其他';
    },

    areColorsInSameSystem(color1, color2) {
      return this.getColorSystem(color1) === this.getColorSystem(color2);
    },

    getColorHarmonyScore(colors) {
      if (!colors || colors.length < 2) return 1;

      const systems = new Set();
      const validColors = colors.filter(Boolean);
      validColors.forEach(color => {
        const system = this.getColorSystem(color);
        systems.add(system);
      });

      return 1 - (systems.size - 1) / validColors.length;
    },

    isItemInCategory(item, category, subcategory = null) {
      try {
        if (!item || !category) return false;

        if (category === 'dresses' && item.category === 'dresses') {
          return true;
        }

        const isMainCategoryMatch = item.category === category;
        if (!subcategory) return isMainCategoryMatch;

        return isMainCategoryMatch && item.subcategory === subcategory;
      } catch (error) {
        return false;
      }
    },

    getItemOccasions(item) {
      if (!item || !item.styleTags) return new Set();

      const occasions = new Set();
      if (item.styleTags.includes('formal')) occasions.add('formal');
      if (item.styleTags.includes('casual')) occasions.add('casual');
      if (item.styleTags.includes('sports')) occasions.add('sports');
      if (item.styleTags.includes('party')) occasions.add('party');
      if (item.styleTags.includes('work')) occasions.add('work');

      return occasions;
    },

    areItemsOccasionCompatible(item1, item2) {
      const occasions1 = this.getItemOccasions(item1);
      const occasions2 = this.getItemOccasions(item2);

      if (occasions1.size === 0 || occasions2.size === 0) return true;

      for (const occasion of occasions1) {
        if (occasions2.has(occasion)) return true;
      }

      return false;
    },

    getCategoryPath(category, subcategory, type) {
      const mainCategory = this.categorySystem[category];
      if (!mainCategory) return [];

      const sub = mainCategory.subcategories[subcategory];
      if (!sub) return [mainCategory.name];

      if (!type || !sub.types.includes(type)) {
        return [mainCategory.name, sub.name];
      }

      return [mainCategory.name, sub.name, type];
    }
  }
});
