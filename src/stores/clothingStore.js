/**
 * Clothing Store
 *
 * Manages clothing items state including CRUD operations, filtering, and pagination.
 * Provides centralized state management for all clothing-related data and operations.
 *
 * @store clothingStore
 * @example
 * import { useClothingStore } from '@/stores/clothingStore'
 *
 * const clothingStore = useClothingStore()
 * await clothingStore.fetchClothingList()
 * const clothing = clothingStore.getClothingById('123')
 */
import { defineStore } from 'pinia';
import { clothingApi } from '@/api/clothing';
import { ERROR_MESSAGES } from '@/constants/errorMessages';

export const useClothingStore = defineStore('clothing', {
  /**
   * Store State
   * @typedef {Object} ClothingState
   * @property {Array} clothingList - Array of clothing items
   * @property {Object|null} currentClothing - Currently selected clothing item
   * @property {number} totalItems - Total number of clothing items
   * @property {boolean} isLoading - Loading state for async operations
   * @property {string|null} error - Error message if any operation fails
   * @property {Object} filterParams - Current filter and pagination parameters
   */
  state: () => ({
    clothingList: [],
    currentClothing: null,
    totalItems: 0,
    isLoading: false,
    error: null,
    filterParams: {
      page: 1,
      pageSize: 20,
      category: '',
      subcategory: '',
      type: '',
      season: '',
      color: '',
      searchText: ''
    }
  }),

  /**
   * Store Getters
   * Computed properties that derive data from the state
   */
  getters: {
    /**
     * Get clothing item by ID
     * @param {string} id - Clothing item ID
     * @returns {Object|null} Clothing item or null if not found
     */
    getClothingById: state => id => {
      return state.clothingList.find(item => item.id === id) || null;
    }
  },

  /**
   * Store Actions
   * Methods for state mutations and async operations
   */
  actions: {
    /**
     * Set filter parameters for clothing list
     * @param {Object} params - Filter parameters to update
     * @param {number} params.page - Page number
     * @param {number} params.pageSize - Items per page
     * @param {string} params.category - Category filter
     * @param {string} params.subcategory - Subcategory filter
     * @param {string} params.type - Type filter
     * @param {string} params.season - Season filter
     * @param {string} params.color - Color filter
     * @param {string} params.searchText - Search text filter
     */
    setFilterParams(params) {
      this.filterParams = {
        ...this.filterParams,
        ...params,
        page: params.page || 1 // 如果更改筛选条件，重置到第一页
      };
    },

    /**
     * Reset all filter parameters to default values
     */
    resetFilterParams() {
      this.filterParams = {
        page: 1,
        pageSize: 20,
        category: '',
        subcategory: '',
        type: '',
        season: '',
        color: '',
        searchText: ''
      };
    },

    /**
     * Fetch clothing list with optional parameters
     * @param {Object} params - Optional query parameters
     * @returns {Promise<Object>} API response with clothing list
     * @throws {Error} When API call fails
     */
    async fetchClothingList(params = {}) {
      this.isLoading = true;
      this.error = null;

      const queryParams = {
        ...this.filterParams,
        ...params
      };

      try {
        const response = await clothingApi.getClothingList(queryParams);

        if (response.success) {
          this.clothingList = response.data.items;
          this.totalItems = response.data.total;
        }

        return response;
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.GET_CLOTHING_LIST_FAILED;
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetch detailed information for a specific clothing item
     * @param {string} clothingId - Clothing item ID
     * @returns {Promise<Object>} API response with clothing details
     * @throws {Error} When API call fails
     */
    async fetchClothingDetail(clothingId) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await clothingApi.getClothingDetail(clothingId);

        if (response.success) {
          this.currentClothing = response.data;

          // 更新列表中的数据
          const index = this.clothingList.findIndex(
            item => item.id === clothingId
          );
          if (index !== -1) {
            this.clothingList[index] = response.data;
          }
        }

        return response;
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.GET_CLOTHING_DETAIL_FAILED;
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Add a new clothing item
     * @param {Object} clothingData - Clothing item data
     * @param {string} clothingData.name - Clothing name
     * @param {string} clothingData.category - Category
     * @param {string} clothingData.subcategory - Subcategory
     * @param {string} clothingData.type - Type
     * @param {string} clothingData.color - Color
     * @param {string} clothingData.season - Season
     * @param {Array<string>} clothingData.imageUrls - Image URLs
     * @returns {Promise<Object>} API response with created clothing item
     * @throws {Error} When API call fails
     */
    async addClothing(clothingData) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await clothingApi.addClothing(clothingData);

        if (response.success) {
          // 添加到列表头部
          this.clothingList = [response.data, ...this.clothingList];
          this.totalItems++;
        }

        return response;
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.ADD_CLOTHING_FAILED;
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Update an existing clothing item
     * @param {string} clothingId - Clothing item ID
     * @param {Object} clothingData - Updated clothing data (partial updates allowed)
     * @returns {Promise<Object>} API response with updated clothing item
     * @throws {Error} When API call fails
     */
    async updateClothing(clothingId, clothingData) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await clothingApi.updateClothing(
          clothingId,
          clothingData
        );

        if (response.success) {
          // 更新当前衣物
          if (this.currentClothing && this.currentClothing.id === clothingId) {
            this.currentClothing = response.data;
          }

          // 更新列表中的数据
          const index = this.clothingList.findIndex(
            item => item.id === clothingId
          );
          if (index !== -1) {
            this.clothingList[index] = response.data;
          }
        }

        return response;
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.UPDATE_CLOTHING_FAILED;
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Delete a clothing item
     * @param {string} clothingId - Clothing item ID
     * @returns {Promise<Object>} API response confirming deletion
     * @throws {Error} When API call fails
     */
    async deleteClothing(clothingId) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await clothingApi.deleteClothing(clothingId);

        if (response.success) {
          // 从列表中移除
          this.clothingList = this.clothingList.filter(
            item => item.id !== clothingId
          );
          this.totalItems--;

          // 如果当前查看的是被删除的衣物，则清空当前衣物
          if (this.currentClothing && this.currentClothing.id === clothingId) {
            this.currentClothing = null;
          }
        }

        return response;
      } catch (error) {
        this.error = error.message || ERROR_MESSAGES.DELETE_CLOTHING_FAILED;
        throw error;
      } finally {
        this.isLoading = false;
      }
    }
  }
});
