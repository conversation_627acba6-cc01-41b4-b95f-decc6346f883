/**
 * Outfit Store
 *
 * Manages outfit collections state including CRUD operations, favorites, and filtering.
 * Handles outfit creation, management, and organization functionality.
 *
 * @store outfitStore
 * @example
 * import { useOutfitStore } from '@/stores/outfitStore'
 *
 * const outfitStore = useOutfitStore()
 * await outfitStore.fetchOutfitList()
 * const outfit = outfitStore.getOutfitById('123')
 * await outfitStore.toggleFavorite('123')
 */
import { defineStore } from 'pinia';
import { outfitApi } from '@/api/outfit';
import { ref } from 'vue';

// 全局事件总线用于跨页面状态同步
const eventBus = ref(new Map());

export const useOutfitStore = defineStore('outfit', {
  /**
   * Store State
   * @typedef {Object} OutfitState
   * @property {Array} outfitList - Array of outfit items
   * @property {Object|null} currentOutfit - Currently selected outfit
   * @property {number} totalItems - Total number of outfits
   * @property {boolean} isLoading - Loading state for async operations
   * @property {string|null} error - Error message if any operation fails
   * @property {Object} filterParams - Current filter and pagination parameters
   */
  state: () => ({
    outfitList: [],
    currentOutfit: null,
    totalItems: 0,
    isLoading: false,
    error: null,
    filterParams: {
      page: 1,
      pageSize: 20
    },
    // 实时更新相关状态
    lastUpdateTime: null,
    updateListeners: new Set(),
    syncStatus: 'idle', // idle, syncing, success, error
    pendingUpdates: []
  }),

  /**
   * Store Getters
   * Computed properties that derive data from the state
   */
  getters: {
    /**
     * Get outfit by ID
     * @param {string} id - Outfit ID
     * @returns {Object|null} Outfit item or null if not found
     */
    getOutfitById: state => id => {
      return state.outfitList.find(item => item.id === id) || null;
    },

    /**
     * Get all favorited outfits
     * @returns {Array} Array of favorited outfit items
     */
    getFavoriteOutfits: state => {
      return state.outfitList.filter(outfit => outfit.isFavorited);
    },

    /**
     * Get outfits that contain a specific clothing item
     * @param {string} clothingId - Clothing item ID
     * @returns {Array} Array of outfits containing the clothing item
     */
    getOutfitsByClothingId: state => clothingId => {
      return state.outfitList.filter(outfit => {
        return (
          outfit.clothingItems &&
          outfit.clothingItems.some(item => item.id === clothingId)
        );
      });
    },

    getLastUpdateTime: state => state.lastUpdateTime,

    getSyncStatus: state => state.syncStatus,

    getPendingUpdates: state => state.pendingUpdates
  },

  /**
   * Store Actions
   * Methods for state mutations and async operations
   */
  actions: {
    /**
     * Set filter parameters for outfit list
     * @param {Object} params - Filter parameters to update
     * @param {number} params.page - Page number
     * @param {number} params.pageSize - Items per page
     */
    setFilterParams(params) {
      this.filterParams = {
        ...this.filterParams,
        ...params,
        page: params.page || 1 // 如果更改筛选条件，重置到第一页
      };
    },

    // 实时更新通知机制
    addUpdateListener(callback) {
      this.updateListeners.add(callback);
      return () => this.updateListeners.delete(callback);
    },

    removeUpdateListener(callback) {
      this.updateListeners.delete(callback);
    },

    notifyUpdate(type, data) {
      this.lastUpdateTime = new Date();
      
      // 通知所有监听器
      this.updateListeners.forEach(callback => {
        try {
          callback({ type, data, timestamp: this.lastUpdateTime });
        } catch (error) {
          console.warn('更新监听器执行失败:', error);
        }
      });

      // 触发全局事件
      this.emitGlobalEvent('outfit:update', { type, data });
    },

    emitGlobalEvent(eventName, data) {
      const listeners = eventBus.value.get(eventName) || [];
      listeners.forEach(callback => {
        try {
          callback(data);
        } catch (error) {
          console.warn('全局事件监听器执行失败:', error);
        }
      });
    },

    onGlobalEvent(eventName, callback) {
      if (!eventBus.value.has(eventName)) {
        eventBus.value.set(eventName, []);
      }
      eventBus.value.get(eventName).push(callback);
      
      // 返回取消监听的函数
      return () => {
        const listeners = eventBus.value.get(eventName) || [];
        const index = listeners.indexOf(callback);
        if (index > -1) {
          listeners.splice(index, 1);
        }
      };
    },

    setSyncStatus(status) {
      this.syncStatus = status;
      this.notifyUpdate('sync_status_change', { status });
    },

    addPendingUpdate(update) {
      this.pendingUpdates.push({
        ...update,
        id: `update_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: new Date()
      });
    },

    removePendingUpdate(updateId) {
      this.pendingUpdates = this.pendingUpdates.filter(
        update => update.id !== updateId
      );
    },

    processPendingUpdates() {
      const updates = [...this.pendingUpdates];
      this.pendingUpdates = [];
      
      updates.forEach(update => {
        try {
          this.applyUpdate(update);
        } catch (error) {
          console.warn('处理待更新项失败:', error);
          // 重新添加到待更新列表
          this.addPendingUpdate(update);
        }
      });
    },

    applyUpdate(update) {
      switch (update.type) {
        case 'outfit_created':
          this.outfitList = [update.data, ...this.outfitList];
          this.totalItems++;
          break;
        case 'outfit_updated':
          const updateIndex = this.outfitList.findIndex(
            outfit => outfit.id === update.data.id
          );
          if (updateIndex !== -1) {
            this.outfitList[updateIndex] = {
              ...this.outfitList[updateIndex],
              ...update.data
            };
          }
          break;
        case 'outfit_deleted':
          this.outfitList = this.outfitList.filter(
            outfit => outfit.id !== update.data.id
          );
          this.totalItems--;
          break;
        case 'outfit_favorite_toggled':
          const favoriteIndex = this.outfitList.findIndex(
            outfit => outfit.id === update.data.id
          );
          if (favoriteIndex !== -1) {
            this.outfitList[favoriteIndex].isFavorited = update.data.isFavorited;
          }
          break;
      }
    },

    /**
     * Fetch outfit list with optional parameters
     * @param {Object} params - Optional query parameters
     * @returns {Promise<Object>} API response with outfit list
     * @throws {Error} When API call fails
     */
    async fetchOutfitList(params = {}) {
      this.isLoading = true;
      this.error = null;

      const queryParams = {
        ...this.filterParams,
        ...params
      };

      try {
        const response = await outfitApi.getOutfitList(queryParams);

        if (response.success) {
          this.outfitList = response.data.items;
          this.totalItems = response.data.total;
        }

        return response;
      } catch (error) {
        this.error = error.message || '获取穿搭列表失败';
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetch detailed information for a specific outfit
     * @param {string} outfitId - Outfit ID
     * @returns {Promise<Object>} API response with outfit details
     * @throws {Error} When API call fails
     */
    async fetchOutfitDetail(outfitId) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await outfitApi.getOutfitDetail(outfitId);

        if (response.success) {
          this.currentOutfit = response.data;

          // 更新列表中的数据
          const index = this.outfitList.findIndex(item => item.id === outfitId);
          if (index !== -1) {
            this.outfitList[index] = {
              ...this.outfitList[index],
              ...response.data
            };
          }
        }

        return response;
      } catch (error) {
        this.error = error.message || '获取穿搭详情失败';
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Create a new outfit
     * @param {Object} outfitData - Outfit data
     * @param {string} outfitData.name - Outfit name
     * @param {string} outfitData.occasion - Occasion type
     * @param {string} outfitData.notes - Optional notes
     * @param {Array<string>} outfitData.clothingItems - Array of clothing item IDs
     * @returns {Promise<Object>} API response with created outfit
     * @throws {Error} When API call fails
     */
    async createOutfit(outfitData) {
      this.isLoading = true;
      this.error = null;
      this.setSyncStatus('syncing');

      try {
        const response = await outfitApi.createOutfit(outfitData);

        if (response.success) {
          // 添加到列表头部
          this.outfitList = [response.data, ...this.outfitList];
          this.totalItems++;
          
          // 通知实时更新
          this.notifyUpdate('outfit_created', response.data);
          this.setSyncStatus('success');
          
          return response.success;
        }

        return false;
      } catch (error) {
        this.error = error.message || '创建穿搭失败';
        this.setSyncStatus('error');
        
        // 添加到待更新列表
        this.addPendingUpdate({
          type: 'outfit_created',
          data: outfitData,
          operation: 'create'
        });
        
        console.error('Create outfit error:', error);
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Add a new outfit (alias for createOutfit for backward compatibility)
     * @param {Object} outfitData - Outfit data
     * @returns {Promise<boolean>} Success status
     */
    async addOutfit(outfitData) {
      // Transform itemIds to clothingItems for API compatibility
      const apiData = {
        name: outfitData.name,
        occasion: outfitData.occasion || '',
        notes: outfitData.notes || '',
        clothingItems: outfitData.itemIds || []
      };

      return await this.createOutfit(apiData);
    },

    /**
     * Update an existing outfit
     * @param {string} outfitId - Outfit ID
     * @param {Object} outfitData - Updated outfit data (partial updates allowed)
     * @returns {Promise<boolean>} Success status
     * @throws {Error} When API call fails
     */
    async updateOutfit(outfitId, outfitData) {
      this.isLoading = true;
      this.error = null;
      this.setSyncStatus('syncing');

      try {
        // Transform itemIds to clothingItems for API compatibility
        const apiData = {
          name: outfitData.name,
          occasion: outfitData.occasion || '',
          notes: outfitData.notes || '',
          clothingItems: outfitData.itemIds || []
        };

        const response = await outfitApi.updateOutfit(outfitId, apiData);

        if (response.success) {
          // 更新当前穿搭
          if (this.currentOutfit && this.currentOutfit.id === outfitId) {
            this.currentOutfit = {
              ...this.currentOutfit,
              ...response.data
            };
          }

          // 更新列表中的数据
          const index = this.outfitList.findIndex(item => item.id === outfitId);
          if (index !== -1) {
            this.outfitList[index] = {
              ...this.outfitList[index],
              ...response.data
            };
          }
          
          // 通知实时更新
          this.notifyUpdate('outfit_updated', response.data);
          this.setSyncStatus('success');
          
          return true;
        }

        return false;
      } catch (error) {
        this.error = error.message || '更新穿搭失败';
        this.setSyncStatus('error');
        
        // 添加到待更新列表
        this.addPendingUpdate({
          type: 'outfit_updated',
          data: { id: outfitId, ...outfitData },
          operation: 'update'
        });
        
        console.error('Update outfit error:', error);
        return false;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Delete an outfit
     * @param {string} outfitId - Outfit ID
     * @returns {Promise<Object>} API response confirming deletion
     * @throws {Error} When API call fails
     */
    async deleteOutfit(outfitId) {
      this.isLoading = true;
      this.error = null;
      this.setSyncStatus('syncing');

      try {
        const response = await outfitApi.deleteOutfit(outfitId);

        if (response.success) {
          // 保存被删除的穿搭信息用于通知
          const deletedOutfit = this.outfitList.find(item => item.id === outfitId);
          
          // 从列表中移除
          this.outfitList = this.outfitList.filter(
            item => item.id !== outfitId
          );
          this.totalItems--;

          // 如果当前查看的是被删除的穿搭，则清空当前穿搭
          if (this.currentOutfit && this.currentOutfit.id === outfitId) {
            this.currentOutfit = null;
          }
          
          // 通知实时更新
          this.notifyUpdate('outfit_deleted', { id: outfitId, outfit: deletedOutfit });
          this.setSyncStatus('success');
        }

        return response;
      } catch (error) {
        this.error = error.message || '删除穿搭失败';
        this.setSyncStatus('error');
        
        // 添加到待更新列表
        this.addPendingUpdate({
          type: 'outfit_deleted',
          data: { id: outfitId },
          operation: 'delete'
        });
        
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Toggle favorite status of an outfit
     * @param {string} outfitId - Outfit ID
     * @returns {Promise<Object>} API response with updated favorite status
     * @throws {Error} When API call fails
     */
    async toggleFavorite(outfitId) {
      this.isLoading = true;
      this.error = null;
      this.setSyncStatus('syncing');

      try {
        const response = await outfitApi.toggleFavorite(outfitId);

        if (response.success) {
          // 更新当前穿搭
          if (this.currentOutfit && this.currentOutfit.id === outfitId) {
            this.currentOutfit = {
              ...this.currentOutfit,
              isFavorited: response.data.isFavorited
            };
          }

          // 更新列表中的数据
          const index = this.outfitList.findIndex(item => item.id === outfitId);
          if (index !== -1) {
            this.outfitList[index] = {
              ...this.outfitList[index],
              isFavorited: response.data.isFavorited
            };
          }
          
          // 通知实时更新
          this.notifyUpdate('outfit_favorite_toggled', {
            id: outfitId,
            isFavorited: response.data.isFavorited,
            outfit: this.outfitList[index]
          });
          this.setSyncStatus('success');
        }

        return response;
      } catch (error) {
        this.error = error.message || '切换收藏状态失败';
        this.setSyncStatus('error');
        
        // 添加到待更新列表
        this.addPendingUpdate({
          type: 'outfit_favorite_toggled',
          data: { id: outfitId },
          operation: 'toggle_favorite'
        });
        
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    /**
     * Fetch outfits that contain a specific clothing item
     * @param {string} clothingId - Clothing item ID
     * @returns {Promise<Array>} Array of outfits containing the clothing item
     * @throws {Error} When API call fails
     */
    async fetchOutfitsByClothingId(clothingId) {
      this.isLoading = true;
      this.error = null;

      try {
        // First check local cache
        const cachedOutfits = this.getOutfitsByClothingId(clothingId);
        if (cachedOutfits.length > 0) {
          return cachedOutfits;
        }

        // If not in cache, fetch all outfits and filter
        // TODO: In the future, add a specific API endpoint for this
        const response = await this.fetchOutfitList();

        if (response.success) {
          return this.getOutfitsByClothingId(clothingId);
        } else {
          return [];
        }
      } catch (error) {
        this.error = error.message || '获取相关穿搭失败';
        console.error('获取相关穿搭失败:', error);
        return []; // Return empty array instead of throwing to not break the main flow
      } finally {
        this.isLoading = false;
      }
    }
  }
});
