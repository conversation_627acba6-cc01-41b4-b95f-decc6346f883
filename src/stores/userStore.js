// src/stores/userStore.js
import { defineStore } from 'pinia';
import { authApi } from '@/api/auth';
import { navigateAfterLogout } from '@/utils/navigation';

export const useUserStore = defineStore('user', {
  state: () => ({
    token: uni.getStorageSync('token') || null,
    userInfo: uni.getStorageSync('userInfo') || null,
    isLoading: false,
    error: null
  }),

  getters: {
    isLoggedIn: state => !!state.token,
    userId: state => state.userInfo?.userId,
    username: state => state.userInfo?.username
  },

  actions: {
    // 登录
    async login(credentials) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await authApi.login(credentials);

        if (response.success) {
          this.token = response.data.token;
          this.userInfo = {
            userId: response.data.user.id,
            username: response.data.user.username,
            email: response.data.user.email
          };

          // 保存到本地存储
          uni.setStorageSync('token', this.token);
          uni.setStorageSync('userInfo', this.userInfo);

          return response;
        }
      } catch (error) {
        this.error = error.message || '登录失败';
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 注册
    async register(userData) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await authApi.register(userData);
        return response;
      } catch (error) {
        this.error = error.message || '注册失败';
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 更新用户信息
    async updateProfile(profileData) {
      this.isLoading = true;
      this.error = null;

      try {
        const response = await authApi.updateProfile(profileData);

        if (response.success) {
          // 更新本地用户信息
          this.userInfo = {
            ...this.userInfo,
            ...profileData
          };

          // 更新本地存储
          uni.setStorageSync('userInfo', this.userInfo);

          return response;
        }
      } catch (error) {
        this.error = error.message || '更新个人信息失败';
        throw error;
      } finally {
        this.isLoading = false;
      }
    },

    // 登出
    logout() {
      // 清除状态
      this.token = null;
      this.userInfo = null;

      // 清除本地存储
      authApi.logout();

      // 使用导航工具函数进行登出后导航
      navigateAfterLogout();
    },

    // 检查登录状态
    checkLoginStatus() {
      // 如果有token但没有userInfo，尝试获取用户信息
      if (this.token && !this.userInfo) {
        this.fetchUserInfo();
      }
      return this.isLoggedIn;
    },

    // 获取用户信息
    async fetchUserInfo() {
      if (!this.token) return;

      this.isLoading = true;

      try {
        const response = await authApi.getProfile();

        if (response.success) {
          this.userInfo = response.data;
          uni.setStorageSync('userInfo', this.userInfo);
        }
      } catch (error) {
        // 如果获取用户信息失败，可能是token过期
        if (error.statusCode === 401) {
          this.logout();
        }
        console.error('获取用户信息失败:', error);
      } finally {
        this.isLoading = false;
      }
    }
  }
});
