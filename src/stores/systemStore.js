// src/stores/systemStore.js
import { defineStore } from 'pinia';
import { systemApi } from '@/api/system';

export const useSystemStore = defineStore('system', {
  state: () => ({
    config: {
      categories: [],
      subcategories: {},
      types: {},
      colors: [],
      sizes: {},
      seasons: []
    },
    isLoading: false,
    error: null,
    lastFetchTime: null
  }),

  getters: {
    // 获取分类列表
    getCategories: state => state.config.categories,

    // 根据分类获取子分类
    getSubcategories: state => category => {
      return state.config.subcategories[category] || [];
    },

    // 根据分类和子分类获取类型
    getTypes: state => (category, subcategory) => {
      return state.config.types[category]?.[subcategory] || [];
    },

    // 获取颜色列表
    getColors: state => state.config.colors,

    // 根据分类获取尺码
    getSizes: state => category => {
      return state.config.sizes[category] || [];
    },

    // 获取季节列表
    getSeasons: state => state.config.seasons,

    // 检查配置是否已加载
    isConfigLoaded: state => state.config.categories.length > 0,

    // 获取配置的最后获取时间
    getLastFetchTime: state => state.lastFetchTime
  },

  actions: {
    // 获取系统配置
    async fetchSystemConfig(forceRefresh = false) {
      // 如果已经加载过配置，并且不强制刷新，则直接返回
      if (this.isConfigLoaded && !forceRefresh) {
        return this.config;
      }

      this.isLoading = true;
      this.error = null;

      try {
        const response = await systemApi.getSystemConfig();

        if (response.success) {
          this.config = response.data;
          this.lastFetchTime = new Date().toISOString();
        }

        return this.config;
      } catch (error) {
        this.error = error.message || '获取系统配置失败';
        throw error;
      } finally {
        this.isLoading = false;
      }
    }
  }
});
