/**
 * 系统选项数据配置文件
 * 包含所有硬编码的选项数据，便于统一管理和维护
 */

// 合身度选项
export const fitOptions = [
  { value: 'loose', text: '宽松' },
  { value: 'regular', text: '标准' },
  { value: 'slim', text: '修身' },
  { value: 'oversized', text: '超大' }
];

// 风格选项
export const styleOptions = [
  { value: 'casual', text: '休闲' },
  { value: 'formal', text: '正式' },
  { value: 'sports', text: '运动' },
  { value: 'vintage', text: '复古' },
  { value: 'elegant', text: '优雅' }
];

// 基础颜色选项
export const colorOptions = [
  { value: 'white', text: '白色', hex: '#FFFFFF' },
  { value: 'black', text: '黑色', hex: '#000000' },
  { value: 'gray', text: '灰色', hex: '#808080' },
  { value: 'red', text: '红色', hex: '#FF0000' },
  { value: 'blue', text: '蓝色', hex: '#0000FF' }
];

// 材质选项
export const materialOptions = [
  { value: 'cotton', text: '棉' },
  { value: 'wool', text: '羊毛' },
  { value: 'polyester', text: '聚酯纤维' },
  { value: 'silk', text: '丝绸' },
  { value: 'linen', text: '亚麻' }
];

// 季节选项
export const seasonOptions = [
  { value: 'spring', text: '春季' },
  { value: 'summer', text: '夏季' },
  { value: 'autumn', text: '秋季' },
  { value: 'winter', text: '冬季' }
];

// 颜色系统配置
export const colorSystems = {
  neutral: {
    name: '中性色系',
    colors: ['black', 'white', 'gray', 'beige', 'brown']
  },
  warm: {
    name: '暖色系',
    colors: ['red', 'orange', 'yellow', 'pink', 'coral']
  },
  cool: {
    name: '冷色系',
    colors: ['blue', 'green', 'purple', 'navy', 'teal']
  },
  earth: {
    name: '大地色系',
    colors: ['beige', 'brown', 'khaki', 'camel']
  },
  macaron: {
    name: '马卡龙色系',
    colors: [
      'pastel-pink',
      'pastel-blue',
      'pastel-yellow',
      'pastel-green',
      'pastel-purple'
    ]
  },
  morandi: {
    name: '莫兰迪色系',
    colors: ['dusty-pink', 'sage-green', 'muted-blue', 'warm-gray', 'taupe']
  }
};

// 衣物分类系统
export const categorySystem = {
  tops: {
    name: '上衣',
    subcategories: {
      outerwear: {
        name: '外套',
        types: ['夹克', '大衣', '西装', '风衣', '羽绒服', '棉服']
      },
      inner: {
        name: '内搭',
        types: ['T恤', '衬衫', 'POLO衫', '卫衣', '背心']
      },
      base: {
        name: '打底',
        types: ['打底衫', '针织衫', '背心']
      },
      tshirts: {
        name: 'T恤',
        types: ['圆领T恤', 'V领T恤', 'POLO衫']
      },
      shirts: {
        name: '衬衫',
        types: ['商务衬衫', '休闲衬衫', '法兰绒衬衫']
      },
      sweaters: {
        name: '毛衣',
        types: ['圆领毛衣', 'V领毛衣', '高领毛衣']
      },
      hoodies: {
        name: '卫衣',
        types: ['连帽卫衣', '圆领卫衣', '半拉链卫衣']
      }
    }
  },
  bottoms: {
    name: '下装',
    subcategories: {
      pants: {
        name: '裤装',
        types: ['休闲裤', '牛仔裤', '西裤', '运动裤', '短裤']
      },
      skirts: {
        name: '裙装',
        types: ['短裙', '中裙', '长裙', '百褶裙', 'A字裙']
      }
    }
  },
  shoes: {
    name: '鞋履',
    subcategories: {
      casual: {
        name: '休闲鞋',
        types: ['运动鞋', '帆布鞋', '乐福鞋', '平底鞋']
      },
      formal: {
        name: '正装鞋',
        types: ['高跟鞋', '牛津鞋', '德比鞋']
      },
      boots: {
        name: '靴子',
        types: ['短靴', '长靴', '雨靴', '马丁靴']
      }
    }
  },
  accessories: {
    name: '配饰',
    subcategories: {
      neck: {
        name: '颈部配饰',
        types: ['围巾', '项链', '领带']
      },
      head: {
        name: '头部配饰',
        types: ['帽子', '发饰', '眼镜']
      },
      hand: {
        name: '手部配饰',
        types: ['手表', '手链', '手套']
      }
    }
  },
  bags: {
    name: '包袋',
    subcategories: {
      daily: {
        name: '日常包',
        types: ['单肩包', '双肩包', '手提包', '托特包']
      },
      special: {
        name: '特殊场合',
        types: ['晚宴包', '运动包', '旅行包']
      }
    }
  },
  others: {
    name: '其他',
    subcategories: {
      misc: {
        name: '其他配件',
        types: ['袜子', '腰带', '雨伞']
      }
    }
  }
};

// 导出所有选项的便捷对象
export const clothingOptions = {
  fit: fitOptions,
  style: styleOptions,
  color: colorOptions,
  material: materialOptions,
  season: seasonOptions
};

// 导出系统配置的便捷对象
export const systemConfig = {
  colorSystems,
  categorySystem
};
