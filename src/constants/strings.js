// src/constants/strings.js
export const TOAST_TITLES = {
  LOADING: '加载中...',
  LOAD_FAILED_GENERAL: '获取数据失败，请稍后重试',
  SAVE_SUCCESS: '保存成功！',
  OPERATION_FAILED: '操作失败，请重试',
  FETCH_COLLECTION_FAILED: '获取收藏列表失败',
  FETCH_WARDROBE_FAILED: '获取衣物列表失败',
  FETCH_INITIAL_WARDROBE_FAILED: '加载初始数据失败，请稍后重试',
  FETCH_MORE_WARDROBE_FAILED: '加载更多数据失败，请稍后重试',
  ADD_ITEM_FAILED: '添加衣物失败',
  UPDATE_ITEM_FAILED: '更新衣物失败',
  DELETE_ITEM_FAILED: '删除衣物失败',
  ITEM_NOT_FOUND: '衣物不存在',
  CREATE_OUTFIT_FAILED: '创建搭配失败',
  UPDATE_OUTFIT_FAILED: '更新搭配失败',
  DELETE_OUTFIT_FAILED: '删除搭配失败',
  OUTFIT_NOT_FOUND: '搭配不存在',
  SHARE_OUTFIT_FAILED: '分享失败',
  FETCH_RECENT_OUTFITS_FAILED: '获取最近搭配失败',
  UPDATE_OUTFIT_FAVORITE_FAILED: '更新收藏状态失败',
  OUTFIT_CALENDAR_SOON: '穿搭日历功能即将上线',
  FILTER_SOON: '筛选功能开发中...',
  NEED_OUTFIT_NAME_AND_ITEMS: '请输入穿搭名称并至少选择一件衣物',
  UPDATING: '更新中...',
  UPDATE_SUCCESS: '衣物更新成功！',
  PROFILE_UPDATE_SUCCESS: '用户信息更新成功！',
  LOGGING_OUT: '正在退出...',
  LOGOUT_SUCCESS: '已退出登录',
  LOGOUT_FAILED: '退出失败，请稍后再试'
};

export const BUTTON_TEXTS = {
  ADD_ITEM: '添加新衣物',
  ADD_NEW_ITEM: '添加新衣物',
  EDIT_ITEM: '编辑衣物',
  ADD_MORE_ITEMS: '添加更多衣物',
  CONFIRM_SELECTION: '确认选择',
  SAVE_OUTFIT: '保存穿搭',
  ADD_TAG: '添加',
  FILTER: '筛选',
  ASSIGN_OUTFIT: '安排穿搭',
  UNASSIGN_OUTFIT: '取消安排',
  CHANGE_OUTFIT: '更换穿搭',
  CANCEL: '取消',
  RESET_FILTERS: '重置',
  APPLY_FILTERS: '应用',
  SAVE_CHANGES: '保存更改',
  SAVE_ITEM: '保存衣物',
  ADVANCED_FILTER: '高级筛选',
  CHANGE_AVATAR: '更换头像',
  EDIT_PROFILE: '编辑资料',
  LOGOUT: '退出登录'
};

export const PLACEHOLDERS = {
  OUTFIT_NAME: '为您的穿搭起个名字',
  OUTFIT_NOTES: '添加关于这套穿搭的备注（可选）',
  TAG_INPUT: '输入标签，按回车添加',
  SEARCH_ITEMS: '搜索衣物',
  SELECT_CATEGORY: '选择主分类',
  SELECT_SUBCATEGORY: '选择子分类',
  SELECT_TYPE: '选择具体类型',
  SELECT_COLOR_SYSTEM: '选择色系',
  ENTER_USERNAME: '请输入用户名',
  ENTER_BIO: '请输入个性签名'
};

export const MISC_TEXT = {
  WARDROBE_TITLE: '我的衣橱',
  CREATE_OUTFIT_TITLE: '创建穿搭',
  SELECT_ITEMS_TITLE: '选择衣物',
  OUTFIT_NAME_LABEL: '穿搭名称',
  SELECTED_ITEMS_LABEL_PREFIX: '已选择的衣物',
  OUTFIT_NOTES_LABEL: '穿搭备注',
  TAGS_LABEL: '标签',
  EMPTY_WARDROBE_PROMPT: '衣橱还是空的',
  EMPTY_WARDROBE_SUBPROMPT: '添加一些衣服开始你的时尚之旅吧',
  EMPTY_SELECTION_OUTFIT: '您还未选择任何衣物',
  LOAD_MORE_CONTENT_DOWN: '上拉显示更多',
  LOAD_MORE_CONTENT_REFRESH: '加载中...',
  LOAD_MORE_CONTENT_NOMORE: '没有更多了',
  SELECTED_COUNT_SUFFIX: '件衣物',
  SELECT_DATE_FIRST: '请先选择一个日期',
  OUTFIT_ASSIGNED_SUCCESS: '穿搭已成功安排！',
  NO_OUTFIT_TO_UNASSIGN: '当前日期没有穿搭可取消',
  OUTFIT_UNASSIGNED_SUCCESS: '穿搭已取消安排',
  NO_OUTFIT_ASSIGNED_TODAY: '当日无穿搭安排。',
  SELECT_OUTFIT_TITLE: '选择一套穿搭',
  NO_OUTFITS_CREATED_YET: '您还没有创建任何穿搭。',
  FILTER_TITLE: '筛选衣物',
  CATEGORY_FILTER_TITLE: '分类筛选',
  SEASON_FILTER_TITLE: '季节筛选',
  COLOR_SYSTEM_FILTER_TITLE: '色系筛选',
  NO_ITEMS_IN_WARDROBE: '您的衣橱里还没有任何衣物。',
  NO_ITEMS_MATCH_FILTERS: '没有找到符合筛选条件的衣物。'
};

export const ERROR_MESSAGES = {
  ITEM_NOT_FOUND: '衣物不存在',
  OUTFIT_NOT_FOUND: '搭配不存在',
  FETCH_PROFILE_FAILED: '获取用户信息失败',
  UPDATE_PROFILE_FAILED: '更新用户信息失败',
  PROFILE_NOT_LOADED: '用户配置未加载，无法更新'
};
