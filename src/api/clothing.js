import { http } from './http';

export const clothingApi = {
  // 添加衣物
  addClothing: clothingData => {
    return http.post('/clothing', clothingData);
  },

  // 获取衣物列表
  getClothingList: (params = { page: 1, pageSize: 20 }) => {
    return http.get('/clothing', params);
  },

  // 获取衣物详情
  getClothingDetail: clothingId => {
    return http.get(`/clothing/${clothingId}`);
  },

  // 更新衣物
  updateClothing: (clothingId, clothingData) => {
    return http.put(`/clothing/${clothingId}`, clothingData);
  },

  // 删除衣物
  deleteClothing: clothingId => {
    return http.delete(`/clothing/${clothingId}`);
  }
};
