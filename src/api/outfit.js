import { http } from './http';

export const outfitApi = {
  // 创建穿搭
  createOutfit: outfitData => {
    return http.post('/outfits', outfitData);
  },

  // 获取穿搭列表
  getOutfitList: (params = { page: 1, pageSize: 20 }) => {
    return http.get('/outfits', params);
  },

  // 获取穿搭详情
  getOutfitDetail: outfitId => {
    return http.get(`/outfits/${outfitId}`);
  },

  // 更新穿搭
  updateOutfit: (outfitId, outfitData) => {
    return http.put(`/outfits/${outfitId}`, outfitData);
  },

  // 删除穿搭
  deleteOutfit: outfitId => {
    return http.delete(`/outfits/${outfitId}`);
  },

  // 切换穿搭收藏状态
  toggleFavorite: outfitId => {
    return http.post('/favorites/toggle', {
      itemType: 'outfit',
      itemId: outfitId
    });
  },

  // 获取收藏的搭配列表
  getFavoriteOutfits: (params = { page: 1, pageSize: 20 }) => {
    return http.get('/favorites', {
      ...params,
      type: 'outfit'
    });
  },

  // 批量收藏搭配
  batchFavoriteOutfits: (action, outfitIds) => {
    const items = outfitIds.map(id => ({
      itemType: 'outfit',
      itemId: id
    }));
    return http.post('/favorites/batch', {
      action,
      items
    });
  },

  // 检查搭配收藏状态
  checkFavoriteStatus: outfitIds => {
    return http.get('/favorites/status', {
      itemType: 'outfit',
      itemIds: outfitIds.join(',')
    });
  }
};
