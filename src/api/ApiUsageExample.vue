<!-- src/api/ApiUsageExample.vue -->
<template>
  <view class="api-example">
    <view v-if="!isLoggedIn">
      <uni-forms ref="loginForm" :model-value="loginForm">
        <uni-forms-item label="用户名" name="username">
          <uni-easyinput
            v-model="loginForm.username"
            placeholder="请输入用户名"
          />
        </uni-forms-item>
        <uni-forms-item label="密码" name="password">
          <uni-easyinput
            v-model="loginForm.password"
            type="password"
            placeholder="请输入密码"
          />
        </uni-forms-item>
      </uni-forms>
      <button type="primary" @click="handleLogin">登录</button>
    </view>

    <view v-else>
      <view class="section">
        <view class="section-title">我的衣物</view>
        <button type="primary" @click="fetchClothingList">获取衣物列表</button>
        <view v-if="clothingList.length" class="item-list">
          <view
            v-for="item in clothingList"
            :key="item.id"
            class="item"
            @click="viewClothingDetail(item.id)"
          >
            {{ item.name }}
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">我的穿搭</view>
        <button type="primary" @click="fetchOutfitList">获取穿搭列表</button>
        <view v-if="outfitList.length" class="item-list">
          <view
            v-for="outfit in outfitList"
            :key="outfit.id"
            class="item"
            @click="viewOutfitDetail(outfit.id)"
          >
            {{ outfit.name }}
          </view>
        </view>
      </view>

      <view class="section">
        <view class="section-title">系统配置</view>
        <button type="primary" @click="fetchSystemConfig">获取系统配置</button>
        <view v-if="systemConfig.categories">
          <view>分类列表: {{ systemConfig.categories.join(', ') }}</view>
        </view>
      </view>
    </view>
  </view>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { authApi, clothingApi, outfitApi, systemApi } from './index';

// 登录表单
const loginForm = reactive({
  username: '',
  password: ''
});

// 状态
const isLoggedIn = ref(false);
const clothingList = ref([]);
const outfitList = ref([]);
const systemConfig = ref({});

// 检查登录状态
onMounted(() => {
  const token = uni.getStorageSync('token');
  isLoggedIn.value = !!token;

  if (isLoggedIn.value) {
    fetchClothingList();
  }
});

// 登录处理
const handleLogin = async () => {
  try {
    const response = await authApi.login({
      username: loginForm.username,
      password: loginForm.password
    });

    if (response.success) {
      // 保存token
      uni.setStorageSync('token', response.data.token);
      uni.setStorageSync('userInfo', {
        userId: response.data.userId,
        username: response.data.username
      });

      isLoggedIn.value = true;
      uni.showToast({
        title: '登录成功',
        icon: 'success'
      });

      // 登录成功后获取衣物列表
      fetchClothingList();
    }
  } catch (error) {
    console.error('登录失败:', error);
  }
};

// 获取衣物列表
const fetchClothingList = async () => {
  try {
    const response = await clothingApi.getClothingList();
    if (response.success) {
      clothingList.value = response.data.items;
    }
  } catch (error) {
    console.error('获取衣物列表失败:', error);
  }
};

// 查看衣物详情
const viewClothingDetail = async clothingId => {
  try {
    const response = await clothingApi.getClothingDetail(clothingId);
    if (response.success) {
      uni.showModal({
        title: '衣物详情',
        content: JSON.stringify(response.data, null, 2)
      });
    }
  } catch (error) {
    console.error('获取衣物详情失败:', error);
  }
};

// 获取穿搭列表
const fetchOutfitList = async () => {
  try {
    const response = await outfitApi.getOutfitList();
    if (response.success) {
      outfitList.value = response.data.items;
    }
  } catch (error) {
    console.error('获取穿搭列表失败:', error);
  }
};

// 查看穿搭详情
const viewOutfitDetail = async outfitId => {
  try {
    const response = await outfitApi.getOutfitDetail(outfitId);
    if (response.success) {
      uni.showModal({
        title: '穿搭详情',
        content: JSON.stringify(response.data, null, 2)
      });
    }
  } catch (error) {
    console.error('获取穿搭详情失败:', error);
  }
};

// 获取系统配置
const fetchSystemConfig = async () => {
  try {
    const response = await systemApi.getSystemConfig();
    if (response.success) {
      systemConfig.value = response.data;
    }
  } catch (error) {
    console.error('获取系统配置失败:', error);
  }
};
</script>

<style>
.api-example {
  padding: 20rpx;
}
.section {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #eee;
}
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}
.item-list {
  margin-top: 20rpx;
}
.item {
  padding: 20rpx;
  background-color: #f8f8f8;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
}
</style>
