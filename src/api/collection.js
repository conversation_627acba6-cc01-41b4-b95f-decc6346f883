// src/api/collection.js
import { http } from './http';

export const collectionApi = {
  // 切换收藏状态
  toggleFavorite: (itemId, itemType = 'clothing') => {
    return http.post('/favorites/toggle', {
      itemId,
      itemType
    });
  },

  // 获取收藏集合列表（旧版 favorites 接口的替代）
  getFavoriteClothings: (params = { page: 1, pageSize: 20 }) => {
    // 注意：此端点现在返回收藏集合列表，而不是衣物列表
    return http.get('/collections', params);
  },

  // 获取收藏集合列表（旧版 favorites 接口的替代）
  getFavoriteOutfits: (params = { page: 1, pageSize: 20 }) => {
    // 注意：此端点现在返回收藏集合列表，而不是搭配列表
    return http.get('/collections', params);
  },

  // 检查是否已收藏
  checkIsFavorited: (itemId, itemType = 'clothing') => {
    return http.get(`/favorites/status`, {
      itemId,
      itemType
    });
  },

  // 批量收藏操作
  batchToggleFavorite: (action, items) => {
    return http.post('/favorites/batch', {
      action,
      items
    });
  },

  // 获取收藏统计
  getFavoriteStats: () => {
    return http.get('/collections/stats');
  },

  // 搜索收藏 (功能已废弃, 返回空值)
  searchFavorites: (params = {}) => {
    console.warn('searchFavorites is deprecated and will be removed.');
    return Promise.resolve({ data: { items: [], total: 0 } });
  },

  // 获取搜索建议 (功能已废弃, 返回空值)
  getSearchSuggestions: (query, limit = 10) => {
    console.warn('getSearchSuggestions is deprecated and will be removed.');
    return Promise.resolve({ data: [] });
  },

  // 获取热门搜索词 (功能已废弃, 返回空值)
  getPopularSearchTerms: (limit = 5) => {
    console.warn('getPopularSearchTerms is deprecated and will be removed.');
    return Promise.resolve({ data: [] });
  },

  // 获取衣物详情
  getClothingDetail: clothingId => {
    return http.get(`/clothing/${clothingId}`);
  },

  // 获取搭配详情
  getOutfitDetail: outfitId => {
    return http.get(`/outfits/${outfitId}`);
  },

  // 收藏集合相关API
  getCollections: (params = { page: 1, pageSize: 20 }) => {
    return http.get('/collections', params);
  },

  createCollection: data => {
    return http.post('/collections', data);
  },

  updateCollection: (id, data) => {
    return http.put(`/collections/${id}`, data);
  },

  deleteCollection: id => {
    return http.delete(`/collections/${id}`);
  },

  addItemsToCollection: (collectionId, items) => {
    return http.post(`/collections/${collectionId}/items`, { items });
  },

  removeItemsFromCollection: (collectionId, items) => {
    return http.delete(`/collections/${collectionId}/items`, { items });
  },

  // 分享相关API
  shareCollection: (collectionId, options = {}) => {
    return http.post(`/collections/${collectionId}/share`, options);
  },

  shareFavoriteItem: (itemId, itemType, options = {}) => {
    return http.post('/favorites/share', {
      itemId,
      itemType,
      ...options
    });
  },

  // 批量分享收藏项目
  batchShareFavorites: (items, options = {}) => {
    return http.post('/favorites/share/batch', {
      items,
      ...options
    });
  },

  // 获取用户的分享列表
  getUserShares: (params = {}) => {
    return http.get('/favorites/shares', params);
  },

  // 删除分享
  deleteShare: shareId => {
    return http.delete(`/shares/${shareId}`);
  },

  // 获取分享统计
  getShareStats: () => {
    return http.get('/shares/stats');
  },

  // 通过分享令牌获取分享内容
  getSharedContent: shareToken => {
    return http.get(`/share/${shareToken}`);
  }
};

// 便捷的分享函数
export const shareCollection = params => {
  return collectionApi.shareFavoriteItem(params.itemId, params.itemType, {
    expiresIn: params.expiresIn,
    allowPublic: params.allowPublic
  });
};
