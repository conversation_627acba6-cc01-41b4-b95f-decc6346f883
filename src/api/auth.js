import { http } from './http';

export const authApi = {
  // 用户注册
  register: userData => {
    return http.post('/auth/register', userData);
  },

  // 用户登录
  login: credentials => {
    return http.post('/auth/login', credentials);
  },

  // 更新用户信息
  updateProfile: profileData => {
    return http.put('/auth/profile', profileData);
  },

  // 获取用户个人信息
  getProfile: () => {
    return http.get('/auth/profile');
  },

  // 登出
  logout: () => {
    // 清除本地存储的token
    uni.removeStorageSync('token');
    uni.removeStorageSync('userInfo');
  }
};
