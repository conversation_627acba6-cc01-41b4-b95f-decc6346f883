# API 服务层使用文档

本项目API服务层基于 Uni-app 的 `uni.request` 封装，提供与后端API的统一接口。

## 目录结构

```
src/api/
├── auth.js         // 用户认证相关API
├── clothing.js     // 衣物管理相关API
├── collection.js   // 收藏相关API
├── http.js         // HTTP请求工具
├── index.js        // 导出所有API
├── outfit.js       // 穿搭相关API
├── system.js       // 系统配置相关API
└── ApiUsageExample.vue  // API使用示例
```

## 使用方法

### 1. 引入API

```javascript
// 引入单个API服务
import { authApi } from '@/api/auth';

// 或引入多个API服务
import { authApi, clothingApi, outfitApi } from '@/api';
```

### 2. 认证相关 (authApi)

```javascript
// 用户注册
const register = async () => {
  try {
    const userData = {
      username: 'testuser',
      password: 'password123',
      email: '<EMAIL>'
    };
    const result = await authApi.register(userData);
    console.log('注册成功:', result);
  } catch (error) {
    console.error('注册失败:', error);
  }
};

// 用户登录
const login = async () => {
  try {
    const credentials = {
      username: 'testuser',
      password: 'password123'
    };
    const result = await authApi.login(credentials);
    console.log('登录成功:', result);

    // 登录成功后，token已自动保存到storage
  } catch (error) {
    console.error('登录失败:', error);
  }
};

// 更新用户信息
const updateProfile = async () => {
  try {
    const profileData = {
      username: 'newname',
      email: '<EMAIL>',
      bio: '个人简介'
    };
    const result = await authApi.updateProfile(profileData);
    console.log('更新成功:', result);
  } catch (error) {
    console.error('更新失败:', error);
  }
};

// 退出登录
const logout = () => {
  authApi.logout();
  // 此方法会清除本地存储的token和用户信息
};
```

### 3. 衣物管理 (clothingApi)

```javascript
// 添加衣物
const addClothing = async () => {
  try {
    const clothingData = {
      name: '蓝色衬衫',
      category: '上装',
      subcategory: '衬衫',
      type: '休闲衬衫',
      brand: '优衣库',
      color: '蓝色',
      size: 'M',
      season: ['春', '秋'],
      purchaseDate: '2023-01-15',
      price: 199.99,
      description: '一件蓝色的休闲衬衫',
      tags: ['商务', '休闲'],
      imageUrls: ['https://example.com/image1.jpg']
    };
    const result = await clothingApi.addClothing(clothingData);
    console.log('添加衣物成功:', result);
  } catch (error) {
    console.error('添加衣物失败:', error);
  }
};

// 获取衣物列表
const getClothingList = async () => {
  try {
    // 基本查询
    const result = await clothingApi.getClothingList();

    // 带查询参数
    const params = {
      page: 1,
      pageSize: 10,
      category: '上装',
      season: '春'
    };
    const filteredResult = await clothingApi.getClothingList(params);
    console.log('获取衣物列表成功:', filteredResult);
  } catch (error) {
    console.error('获取衣物列表失败:', error);
  }
};

// 获取衣物详情
const getClothingDetail = async clothingId => {
  try {
    const result = await clothingApi.getClothingDetail(clothingId);
    console.log('获取衣物详情成功:', result);
  } catch (error) {
    console.error('获取衣物详情失败:', error);
  }
};

// 更新衣物
const updateClothing = async clothingId => {
  try {
    const clothingData = {
      name: '更新的衣物名称',
      color: '红色'
    };
    const result = await clothingApi.updateClothing(clothingId, clothingData);
    console.log('更新衣物成功:', result);
  } catch (error) {
    console.error('更新衣物失败:', error);
  }
};

// 删除衣物
const deleteClothing = async clothingId => {
  try {
    const result = await clothingApi.deleteClothing(clothingId);
    console.log('删除衣物成功:', result);
  } catch (error) {
    console.error('删除衣物失败:', error);
  }
};
```

### 4. 收藏相关 (collectionApi)

```javascript
// 切换收藏状态
const toggleFavorite = async clothingId => {
  try {
    const result = await collectionApi.toggleFavorite(clothingId);
    console.log('切换收藏状态成功:', result);
    // result.data.isFavorited 表示当前收藏状态
  } catch (error) {
    console.error('切换收藏状态失败:', error);
  }
};

// 获取收藏列表
const getFavoriteClothings = async () => {
  try {
    const result = await collectionApi.getFavoriteClothings();
    console.log('获取收藏列表成功:', result);
  } catch (error) {
    console.error('获取收藏列表失败:', error);
  }
};
```

### 5. 穿搭相关 (outfitApi)

```javascript
// 创建穿搭
const createOutfit = async () => {
  try {
    const outfitData = {
      name: '春季商务穿搭',
      itemIds: ['衣物ID1', '衣物ID2', '衣物ID3'],
      occasion: '商务',
      notes: '适合春季商务场合的搭配'
    };
    const result = await outfitApi.createOutfit(outfitData);
    console.log('创建穿搭成功:', result);
  } catch (error) {
    console.error('创建穿搭失败:', error);
  }
};

// 获取穿搭列表
const getOutfitList = async () => {
  try {
    const result = await outfitApi.getOutfitList();
    console.log('获取穿搭列表成功:', result);
  } catch (error) {
    console.error('获取穿搭列表失败:', error);
  }
};

// 获取穿搭详情
const getOutfitDetail = async outfitId => {
  try {
    const result = await outfitApi.getOutfitDetail(outfitId);
    console.log('获取穿搭详情成功:', result);
  } catch (error) {
    console.error('获取穿搭详情失败:', error);
  }
};

// 更新穿搭
const updateOutfit = async outfitId => {
  try {
    const outfitData = {
      name: '更新的穿搭名称',
      notes: '更新的备注'
    };
    const result = await outfitApi.updateOutfit(outfitId, outfitData);
    console.log('更新穿搭成功:', result);
  } catch (error) {
    console.error('更新穿搭失败:', error);
  }
};

// 删除穿搭
const deleteOutfit = async outfitId => {
  try {
    const result = await outfitApi.deleteOutfit(outfitId);
    console.log('删除穿搭成功:', result);
  } catch (error) {
    console.error('删除穿搭失败:', error);
  }
};

// 切换穿搭收藏状态
const toggleOutfitFavorite = async outfitId => {
  try {
    const result = await outfitApi.toggleFavorite(outfitId);
    console.log('切换穿搭收藏状态成功:', result);
  } catch (error) {
    console.error('切换穿搭收藏状态失败:', error);
  }
};
```

### 6. 系统配置 (systemApi)

```javascript
// 获取系统配置
const getSystemConfig = async () => {
  try {
    const result = await systemApi.getSystemConfig();
    console.log('获取系统配置成功:', result);

    // 使用系统配置
    const { categories, subcategories, types, colors, sizes, seasons } = result.data;
    // 例如，更新表单选项等
  } catch (error) {
    console.error('获取系统配置失败:', error);
  }
};
```

## 错误处理

所有API调用都应该使用try-catch进行错误处理：

```javascript
try {
  const result = await someApi.someMethod();
  // 处理成功结果
} catch (error) {
  // 处理错误
  console.error('操作失败:', error);

  // 可以根据error.statusCode进行不同的错误处理
  if (error.statusCode === 401) {
    // 未授权，可能需要重新登录
    uni.navigateTo({ url: '/pages/login/login' });
  } else {
    // 显示错误提示
    uni.showToast({
      title: error.message || '操作失败，请重试',
      icon: 'none'
    });
  }
}
```

## 完整示例

请参考 `ApiUsageExample.vue` 文件，其中包含了常见API调用的完整示例。
