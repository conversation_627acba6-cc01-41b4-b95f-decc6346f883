import { http } from './http';
import { mockService } from './mockService';

// 导入 API_URL 配置
const PROD_BASE_URL = 'https://yigui-backend.qu18354531302.workers.dev';
const DEV_BASE_URL = 'http://localhost:8787';

const isUploadDevMode =
  process.env.NODE_ENV === 'development' ||
  (typeof location !== 'undefined' &&
    (location.hostname === 'localhost' ||
      location.hostname.includes('127.0.0.1')));

const BASE_URL = isUploadDevMode ? DEV_BASE_URL : PROD_BASE_URL;
const API_URL = `${BASE_URL}/api`;

// 支持的图片类型
const ALLOWED_IMAGE_TYPES = ['image/jpeg', 'image/jpg', 'image/png'];
// 最大文件大小（10MB）
const MAX_FILE_SIZE = 10 * 1024 * 1024;

// 检测当前运行环境
const isH5Upload =
  process.env.UNI_PLATFORM === 'h5' || typeof window !== 'undefined';
const isMP =
  process.env.UNI_PLATFORM === 'mp-weixin' ||
  (typeof uni !== 'undefined' &&
    uni.getSystemInfoSync().platform === 'devtools');
const isDevMode =
  process.env.NODE_ENV === 'development' ||
  (typeof location !== 'undefined' &&
    (location.hostname === 'localhost' ||
      location.hostname.includes('127.0.0.1')));

// 判断是否使用Mock服务
const useMockService = mockService.shouldUseMockService();

// 获取上传签名
const getUploadSignature = async (fileName, fileType) => {
  // H5开发环境使用Mock数据
  if (useMockService) {
    return mockService.getUploadSignature();
  }

  try {
    // 新的Cloudflare Workers API请求
    const params = new URLSearchParams({
      fileName: fileName || 'image.jpg',
      fileType: fileType || 'image/jpeg'
    });

    const response = await http.get(`/upload/signature?${params.toString()}`);

    // 适配新的响应格式
    return {
      success: response.success,
      data: {
        uploadUrl: response.data.uploadUrl,
        key: response.data.key,
        fileUrl: response.data.fileUrl,
        expires: response.data.expires
      }
    };
  } catch (error) {
    // 如果在H5开发环境中API请求失败，尝试回退到Mock服务
    if (isH5Upload && isDevMode) {
      console.warn('API请求失败，回退到Mock服务:', error.message);
      return mockService.getUploadSignature();
    }
    throw error;
  }
};

// 检查图片文件
const checkImageFile = tempFilePath => {
  return new Promise((resolve, reject) => {
    // 处理微信小程序临时文件路径的特殊情况
    const isWxTempFile =
      tempFilePath.startsWith('wxfile://') ||
      tempFilePath.startsWith('http://tmp/') ||
      tempFilePath.startsWith('file://');
    // 处理Blob URL
    const isBlobUrl = tempFilePath.startsWith('blob:');

    uni.getFileInfo({
      filePath: tempFilePath,
      digestAlgorithm: 'sha1', // 获取文件的sha1值，可用于进一步验证
      success: res => {
        // 检查文件大小
        if (res.size > MAX_FILE_SIZE) {
          reject(
            new Error(
              `图片大小不能超过10MB，当前大小: ${(res.size / 1024 / 1024).toFixed(2)}MB`
            )
          );
          return;
        }

        // 检查文件类型
        let isValidType = false;

        if (isWxTempFile || isBlobUrl) {
          // 对于微信临时文件或Blob URL，如果无法获取扩展名，直接假设有效
          // 由于微信小程序选择图片API本身会过滤图片类型，所以这里可以相对宽松处理
          isValidType = true;
        } else {
          // 常规扩展名检查
          try {
            const extPosition = tempFilePath.lastIndexOf('.');
            // 只有当找到扩展名时才进行检查
            if (extPosition !== -1 && extPosition < tempFilePath.length - 1) {
              const ext = tempFilePath.substring(extPosition + 1).toLowerCase();
              isValidType = ['jpg', 'jpeg', 'png'].includes(ext);
            } else {
              // 如果找不到扩展名但是通过了微信的选择图片API，可能是合法图片
              isValidType = true;
            }
          } catch (error) {
            console.warn('扩展名检查异常:', error);
            // 发生异常时，为避免阻断用户体验，允许上传
            isValidType = true;
          }
        }

        if (!isValidType) {
          reject(new Error('仅支持JPG/JPEG/PNG格式的图片，请重新选择'));
          return;
        }

        resolve(true);
      },
      fail: error => {
        // 文件信息获取失败
        console.warn('获取文件信息失败:', error);
        // 宽松处理，仍然允许上传尝试
        resolve(true);
      }
    });
  });
};

// 压缩图片
const compressImage = (tempFilePath, quality = 80) => {
  return new Promise((resolve, reject) => {
    // 先获取图片信息，根据大小决定压缩质量
    uni.getFileInfo({
      filePath: tempFilePath,
      success: fileInfo => {
        const fileSize = fileInfo.size;
        let targetQuality = quality;

        // 根据文件大小自动调整压缩质量
        if (fileSize > 5 * 1024 * 1024) {
          // 大于5MB
          targetQuality = 60;
        } else if (fileSize > 2 * 1024 * 1024) {
          // 大于2MB
          targetQuality = 70;
        } else if (fileSize > 1 * 1024 * 1024) {
          // 大于1MB
          targetQuality = 75;
        } else if (fileSize > 500 * 1024) {
          // 大于500KB
          targetQuality = 80;
        } else {
          targetQuality = 85; // 小图片使用较高质量
        }

        // 检测当前环境并使用对应的压缩方法
        if (isH5Upload) {
          // H5环境下直接返回原图，因为H5环境不支持uni.compressImage
          resolve(tempFilePath);
        } else {
          // 微信小程序环境下使用uni.compressImage
          uni.compressImage({
            src: tempFilePath,
            quality: targetQuality,
            success: res => {
              // 压缩后再次获取文件大小，用于日志
              uni.getFileInfo({
                filePath: res.tempFilePath,
                success: compressedInfo => {
                  // Log compression results for monitoring
                },
                complete: () => {
                  resolve(res.tempFilePath);
                }
              });
            },
            fail: error => {
              console.warn('图片压缩失败，将使用原图:', error);
              resolve(tempFilePath); // 压缩失败时使用原图
            }
          });
        }
      },
      fail: error => {
        // 获取文件信息失败，直接使用原图
        console.warn('获取文件信息失败，将使用原图:', error);
        resolve(tempFilePath);
      }
    });
  });
};

// 上传图片到云存储
const uploadImage = async (tempFilePath, onProgress) => {
  try {
    // 检查图片文件
    await checkImageFile(tempFilePath);

    // 压缩图片
    const compressedFilePath = await compressImage(tempFilePath);

    // 如果使用Mock服务，则调用Mock上传
    if (useMockService) {
      return await mockService.uploadFile(compressedFilePath, onProgress);
    }

    // 获取文件类型
    let fileType = 'image/jpeg';
    const extPosition = tempFilePath.lastIndexOf('.');
    if (extPosition !== -1) {
      const ext = tempFilePath.substring(extPosition + 1).toLowerCase();
      if (ext === 'png') fileType = 'image/png';
      else if (ext === 'gif') fileType = 'image/gif';
      else if (ext === 'webp') fileType = 'image/webp';
    }

    // 方法1: 尝试使用预签名URL上传（推荐）
    try {
      const fileName = `image-${Date.now()}.${fileType.split('/')[1]}`;
      const signatureRes = await getUploadSignature(fileName, fileType);

      if (signatureRes.success && signatureRes.data.uploadUrl) {
        // 使用预签名URL直接上传到R2
        return new Promise((resolve, reject) => {
          const uploadTask = uni.uploadFile({
            url: signatureRes.data.uploadUrl,
            filePath: compressedFilePath,
            name: 'file',
            header: {
              'Content-Type': fileType
            },
            success: uploadRes => {
              if (
                uploadRes.statusCode === 200 ||
                uploadRes.statusCode === 204
              ) {
                resolve({
                  success: true,
                  data: {
                    url: signatureRes.data.fileUrl,
                    key: signatureRes.data.key,
                    originalPath: tempFilePath
                  }
                });
              } else {
                reject(new Error('图片上传失败'));
              }
            },
            fail: error => {
              reject(new Error(error.errMsg || '图片上传失败'));
            }
          });

          // 监听上传进度
          if (onProgress && typeof onProgress === 'function') {
            uploadTask.onProgressUpdate(res => {
              onProgress(res.progress);
            });
          }
        });
      }
    } catch (presignError) {
      console.warn('预签名URL上传失败，尝试直接上传:', presignError.message);
    }

    // 方法2: 直接上传到Workers API（备用方案）
    return new Promise((resolve, reject) => {
      const uploadTask = uni.uploadFile({
        url: `${API_URL}/upload`,
        filePath: compressedFilePath,
        name: 'file',
        header: {
          Authorization: `Bearer ${uni.getStorageSync('token')}`
        },
        success: uploadRes => {
          try {
            if (uploadRes.statusCode === 200) {
              const response = JSON.parse(uploadRes.data);
              if (response.success) {
                resolve({
                  success: true,
                  data: {
                    url: response.data.url,
                    key: response.data.key,
                    originalPath: tempFilePath
                  }
                });
              } else {
                reject(new Error(response.message || '图片上传失败'));
              }
            } else {
              reject(new Error('图片上传失败'));
            }
          } catch (parseError) {
            reject(new Error('上传响应解析失败'));
          }
        },
        fail: error => {
          let errorMsg = '图片上传失败';

          // 处理不同类型的错误
          if (error.errMsg) {
            if (error.errMsg.includes('timeout')) {
              errorMsg = '上传超时，请检查网络后重试';
            } else if (error.errMsg.includes('fail')) {
              errorMsg = '上传失败，请重试';
            }
          }

          // 如果是H5开发环境，在上传失败时尝试使用Mock服务
          if (isH5Upload && isDevMode) {
            console.warn(
              '上传失败，尝试使用Mock服务:',
              error.errMsg || errorMsg
            );
            mockService
              .uploadFile(compressedFilePath, onProgress)
              .then(resolve)
              .catch(reject);
            return;
          }

          reject(new Error(error.errMsg || errorMsg));
        }
      });

      // 监听上传进度
      if (onProgress && typeof onProgress === 'function') {
        uploadTask.onProgressUpdate(res => {
          onProgress(res.progress);
        });
      }
    });
  } catch (error) {
    // 对于某些错误，如果在H5开发环境，尝试使用Mock服务
    if (isH5Upload && isDevMode && !useMockService) {
      console.warn('上传过程中出错，尝试使用Mock服务:', error.message);
      try {
        return await mockService.uploadFile(tempFilePath, onProgress);
      } catch (mockError) {
        throw mockError;
      }
    }
    throw error;
  }
};

// 批量上传图片
const uploadImages = async (tempFilePaths, onProgress) => {
  try {
    let totalProgress = 0;
    const progressMap = new Map();

    // 创建进度回调函数
    const createProgressCallback = index => {
      return progress => {
        progressMap.set(index, progress);

        // 计算总体进度
        if (onProgress && typeof onProgress === 'function') {
          const total = Array.from(progressMap.values()).reduce(
            (sum, p) => sum + p,
            0
          );
          totalProgress = Math.floor(total / tempFilePaths.length);
          onProgress(totalProgress);
        }
      };
    };

    // 逐个上传图片
    const uploadPromises = tempFilePaths.map((tempFilePath, index) =>
      uploadImage(tempFilePath, createProgressCallback(index))
    );

    const results = await Promise.all(uploadPromises);

    return {
      success: true,
      data: results.map(result => result.data.url)
    };
  } catch (error) {
    throw error;
  }
};

// 预览图片
const previewImage = (current, urls) => {
  uni.previewImage({
    current,
    urls
  });
};

export const uploadApi = {
  getUploadSignature,
  uploadImage,
  uploadImages,
  compressImage,
  previewImage,
  ALLOWED_IMAGE_TYPES,
  MAX_FILE_SIZE,
  isMockMode: useMockService
};
