// src/api/mockService.js
// 为开发环境提供的Mock服务，用于模拟API响应

// 检测当前环境
const isDevMode =
  process.env.NODE_ENV === 'development' ||
  location.hostname === 'localhost' ||
  location.hostname.includes('127.0.0.1');
const isH5 = process.env.UNI_PLATFORM === 'h5' || typeof window !== 'undefined';

// 模拟生成随机字符串
const generateRandomString = (length = 10) => {
  const characters =
    'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * characters.length));
  }
  return result;
};

// 模拟的API响应时间（模拟网络延迟）
const mockResponseDelay = () =>
  new Promise(resolve => setTimeout(resolve, 300 + Math.random() * 700));

// 模拟获取上传签名
export const mockGetUploadSignature = async () => {
  await mockResponseDelay();

  // 生成模拟的签名数据
  const randomKey = `${Date.now()}_${generateRandomString(8)}.jpg`;

  return {
    success: true,
    data: {
      signature: `mock_signature_${generateRandomString(16)}`,
      policy: `mock_policy_${generateRandomString(16)}`,
      key: randomKey,
      host: 'https://mock-image-server.example.com'
    }
  };
};

// 模拟文件上传
export const mockUploadFile = async (filePath, onProgress) => {
  // 创建本地存储的文件URL（用于H5环境下测试）
  let mockImageUrl;

  // 如果是Blob URL（H5环境）
  if (filePath.startsWith('blob:')) {
    try {
      // 尝试从IndexedDB或localStorage获取缓存的图片URL
      const cachedUrls = uni.getStorageSync('mock_uploaded_images') || [];
      if (cachedUrls.includes(filePath)) {
        // 如果已缓存，直接使用
        mockImageUrl = filePath;
      } else {
        // 否则缓存起来，并模拟上传进度
        cachedUrls.push(filePath);
        uni.setStorageSync('mock_uploaded_images', cachedUrls);
        mockImageUrl = filePath;
      }
    } catch (error) {
      console.warn('缓存Mock图片URL失败:', error);
      mockImageUrl = filePath; // 失败时仍使用原始路径
    }
  } else {
    // 对于其他类型路径，直接使用
    mockImageUrl = filePath;
  }

  // 模拟上传进度
  if (onProgress && typeof onProgress === 'function') {
    const totalSteps = 10;
    for (let i = 1; i <= totalSteps; i++) {
      await new Promise(resolve => setTimeout(resolve, 100));
      onProgress(Math.floor((i / totalSteps) * 100));
    }
  }

  await mockResponseDelay();

  // 返回模拟的上传结果
  return {
    success: true,
    data: {
      url: mockImageUrl,
      originalPath: filePath
    }
  };
};

// 辅助函数，判断是否应该使用Mock服务
export const shouldUseMockService = () => {
  return isDevMode && isH5;
};

// 导出Mock服务对象
export const mockService = {
  getUploadSignature: mockGetUploadSignature,
  uploadFile: mockUploadFile,
  shouldUseMockService
};
