# Frontend Architecture Documentation

## Overview

This frontend application is built using **uni-app**, a cross-platform framework based on Vue.js 3 that allows development for multiple platforms including H5, WeChat Mini Program, App, and other mini-programs from a single codebase. The application serves as a wardrobe management system where users can manage their clothing items, create outfits, and organize their fashion collections.

## Technology Stack

- **Framework**: uni-app (Vue.js 3 based)
- **State Management**: Pinia
- **UI Components**: @dcloudio/uni-ui
- **Build Tool**: Vite
- **Language**: JavaScript with TypeScript support
- **Styling**: SCSS/SASS

## Architecture Overview

### uni-app Framework

uni-app is a cross-platform development framework that uses Vue.js syntax and compiles to multiple platforms:

- **H5**: Web browsers
- **Mini Programs**: WeChat, Alipay, Baidu, etc.
- **Native Apps**: iOS and Android
- **Quick Apps**: Various quick app platforms

The framework provides:
- Unified API across platforms
- Platform-specific optimizations
- Built-in components and APIs
- Conditional compilation for platform-specific code

### Project Structure

```
src/
├── api/                    # API service layer
├── components/             # Reusable Vue components
│   ├── common/            # Common components used across pages
│   └── ...
├── constants/             # Application constants and configurations
├── docs/                  # Documentation files
├── mocks/                 # Mock data and MSW handlers
├── pages/                 # Application pages (routes)
│   ├── auth/             # Authentication pages
│   ├── main/             # Main application pages
│   ├── user/             # User profile pages
│   └── wardrobe/         # Wardrobe management pages
├── static/               # Static assets
├── stores/               # Pinia state management stores
├── utils/                # Utility functions
├── App.vue               # Root application component
├── main.js               # Application entry point
├── pages.json            # Page routing and configuration
└── manifest.json         # Application manifest
```

## Page Structure and Routing

The application's page structure is defined in `pages.json`, which serves as the routing configuration for uni-app. This file defines:

### Page Registration
All pages must be registered in the `pages` array with their paths and navigation bar configurations:

```json
{
  "pages": [
    {
      "path": "pages/main/home/<USER>",
      "style": {
        "navigationBarTitleText": "我的衣柜"
      }
    }
  ]
}
```

### Tab Bar Configuration
The application uses a bottom tab bar with three main sections:
- **首页 (Home)**: Main dashboard and overview
- **衣橱 (Wardrobe)**: Clothing item management
- **我的 (Profile)**: User profile and settings

### Global Styles
Global navigation bar styling and app-wide theme configurations are defined in the `globalStyle` section.

### Page Categories

1. **Authentication Pages** (`pages/auth/`)
   - Login, registration, and password recovery

2. **Main Application Pages** (`pages/main/`)
   - Home dashboard
   - Add new clothing items
   - Create outfits
   - Collections management

3. **Wardrobe Pages** (`pages/wardrobe/`)
   - Clothing item listing and details
   - Item management

4. **User Pages** (`pages/user/`)
   - Profile management

5. **Outfit Pages** (`pages/outfits/`)
   - Outfit creation and management

6. **Calendar Pages** (`pages/calendar/`)
   - Outfit planning and calendar view

## Component Model

### Component Structure
Vue components in this application follow the standard Vue 3 Composition API pattern with uni-app specific features:

- **Template**: Uses uni-app components (e.g., `<view>`, `<text>`, `<image>`)
- **Script**: Vue 3 Composition API with uni-app lifecycle hooks
- **Style**: SCSS with platform-specific styling support

### Component Categories

1. **Common Components** (`components/common/`)
   - Reusable components used across multiple pages
   - Examples: ImageUploader, form components

2. **Page-Specific Components** (`pages/*/components/`)
   - Components specific to particular pages or features
   - Examples: ClothingSelector, SharePanel

### uni-app Specific Features
- **Conditional Compilation**: Platform-specific code using `#ifdef` directives
- **uni-app APIs**: Platform-unified APIs for device features
- **Built-in Components**: Cross-platform UI components

## State Management with Pinia

The application uses **Pinia** as the state management solution, providing a modern, TypeScript-friendly alternative to Vuex.

### Store Architecture

All stores are located in the `stores/` directory and follow a consistent pattern:

```javascript
import { defineStore } from 'pinia';

export const useExampleStore = defineStore('example', {
  state: () => ({
    // State properties
  }),
  getters: {
    // Computed properties
  },
  actions: {
    // Methods for state mutations and async operations
  }
});
```

### Available Stores

1. **clothingStore.js** - Manages clothing items, filtering, and CRUD operations
2. **outfitStore.js** - Handles outfit creation, management, and favorites
3. **userStore.js** - User authentication and profile data
4. **wardrobeStore.js** - Wardrobe-specific state and operations
5. **systemStore.js** - Application-wide system state
6. **calendarStore.js** - Calendar and scheduling functionality
7. **collectionStore.js** - Collection management

### Store Features

- **Reactive State**: Automatic reactivity with Vue components
- **Async Actions**: Built-in support for async operations
- **Error Handling**: Consistent error handling patterns
- **Loading States**: Built-in loading state management
- **Data Persistence**: Integration with uni-app storage APIs

### State Management Patterns

1. **Loading States**: Each store manages its own loading state for async operations
2. **Error Handling**: Centralized error handling with user-friendly messages
3. **Data Normalization**: Consistent data structure across stores
4. **Optimistic Updates**: UI updates before API confirmation where appropriate

## API Integration

The `api/` directory contains service modules that handle communication with the backend:

- **Modular Structure**: Separate files for different API domains
- **Consistent Interface**: Standardized request/response handling
- **Error Handling**: Centralized error processing
- **Mock Support**: Integration with MSW for development and testing

## Development Workflow

### Running the Application

```bash
# Development for H5 (web)
npm run dev:h5

# Development for WeChat Mini Program
npm run dev:mp-weixin

# Development for App
npm run dev:app
```

### Building for Production

```bash
# Build for H5
npm run build:h5

# Build for WeChat Mini Program
npm run build:mp-weixin

# Build for App
npm run build:app
```

### Code Quality

- **ESLint**: Code linting with Vue and TypeScript support
- **Prettier**: Code formatting
- **TypeScript**: Type checking support

## Platform Considerations

### Cross-Platform Development
- Use uni-app components instead of HTML elements
- Utilize uni-app APIs for platform-specific functionality
- Test on multiple platforms during development

### Performance Optimization
- Lazy loading for pages and components
- Image optimization for different screen densities
- Efficient state management to minimize re-renders

### Platform-Specific Features
- Conditional compilation for platform-specific code
- Platform-specific styling and layouts
- Native API integration where needed

## Best Practices

1. **Component Design**: Keep components focused and reusable
2. **State Management**: Use appropriate stores for different data domains
3. **API Calls**: Handle loading and error states consistently
4. **Styling**: Use SCSS variables for consistent theming
5. **Performance**: Optimize images and minimize bundle size
6. **Testing**: Write unit tests for critical business logic
7. **Documentation**: Document complex components and business logic