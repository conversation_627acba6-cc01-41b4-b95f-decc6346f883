# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

"我的虚拟衣橱" (My Virtual Wardrobe) is a uni-app project built with Vue 3 + Composition API for cross-platform deployment, primarily targeting WeChat Mini Program. It's a fashion management app for organizing clothing items, creating outfits, and managing wardrobes.

## Development Commands

### Primary Development Commands
```bash
# Development - WeChat Mini Program (primary target)
npm run dev:mp-weixin

# Development - H5 (web browser)
npm run dev:h5

# Development - other platforms
npm run dev:app          # Native app
npm run dev:mp-alipay    # Alipay Mini Program
```

### Build Commands
```bash
# Production builds
npm run build:mp-weixin  # WeChat Mini Program
npm run build:h5         # H5 web
npm run build:app        # Native app
```

### Code Quality Commands
```bash
# Type checking
npm run type-check

# Linting
npm run lint
npm run lint:fix

# Code formatting
npm run format
npm run format:check
```

## Architecture & Structure

### Framework Stack
- **Framework**: Uni-app (Vue 3 + Composition API)
- **State Management**: Pinia stores in `/src/stores/`
- **Build Tool**: Vite with uni-app plugin
- **UI Components**: Uni-app built-in components + @dcloudio/uni-ui
- **Styling**: SCSS with rpx units for cross-device compatibility

### Core Directories
- `/src/pages/` - Page components organized by feature (auth, main, wardrobe, user, etc.)
- `/src/stores/` - Pinia stores for global state management
- `/src/api/` - API layer with service modules and HTTP utilities
- `/src/components/` - Reusable Vue components
- `/src/constants/` - Application constants (options, strings, error messages)
- `/src/utils/` - Utility functions

### State Management Pattern
All stores follow consistent patterns:
- **State**: Reactive data with loading/error states
- **Getters**: Computed properties for derived state
- **Actions**: Async operations with proper error handling

Key stores:
- `wardrobeStore.js` - Clothing items management with pagination
- `outfitStore.js` - Outfit creation and management
- `userStore.js` - User authentication and profile
- `collectionStore.js` - Favorites/collections management

### API Architecture
- Base URL: `https://bfqvozkeojzd.sealoshzh.site/api`
- HTTP client in `/src/api/http.js` with unified error handling
- Service modules: `auth.js`, `clothing.js`, `outfit.js`, `collection.js`, etc.
- Development-friendly error handling with 404 detection

### Navigation Structure
Three-tab bottom navigation:
- **首页** (Home): Dashboard with quick actions
- **衣橱** (Wardrobe): Clothing item management
- **我的** (Profile): User profile and settings

### Component Naming & Conventions
- **Vue Components**: PascalCase (e.g., `ClothingCard.vue`)
- **Pages**: Descriptive names ending with "Page" (e.g., `CreateOutfitPage.vue`)
- **Store files**: camelCase with "Store" suffix (e.g., `wardrobeStore.js`)
- **CSS classes**: kebab-case or BEM methodology
- **Variables/Functions**: camelCase

### Key Configuration Files
- `pages.json` - Page routing, navigation, and tabBar configuration
- `manifest.json` - Platform-specific configurations and permissions
- `project.config.json` - WeChat Mini Program settings (appid: wx0c1ee5de2d374706)

## Development Status

### Current State
- Frontend UI/UX largely complete with mock data
- API structure defined but using simulated responses
- Backend endpoints exist but not fully integrated
- Core features implemented: authentication, wardrobe management, outfit creation, collections

### Known Limitations
- Most data operations use mock/simulated APIs
- Backend integration incomplete (some endpoints may return 404 in development)
- Data persistence limited without full backend connection
- Calendar and advanced filtering features marked as "coming soon"

### Code Quality Standards
- ESLint with Vue 3 rules and Prettier integration
- TypeScript support available for critical modules
- 2-space indentation, single quotes, no semicolons (Prettier config)
- uni-app globals configured: `uni`, `plus`, `getCurrentPages`, `getApp`

## Critical Development Notes

### Uni-app Specific Requirements
- **Use uni-app components**: `<view>`, `<text>`, `<image>`, `<button>`, etc.
- **Use uni-app APIs**: `uni.request`, `uni.navigateTo`, `uni.showToast`, etc.
- **Responsive units**: Use `rpx` for cross-device compatibility
- **Lifecycle hooks**: Use uni-app specific hooks (`onLoad`, `onShow`, `onReady`)

### WeChat Mini Program Constraints
- Strict content security policies
- File size limitations
- API usage restrictions
- Must follow WeChat development guidelines

### Error Handling Pattern
All API calls should include:
```javascript
try {
  const response = await apiCall();
  // handle success
} catch (error) {
  console.error('API Error:', error);
  uni.showToast({
    title: error.message || '操作失败',
    icon: 'none'
  });
}
```

### Development vs Production
- Development environment has enhanced error logging
- 404 errors in development environment are logged as warnings
- Production builds remove console.log statements
- Token-based authentication with localStorage persistence

## Special Considerations

### Image Upload
- Implemented image upload functionality in `/src/api/upload.js`
- Uses uni-app's `uni.chooseImage` and `uni.uploadFile`
- ImageUploader component available in `/src/components/common/`

### Color System & Outfit Matching
- Sophisticated color harmony system in `wardrobeStore.js`
- Occasion compatibility checking for outfit suggestions
- Category system with subcategories and types

### Page Communication
- Uses uni-app's page stack manipulation for data passing
- `getCurrentPages()` for accessing previous page instances
- `defineExpose` for exposing methods to other pages

### Migration Planning
- Backend migration to Cloudflare Workers planned
- Current MongoDB structure designed for easy migration
- API endpoints structured for serverless deployment