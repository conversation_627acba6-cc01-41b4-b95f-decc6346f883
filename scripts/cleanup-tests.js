#!/usr/bin/env node

/**
 * 测试清理脚本
 * 该脚本将删除所有测试文件、目录和 package.json 中的相关依赖
 */

const fs = require('fs');
const path = require('path');

// 需要删除的测试目录和文件
const testPaths = [
  'tests',
  'src/**/__tests__',
  'src/components/__tests__',
  'src/components/common/__tests__',
  'src/stores/__tests__',
  'src/utils/__tests__',
  'vitest.config.js',
  'tests/setup.js',
  'tests/utils'
];

// 需要从 package.json 中移除的依赖
const testDependencies = [
  '@vitest/ui',
  '@vue/test-utils',
  'happy-dom',
  'playwright',
  'vitest'
];

// 需要从 package.json 中移除的脚本
const testScripts = [
  'test',
  'test:run',
  'test:ui',
  'test:coverage',
  'test:e2e',
  'test:e2e:run'
];

/**
 * 递归删除目录
 * @param {string} dirPath - 目录路径
 */
function removeDirectory(dirPath) {
  if (fs.existsSync(dirPath)) {
    console.log(`删除目录: ${dirPath}`);
    fs.rmSync(dirPath, { recursive: true, force: true });
  }
}

/**
 * 删除文件
 * @param {string} filePath - 文件路径
 */
function removeFile(filePath) {
  if (fs.existsSync(filePath)) {
    console.log(`删除文件: ${filePath}`);
    fs.unlinkSync(filePath);
  }
}

/**
 * 递归查找并删除匹配的目录
 * @param {string} basePath - 基础路径
 * @param {string} pattern - 匹配模式
 */
function removeMatchingDirectories(basePath, pattern) {
  if (!fs.existsSync(basePath)) return;

  const items = fs.readdirSync(basePath);
  
  for (const item of items) {
    const itemPath = path.join(basePath, item);
    const stat = fs.statSync(itemPath);
    
    if (stat.isDirectory()) {
      if (item === pattern) {
        removeDirectory(itemPath);
      } else {
        // 递归搜索子目录
        removeMatchingDirectories(itemPath, pattern);
      }
    }
  }
}

/**
 * 更新 package.json，移除测试相关的依赖和脚本
 */
function updatePackageJson() {
  const packageJsonPath = path.join(process.cwd(), 'package.json');
  
  if (!fs.existsSync(packageJsonPath)) {
    console.log('package.json 文件不存在');
    return;
  }

  console.log('更新 package.json...');
  
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // 移除测试相关的依赖
  if (packageJson.devDependencies) {
    testDependencies.forEach(dep => {
      if (packageJson.devDependencies[dep]) {
        console.log(`移除依赖: ${dep}`);
        delete packageJson.devDependencies[dep];
      }
    });
  }
  
  // 移除测试相关的脚本
  if (packageJson.scripts) {
    testScripts.forEach(script => {
      if (packageJson.scripts[script]) {
        console.log(`移除脚本: ${script}`);
        delete packageJson.scripts[script];
      }
    });
  }
  
  // 写回 package.json
  fs.writeFileSync(packageJsonPath, JSON.stringify(packageJson, null, 2) + '\n');
  console.log('package.json 更新完成');
}

/**
 * 主清理函数
 */
function cleanup() {
  console.log('开始清理测试文件和依赖...\n');
  
  // 删除测试目录
  removeDirectory('tests');
  
  // 删除 src 目录下的 __tests__ 目录
  removeMatchingDirectories('src', '__tests__');
  
  // 删除测试配置文件
  removeFile('vitest.config.js');
  
  // 更新 package.json
  updatePackageJson();
  
  console.log('\n测试清理完成！');
  console.log('建议运行 npm install 来清理 node_modules 中的测试依赖');
}

/**
 * 确认清理操作
 */
function confirmCleanup() {
  const readline = require('readline');
  const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
  });

  console.log('⚠️  警告：此操作将删除所有测试文件和相关依赖！');
  console.log('将要删除的内容：');
  console.log('- tests/ 目录');
  console.log('- src/**/__tests__/ 目录');
  console.log('- vitest.config.js 文件');
  console.log('- package.json 中的测试依赖和脚本');
  console.log('');

  rl.question('确定要继续吗？(y/N): ', (answer) => {
    if (answer.toLowerCase() === 'y' || answer.toLowerCase() === 'yes') {
      cleanup();
    } else {
      console.log('操作已取消');
    }
    rl.close();
  });
}

// 检查是否有 --force 参数
const forceMode = process.argv.includes('--force');

if (forceMode) {
  cleanup();
} else {
  confirmCleanup();
}