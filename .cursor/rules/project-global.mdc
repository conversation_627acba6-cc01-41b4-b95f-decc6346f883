---
description: 
globs: 
alwaysApply: true
---
alwaysApply: true description: "衣柜小程序全局编码规范和AI行为准则 (Uni-app for WeChat Mini Program)。"
"衣柜"小程序全局编码标准 (Uni-app for WeChat Mini Program)
1. 语言与框架
本项目使用 Uni-app 框架，基于 Vue 3 和 Composition API 进行开发，主要目标平台为微信小程序。
所有新的 Vue 组件应优先使用 <script setup> 语法。
主要编程语言为 JavaScript (ES6+)。在状态管理、API接口定义等关键模块可使用 TypeScript 以增强类型安全。
2. 编码风格与格式化
命名约定:
Vue组件 (SFCs): PascalCase (例如, ClothingCard.vue)
JavaScript/TypeScript 变量和函数: camelCase (例如, fetchWardrobeItems)
Pinia store 文件名: camelCaseStore.js (例如, wardrobeStore.js)
CSS 类名: kebab-case (例如, .clothing-card-title) 或 BEM。
格式化:
遵循 Prettier 的默认配置 (或项目自定义配置)。
使用2个空格进行缩进。
注释:
为复杂逻辑编写清晰、简洁的注释。
函数头部注释尽量采用 JSDoc 风格。
文件路径/名称作为单行注释置于文件顶部 (例如: // src/components/ClothingCard.vue)。
3. Uni-app for WeChat Mini Program 核心指南
组件使用:
优先使用 Uni-app 提供的跨端组件 (如 <view>, <text>, <image>, <button>, <scroll-view>, <swiper>, <uni-forms>, <uni-easyinput>, <uni-data-checkbox>, <uni-list> 等)。
避免直接在 .vue 文件中编写微信原生组件语法 (WXML)。
API 调用:
优先使用 Uni-app 提供的跨端 API (如 uni.request, uni.navigateTo, uni.login, uni.chooseImage, uni.uploadFile, uni.showToast 等)。
避免直接调用微信原生 API (wx.*)，除非Uni-app未封装或有特殊需求，并添加注释说明。
样式与单位:
推荐使用 rpx 作为主要的尺寸单位，以确保跨设备兼容性。
可以使用 scss 等预处理器，并善用 uni.scss。
生命周期钩子:
应用生命周期: 在 App.vue 中使用 onLaunch, onShow, onHide。
页面生命周期: 在页面 .vue 中使用 onLoad, onShow, onReady, onPullDownRefresh, onReachBottom 等。
配置文件:
pages.json: 严格管理页面路由、导航栏样式、tabBar、分包。
manifest.json:
必须正确填写微信小程序的 appid (mp-weixin -> appid)。
在 mp-weixin -> permission 中声明权限并提供用途说明。
在 mp-weixin -> requiredPrivateInfos 中声明敏感接口并提供用途说明。
4. 错误处理
对所有 uni.request 等异步操作实施健壮的错误处理。
使用 uni.showToast 或 uni.showModal 向用户展示友好的错误提示。
5. 状态管理 (Pinia)
使用 Pinia 进行全局状态管理，Store 文件定义在 /src/stores。
Actions 中进行状态修改。
6. API 调用 (腾讯云CloudBase云函数)
封装在 Service 层 (例如, /src/api/clothing.js)。
使用 uni.request 或腾讯云SDK调用云函数。
7. AI 行为通用准则
对不完整代码使用 TODO: 注释。
专注于提供正确代码，而非道歉。
优先考虑代码可读性和可维护性。
确保包含所有必要导入。
遵循开发者明确指示。
生成的代码应直接适配 Uni-app 编译到微信小程序的环境。


