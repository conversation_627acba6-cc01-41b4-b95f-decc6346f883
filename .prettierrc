{"semi": true, "singleQuote": true, "quoteProps": "as-needed", "trailingComma": "none", "bracketSpacing": true, "bracketSameLine": false, "arrowParens": "avoid", "printWidth": 80, "tabWidth": 2, "useTabs": false, "endOfLine": "lf", "htmlWhitespaceSensitivity": "css", "vueIndentScriptAndStyle": false, "overrides": [{"files": "*.vue", "options": {"parser": "vue"}}, {"files": "*.json", "options": {"parser": "json", "trailingComma": "none"}}, {"files": "*.md", "options": {"parser": "markdown", "printWidth": 100, "proseWrap": "preserve"}}]}