# 项目清理指南

本指南旨在帮助您清理项目目录中不再需要的临时文件、备份文件和构建产物，以保持项目结构的整洁。

**注意：** 在执行任何删除操作前，请确保您了解这些文件的用途。如果不确定，建议先进行备份。

---

### 1. macOS 元数据文件

这些是以 `._` 开头的文件，由 macOS 在将项目复制到某些文件系统（如 FAT32）时自动生成。它们存储了 Mac 特定的元数据，对于项目代码本身没有实际作用，可以安全删除。

**建议删除的文件列表（部分示例）：**
```
./._CONTRIBUTING.md
./._DOCUMENTATION_REVIEW_SUMMARY.md
./._favicon.ico
./._package-lock.json
./._package.json
./._README.md
./public/._favicon.ico
./scripts/._cleanup-tests.js
./src/._README.md
./yigui-backend/._README.md
./yigui-backend/._schema.sql
... (以及其他所有以 ._ 开头的文件)
```

---

### 2. 备份文件

这些是在某些操作（例如，`npm install`）后可能生成的配置文件备份。在确认当前配置无误后，这些备份文件即可删除。

**建议删除的文件：**
```
./package.json.backup
```

---

### 3. 临时测试文件

这些是在开发或测试过程中创建的临时文件，通常用于快速验证某些功能，之后便不再需要。

**建议删除的文件：**
```
./yigui-backend/test.txt
```

---

### 4. 测试报告目录

这个目录包含了自动化测试（如端到端测试）生成的报告。这些报告用于分析测试结果，一旦分析完成，就可以删除，以便在下次运行时生成新的报告。

**建议删除的目录：**
```
./test-reports/