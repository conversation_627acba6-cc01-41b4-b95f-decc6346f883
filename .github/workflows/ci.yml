name: CI

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    runs-on: ubuntu-latest
    
    strategy:
      matrix:
        node-version: [18.x, 20.x]
    
    steps:
    - uses: actions/checkout@v4
    
    - name: Use Node.js ${{ matrix.node-version }}
      uses: actions/setup-node@v4
      with:
        node-version: ${{ matrix.node-version }}
        cache: 'npm'
    
    - name: Install dependencies
      run: npm ci
    
    - name: Run unit and integration tests
      run: npm run test:run
    
    - name: Run E2E tests
      run: npm run test:e2e:run
    
    - name: Run tests with coverage
      run: npm run test:coverage
    
    - name: Upload coverage reports to Codecov
      uses: codecov/codecov-action@v3
      with:
        token: ${{ secrets.CODECOV_TOKEN }}
        files: ./coverage/lcov.info
        fail_ci_if_error: false
    
    - name: Upload coverage to GitHub
      uses: actions/upload-artifact@v4
      with:
        name: coverage-report-${{ matrix.node-version }}
        path: coverage/
        retention-days: 30