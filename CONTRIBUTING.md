# Contributing to Yigui Virtual Wardrobe

Thank you for your interest in contributing to Yigui Virtual Wardrobe! This document provides guidelines and information for contributors to help maintain code quality and ensure a smooth development process.

## Table of Contents

- [Code of Conduct](#code-of-conduct)
- [Getting Started](#getting-started)
- [Development Workflow](#development-workflow)
- [Branching Strategy](#branching-strategy)
- [Pull Request Process](#pull-request-process)
- [Code Review Guidelines](#code-review-guidelines)
- [Coding Standards](#coding-standards)
- [Testing Requirements](#testing-requirements)
- [Documentation Guidelines](#documentation-guidelines)

## Code of Conduct

By participating in this project, you agree to abide by our code of conduct:

- Be respectful and inclusive in all interactions
- Focus on constructive feedback and collaboration
- Help maintain a welcoming environment for all contributors
- Report any unacceptable behavior to the project maintainers

## Getting Started

### Prerequisites

Before contributing, ensure you have:

- Node.js 16.x or higher installed
- npm or yarn package manager
- Git configured with your name and email
- HBuilderX (recommended) or Vite for uni-app development
- Cloudflare CLI (Wrangler) for backend development

### Setting Up Your Development Environment

1. **Fork the repository** on GitHub
2. **Clone your fork** locally:
   ```bash
   git clone https://github.com/your-username/yigui-virtual-wardrobe.git
   cd yigui-virtual-wardrobe
   ```
3. **Add the upstream remote**:
   ```bash
   git remote add upstream https://github.com/original-owner/yigui-virtual-wardrobe.git
   ```
4. **Install dependencies**:
   ```bash
   # Frontend dependencies
   npm install
   
   # Backend dependencies
   cd yigui-backend
   npm install
   ```

## Development Workflow

### Before Starting Work

1. **Sync with upstream**:
   ```bash
   git checkout main
   git pull upstream main
   git push origin main
   ```

2. **Create a feature branch**:
   ```bash
   git checkout -b feature/your-feature-name
   # or
   git checkout -b fix/issue-description
   ```

### During Development

1. **Make small, focused commits** with clear messages
2. **Test your changes** thoroughly on multiple platforms if applicable
3. **Follow the coding standards** outlined below
4. **Update documentation** as needed

### Submitting Changes

1. **Push your branch** to your fork:
   ```bash
   git push origin feature/your-feature-name
   ```

2. **Create a pull request** on GitHub with a clear description

## Branching Strategy

We use a simplified Git flow with the following branch types:

### Main Branches

- **`main`**: Production-ready code. All releases are tagged from this branch.
- **`develop`**: Integration branch for features. Used for staging deployments.

### Supporting Branches

- **`feature/*`**: New features or enhancements
  - Branch from: `develop`
  - Merge to: `develop`
  - Naming: `feature/short-description`

- **`fix/*`**: Bug fixes
  - Branch from: `develop` (or `main` for hotfixes)
  - Merge to: `develop` (and `main` for hotfixes)
  - Naming: `fix/issue-description`

- **`hotfix/*`**: Critical production fixes
  - Branch from: `main`
  - Merge to: `main` and `develop`
  - Naming: `hotfix/critical-issue`

### Branch Naming Conventions

- Use lowercase letters and hyphens
- Be descriptive but concise
- Include issue numbers when applicable: `feature/123-user-authentication`

## Pull Request Process

### Before Submitting

1. **Ensure your branch is up to date** with the target branch
2. **Run all tests** and ensure they pass
3. **Run linting** and fix any issues:
   ```bash
   npm run lint:fix
   npm run format
   ```
4. **Test on multiple platforms** if your changes affect the frontend
5. **Update documentation** if needed

### Pull Request Requirements

Your pull request must include:

1. **Clear title and description** explaining the changes
2. **Reference to related issues** (e.g., "Closes #123")
3. **Screenshots or demos** for UI changes
4. **Testing instructions** for reviewers
5. **Checklist completion** (see template)

### Pull Request Template

When creating a pull request, use this template:

```markdown
## Description
Brief description of changes made.

## Type of Change
- [ ] Bug fix (non-breaking change that fixes an issue)
- [ ] New feature (non-breaking change that adds functionality)
- [ ] Breaking change (fix or feature that would cause existing functionality to not work as expected)
- [ ] Documentation update

## Related Issues
Closes #(issue number)

## Testing
- [ ] Tests pass locally
- [ ] Code follows style guidelines
- [ ] Self-review completed
- [ ] Documentation updated

## Screenshots (if applicable)
Add screenshots or GIFs demonstrating the changes.

## Additional Notes
Any additional information for reviewers.
```

## Code Review Guidelines

### For Authors

- **Respond promptly** to review feedback
- **Be open to suggestions** and constructive criticism
- **Make requested changes** in separate commits for easy tracking
- **Resolve conversations** after addressing feedback

### For Reviewers

- **Be constructive and respectful** in feedback
- **Focus on code quality, not personal preferences**
- **Check for**:
  - Code functionality and logic
  - Performance implications
  - Security considerations
  - Code style compliance
  - Test coverage
  - Documentation updates

### Review Checklist

- [ ] Code follows project coding standards
- [ ] Changes are well-tested
- [ ] Documentation is updated
- [ ] No breaking changes without proper justification
- [ ] Performance impact is acceptable
- [ ] Security best practices are followed

## Coding Standards

### ESLint Configuration

This project uses ESLint for code quality enforcement. The configuration is defined in `.eslintrc.js` and includes:

- **Vue 3 recommended rules** for component development
- **Prettier integration** for consistent formatting
- **Custom rules** for project-specific requirements

### Key Coding Guidelines

#### General JavaScript/TypeScript

- **Use ES6+ features**: arrow functions, destructuring, template literals
- **Prefer `const`** over `let`, avoid `var`
- **Use meaningful variable names** that describe their purpose
- **Keep functions small** and focused on a single responsibility
- **Add JSDoc comments** for complex functions

```javascript
// Good
const getUserById = async (userId) => {
  // Implementation
};

// Bad
var getUser = function(id) {
  // Implementation
};
```

#### Vue.js Components

- **Use Composition API** for new components
- **Follow Vue 3 style guide** conventions
- **Use TypeScript** for type safety where applicable
- **Keep components small** and focused
- **Use proper prop validation**

```vue
<script setup>
import { ref, computed } from 'vue'

// Props with validation
const props = defineProps({
  title: {
    type: String,
    required: true
  },
  items: {
    type: Array,
    default: () => []
  }
})

// Reactive data
const isLoading = ref(false)

// Computed properties
const itemCount = computed(() => props.items.length)
</script>
```

#### uni-app Specific

- **Use uni-app APIs** instead of web APIs when available
- **Test on multiple platforms** (H5, WeChat, App)
- **Follow uni-app component naming** conventions
- **Use conditional compilation** for platform-specific code

```javascript
// Platform-specific code
// #ifdef H5
console.log('Running on H5')
// #endif

// #ifdef MP-WEIXIN
console.log('Running on WeChat Mini Program')
// #endif
```

### File Organization

- **Group related files** in appropriate directories
- **Use consistent naming** conventions
- **Keep file sizes reasonable** (< 300 lines when possible)
- **Separate concerns** (components, stores, utilities)

### Import/Export Guidelines

- **Use named exports** when exporting multiple items
- **Group imports** logically (external libraries, internal modules)
- **Use absolute imports** for src/ directory

```javascript
// External imports first
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'

// Internal imports
import { apiClient } from '@/api/http'
import { formatDate } from '@/utils/date'
```

## Testing Requirements

### Frontend Testing

- **Write unit tests** for utility functions and complex components
- **Test user interactions** and component behavior
- **Mock external dependencies** appropriately
- **Maintain good test coverage** (aim for >80%)

### Backend Testing

- **Write tests for all API endpoints**
- **Test error handling** and edge cases
- **Use Vitest** for testing framework
- **Mock external services** (database, third-party APIs)

### Running Tests

```bash
# Frontend tests (when implemented)
npm run test

# Backend tests
cd yigui-backend
npm run test

# Watch mode for development
npm run test:watch
```

## Documentation Guidelines

### Code Documentation

- **Add JSDoc comments** for public functions and complex logic
- **Document component props** and events
- **Explain non-obvious code** with inline comments
- **Keep comments up to date** with code changes

### README Updates

- **Update setup instructions** if dependencies change
- **Document new features** and configuration options
- **Keep troubleshooting section** current
- **Update version requirements** as needed

### API Documentation

- **Document all endpoints** in the backend API documentation
- **Include request/response examples**
- **Document error codes** and messages
- **Keep API docs synchronized** with implementation

## Getting Help

If you need help or have questions:

1. **Check existing documentation** and issues first
2. **Search closed issues** for similar problems
3. **Create a new issue** with detailed information
4. **Join project discussions** for general questions
5. **Contact maintainers** for urgent matters

## Recognition

Contributors will be recognized in:

- **CONTRIBUTORS.md** file (if created)
- **Release notes** for significant contributions
- **Project documentation** for major features

Thank you for contributing to Yigui Virtual Wardrobe! Your efforts help make this project better for everyone.