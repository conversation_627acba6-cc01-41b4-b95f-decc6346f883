{"name": "uni-preset-vue-vite", "version": "0.0.0", "scripts": {"dev:app": "uni -p app", "dev:app-android": "uni -p app-android", "dev:app-ios": "uni -p app-ios", "dev:custom": "uni -p", "dev:h5": "uni", "dev:h5:ssr": "uni --ssr", "dev:mp-alipay": "uni -p mp-alipay", "dev:mp-baidu": "uni -p mp-baidu", "dev:mp-kuaishou": "uni -p mp-kua<PERSON>ou", "dev:mp-lark": "uni -p mp-lark", "dev:mp-qq": "uni -p mp-qq", "dev:mp-toutiao": "uni -p mp-to<PERSON><PERSON>", "dev:mp-weixin": "uni -p mp-weixin", "dev:quickapp-webview": "uni -p quickapp-webview", "dev:quickapp-webview-huawei": "uni -p quickapp-webview-huawei", "dev:quickapp-webview-union": "uni -p quickapp-webview-union", "build:app": "uni build -p app", "build:app-android": "uni build -p app-android", "build:app-ios": "uni build -p app-ios", "build:custom": "uni build -p", "build:h5": "uni build", "build:h5:ssr": "uni build --ssr", "build:mp-alipay": "uni build -p mp-alipay", "build:mp-baidu": "uni build -p mp-baidu", "build:mp-kuaishou": "uni build -p mp-kuaishou", "build:mp-lark": "uni build -p mp-lark", "build:mp-qq": "uni build -p mp-qq", "build:mp-toutiao": "uni build -p mp-to<PERSON>ao", "build:mp-weixin": "uni build -p mp-weixin", "build:quickapp-webview": "uni build -p quickapp-webview", "build:quickapp-webview-huawei": "uni build -p quickapp-webview-huawei", "build:quickapp-webview-union": "uni build -p quickapp-webview-union", "test": "vitest --run", "test:watch": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "test:e2e": "node src/test/e2e/run-e2e-tests.js all", "test:e2e:basic": "node src/test/e2e/run-e2e-tests.js basic", "test:e2e:workflow": "node src/test/e2e/run-e2e-tests.js workflow", "test:e2e:performance": "node src/test/e2e/run-e2e-tests.js performance", "type-check": "vue-tsc --noEmit", "lint": "eslint src/ --ext .vue,.js,.ts", "lint:fix": "eslint src/ --ext .vue,.js,.ts --fix", "format": "prettier --write \"src/**/*.{vue,js,ts,json,md}\"", "format:check": "prettier --check \"src/**/*.{vue,js,ts,json,md}\""}, "dependencies": {"@dcloudio/uni-app": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-app-plus": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-components": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-h5": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-alipay": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-baidu": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-jd": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-kuaishou": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-lark": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-qq": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-toutiao": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-vue": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-mp-weixin": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-quickapp-webview": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-ui": "^1.4.28", "lodash-es": "^4.17.21", "pinia": "^2.1.7", "vue": "^3.2.45", "vue-i18n": "^9.6.5"}, "devDependencies": {"@dcloudio/types": "^3.3.2", "@dcloudio/uni-automator": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-cli-shared": "^3.0.0-alpha-4020220917001", "@dcloudio/uni-stacktracey": "^3.0.0-alpha-4020220917001", "@dcloudio/vite-plugin-uni": "^3.0.0-alpha-4020220917001", "@types/node": "^20.10.5", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@vue/eslint-config-typescript": "^12.0.0", "@vue/test-utils": "^2.4.6", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.19.2", "prettier": "^3.2.5", "sass": "^1.69.5", "typescript": "^5.3.3", "vite": "^4.5.1", "vitest": "^3.2.4", "vue-tsc": "^1.8.27"}}