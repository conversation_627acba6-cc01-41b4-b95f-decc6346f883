# Yigui Virtual Wardrobe (我的虚拟衣橱)

A comprehensive virtual wardrobe management application that helps users organize their clothing items, create stylish outfits, and manage their fashion collections. Built with uni-app for cross-platform compatibility and powered by Cloudflare Workers for the backend.

## Project Overview

Yigui Virtual Wardrobe is a full-stack application consisting of:
- **Frontend**: Cross-platform mobile app built with uni-app (Vue 3)
- **Backend**: Serverless API powered by Cloudflare Workers
- **Database**: Cloudflare D1 (SQLite) for structured data storage
- **File Storage**: Cloudflare R2 for image uploads

### Key Features

- **User Authentication**: Secure login, registration, and password recovery
- **Wardrobe Management**: Add, view, edit, and organize clothing items
- **Outfit Creation**: Create and manage outfit combinations
- **Collections**: Organize items into custom collections
- **Calendar Integration**: Plan outfits for specific dates
- **Image Upload**: Store and manage clothing photos
- **Cross-Platform**: Runs on iOS, Android, H5, and various mini-programs

## Prerequisites

Before running this project, ensure you have the following installed:

- **Node.js**: Version 16.x or higher
- **npm** or **yarn**: Package manager
- **HBuilderX** (recommended for uni-app development) or **Vite**
- **Cloudflare CLI (Wrangler)**: For backend development and deployment

### Node.js Version Requirement

This project requires Node.js 16.x or higher due to:
- Vue 3 and Composition API dependencies
- Modern JavaScript features used in the codebase
- Cloudflare Workers runtime compatibility

## Environment Setup

### 1. Clone the Repository

```bash
git clone <repository-url>
cd yigui-virtual-wardrobe
```

### 2. Install Frontend Dependencies

```bash
# Install frontend dependencies
npm install
```

### 3. Install Backend Dependencies

```bash
# Navigate to backend directory
cd yigui-backend

# Install backend dependencies
npm install
```

### 4. Environment Configuration

Create necessary environment files:

**Frontend Configuration:**
- Copy `project.config.json` to `project.private.config.json` if needed
- Update API endpoints in `src/api/http.js` if running locally

**Backend Configuration:**
- Set up Cloudflare Workers environment variables
- Configure D1 database bindings
- Set up R2 storage bucket bindings

## Running the Project Locally

### Frontend Development

The frontend supports multiple platforms. Choose the appropriate command:

```bash
# Web/H5 development
npm run dev:h5

# WeChat Mini Program
npm run dev:mp-weixin

# Alipay Mini Program  
npm run dev:mp-alipay

# Android App
npm run dev:app-android

# iOS App
npm run dev:app-ios

# All available platforms
npm run dev:custom
```

**Using HBuilderX (Recommended):**
1. Open HBuilderX
2. Import the project folder
3. Select target platform from the toolbar
4. Click "Run" to start development server

### Backend Development

```bash
# Navigate to backend directory
cd yigui-backend

# Start local development server
npm run dev

# The API will be available at http://localhost:8787
```

### Building for Production

**Frontend:**
```bash
# Build for specific platform
npm run build:h5
npm run build:mp-weixin
npm run build:app-android
# ... other platforms
```

**Backend:**
```bash
# Deploy to Cloudflare Workers
cd yigui-backend
npm run deploy
```

## Project Structure

```
├── src/                          # Frontend source code
│   ├── pages/                    # Application pages
│   ├── components/               # Reusable Vue components
│   ├── stores/                   # Pinia state management
│   ├── api/                      # API service layer
│   ├── utils/                    # Utility functions
│   └── constants/                # Application constants
├── yigui-backend/                # Backend source code
│   ├── src/                      # Worker source code
│   │   ├── handlers/             # API route handlers
│   │   ├── models/               # Data models
│   │   ├── middleware/           # Request middleware
│   │   └── utils/                # Backend utilities
│   ├── schema.sql                # Database schema
│   └── wrangler.toml             # Cloudflare Workers config
├── static/                       # Static assets
├── package.json                  # Frontend dependencies
└── README.md                     # This file
```

## Technology Stack

### Frontend
- **Framework**: uni-app (Vue 3 + Composition API)
- **State Management**: Pinia
- **UI Components**: uni-app built-in components + custom components
- **Build Tool**: Vite
- **Language**: JavaScript/TypeScript
- **Styling**: SCSS

### Backend
- **Runtime**: Cloudflare Workers
- **Router**: itty-router
- **Database**: Cloudflare D1 (SQLite)
- **File Storage**: Cloudflare R2
- **Authentication**: JWT with @tsndr/cloudflare-worker-jwt
- **Validation**: Zod

### Development Tools
- **Linting**: ESLint with Vue and TypeScript support
- **Formatting**: Prettier
- **Testing**: Vitest (backend)
- **Type Checking**: TypeScript

## Available Scripts

### Frontend Scripts
```bash
npm run dev:h5              # Start H5 development server
npm run dev:mp-weixin       # Start WeChat mini-program development
npm run build:h5            # Build for H5 production
npm run build:mp-weixin     # Build for WeChat mini-program
npm run type-check          # Run TypeScript type checking
npm run lint                # Run ESLint
npm run lint:fix            # Fix ESLint issues automatically
npm run format              # Format code with Prettier
npm run format:check        # Check code formatting
```

### Backend Scripts
```bash
cd yigui-backend
npm run dev                 # Start local development server
npm run deploy              # Deploy to Cloudflare Workers
npm run test                # Run tests
npm run test:watch          # Run tests in watch mode
```

## Development Guidelines

- Follow the existing code style enforced by ESLint and Prettier
- Use TypeScript for type safety where applicable
- Write tests for new backend functionality
- Follow uni-app best practices for cross-platform compatibility
- Use Pinia stores for state management
- Keep components small and focused
- Document complex functions and components

## Contributing

Please read [CONTRIBUTING.md](./CONTRIBUTING.md) for details on our code of conduct and the process for submitting pull requests.

## Troubleshooting

### Common Issues

1. **Node.js Version**: Ensure you're using Node.js 16.x or higher
2. **HBuilderX Issues**: Try importing the project fresh or clearing the cache
3. **Build Errors**: Check that all dependencies are installed correctly
4. **API Connection**: Verify backend is running and endpoints are correct

### Getting Help

- Check the [uni-app documentation](https://uniapp.dcloud.net.cn/)
- Review [Cloudflare Workers documentation](https://developers.cloudflare.com/workers/)
- Open an issue in the project repository

## License

This project is licensed under the MIT License - see the LICENSE file for details.